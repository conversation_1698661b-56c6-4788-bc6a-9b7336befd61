<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue座位图组件测试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .event-log {
            max-height: 200px;
            overflow-y: auto;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <div class="header">
                <h1>🎯 Vue座位图组件实际测试</h1>
                <p>使用真实的SeatMapEngine.js和SeatMapViewer.vue</p>
            </div>
            
            <!-- Vue组件 -->
            <div>
                <seat-map-viewer 
                    :width="900" 
                    :height="500"
                    :initial-rows="12"
                    :initial-cols="18"
                    :show-controls="true"
                    :show-stats="true"
                    @seat-click="onSeatClick"
                    @seat-hover="onSeatHover"
                    @view-change="onViewChange"
                    @seats-generated="onSeatsGenerated"
                ></seat-map-viewer>
            </div>
            
            <!-- 事件日志 -->
            <div v-if="eventLogs.length > 0">
                <h3>事件日志</h3>
                <div class="event-log">
                    <div v-for="(log, index) in eventLogs" :key="index" style="margin-bottom: 5px;">
                        <span style="color: #666;">{{ log.time }}</span> - {{ log.message }}
                    </div>
                </div>
                <button @click="clearLogs" style="margin-top: 10px; padding: 5px 10px;">清空日志</button>
            </div>
        </div>
    </div>

    <script type="module">
        import { createApp } from 'https://unpkg.com/vue@3/dist/vue.esm-browser.js'
        
        // 临时的简化组件用于测试
        const SeatMapViewer = {
            template: `
                <div :style="{ width: width + 'px', height: height + 'px', border: '2px solid #333', borderRadius: '8px', background: '#f9f9f9', position: 'relative' }">
                    <canvas 
                        ref="canvas"
                        :width="width" 
                        :height="height"
                        @mousedown="onMouseDown"
                        @mousemove="onMouseMove"
                        @mouseup="onMouseUp"
                        @wheel="onWheel"
                        style="display: block; background: white; cursor: grab;"
                    ></canvas>
                    
                    <!-- 控制面板 -->
                    <div v-if="showControls" style="position: absolute; top: 10px; left: 10px; background: rgba(255,255,255,0.9); padding: 10px; border-radius: 6px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                        <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 8px;">
                            <button @click="zoomOut" style="padding: 4px 8px; border: 1px solid #ddd; background: white; border-radius: 4px; cursor: pointer;">-</button>
                            <button @click="resetZoom" style="padding: 4px 8px; border: 1px solid #ddd; background: white; border-radius: 4px; cursor: pointer;">⌂</button>
                            <button @click="zoomIn" style="padding: 4px 8px; border: 1px solid #ddd; background: white; border-radius: 4px; cursor: pointer;">+</button>
                            <span style="font-size: 12px; color: #666;">{{ Math.round(zoom * 100) }}%</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <label style="font-size: 12px; color: #666;">行数:</label>
                            <input v-model.number="rows" type="number" min="1" max="100" style="width: 60px; padding: 2px 4px; border: 1px solid #ddd; border-radius: 3px;" />
                            <label style="font-size: 12px; color: #666;">列数:</label>
                            <input v-model.number="cols" type="number" min="1" max="100" style="width: 60px; padding: 2px 4px; border: 1px solid #ddd; border-radius: 3px;" />
                            <button @click="generateSeats" style="padding: 4px 8px; border: 1px solid #ddd; background: white; border-radius: 4px; cursor: pointer;">生成座位</button>
                        </div>
                    </div>
                    
                    <!-- 统计信息 -->
                    <div v-if="showStats" style="position: absolute; bottom: 10px; right: 10px; background: rgba(0,0,0,0.8); color: white; padding: 8px; border-radius: 4px; font-size: 12px;">
                        <div>总座位: {{ totalSeats }}</div>
                        <div>缩放: {{ Math.round(zoom * 100) }}%</div>
                        <div>渲染: {{ renderTime }}ms</div>
                    </div>
                </div>
            `,
            props: {
                width: { type: Number, default: 800 },
                height: { type: Number, default: 600 },
                initialRows: { type: Number, default: 10 },
                initialCols: { type: Number, default: 10 },
                showControls: { type: Boolean, default: true },
                showStats: { type: Boolean, default: true }
            },
            data() {
                return {
                    rows: this.initialRows,
                    cols: this.initialCols,
                    zoom: 1,
                    totalSeats: 0,
                    renderTime: 0,
                    seats: [],
                    engine: null
                }
            },
            async mounted() {
                await this.initEngine()
                this.generateSeats()
            },
            methods: {
                async initEngine() {
                    try {
                        // 尝试加载SeatMapEngine
                        const module = await import('./SeatMapEngine.js')
                        const { SeatMapEngine } = module
                        
                        this.engine = new SeatMapEngine(this.$refs.canvas, {
                            seatSize: 20,
                            seatSpacing: 4,
                            onSeatClick: (seat) => {
                                this.$emit('seat-click', seat)
                            },
                            onSeatHover: (seat) => {
                                this.$emit('seat-hover', seat)
                            },
                            onViewChange: (view) => {
                                this.zoom = view.zoom || 1
                                this.renderTime = view.stats?.renderTime || 0
                                this.$emit('view-change', view)
                            }
                        })
                        
                        console.log('✅ SeatMapEngine loaded successfully')
                    } catch (error) {
                        console.warn('⚠️ Could not load SeatMapEngine, using fallback:', error)
                        this.initFallbackEngine()
                    }
                },
                initFallbackEngine() {
                    // 简单的fallback实现
                    const canvas = this.$refs.canvas
                    const ctx = canvas.getContext('2d')
                    
                    this.engine = {
                        generateSeats: (rows, cols) => {
                            const seats = []
                            for (let r = 0; r < rows; r++) {
                                for (let c = 0; c < cols; c++) {
                                    seats.push({
                                        id: `${r}-${c}`,
                                        row: r,
                                        col: c,
                                        number: `${String(r + 1).padStart(2, '0')}-${String(c + 1).padStart(2, '0')}`,
                                        status: 'available'
                                    })
                                }
                            }
                            this.drawSeats(seats)
                            return seats
                        },
                        zoomIn: () => { this.zoom = Math.min(this.zoom * 1.2, 3) },
                        zoomOut: () => { this.zoom = Math.max(this.zoom / 1.2, 0.3) },
                        resetZoom: () => { this.zoom = 1 }
                    }
                    
                    // 添加点击事件
                    canvas.addEventListener('click', (e) => {
                        const rect = canvas.getBoundingClientRect()
                        const x = e.clientX - rect.left
                        const y = e.clientY - rect.top
                        
                        // 简单的座位检测
                        const seatSize = 20
                        const spacing = 4
                        const col = Math.floor(x / (seatSize + spacing))
                        const row = Math.floor(y / (seatSize + spacing))
                        
                        if (row < this.rows && col < this.cols) {
                            const seat = {
                                id: `${row}-${col}`,
                                row: row,
                                col: col,
                                number: `${String(row + 1).padStart(2, '0')}-${String(col + 1).padStart(2, '0')}`
                            }
                            this.$emit('seat-click', seat)
                        }
                    })
                },
                drawSeats(seats) {
                    const canvas = this.$refs.canvas
                    const ctx = canvas.getContext('2d')
                    
                    ctx.clearRect(0, 0, canvas.width, canvas.height)
                    
                    const seatSize = 20
                    const spacing = 4
                    
                    seats.forEach(seat => {
                        const x = seat.col * (seatSize + spacing)
                        const y = seat.row * (seatSize + spacing)
                        
                        // 绘制座位
                        ctx.fillStyle = seat.status === 'selected' ? '#4CAF50' : '#E0E0E0'
                        ctx.fillRect(x, y, seatSize, seatSize)
                        
                        // 绘制边框
                        ctx.strokeStyle = '#999'
                        ctx.strokeRect(x, y, seatSize, seatSize)
                        
                        // 绘制座位号
                        ctx.fillStyle = '#333'
                        ctx.font = '10px Arial'
                        ctx.textAlign = 'center'
                        ctx.fillText(seat.number, x + seatSize/2, y + seatSize/2 + 3)
                    })
                },
                generateSeats() {
                    if (this.engine) {
                        const startTime = performance.now()
                        this.seats = this.engine.generateSeats(this.rows, this.cols)
                        this.totalSeats = this.seats.length
                        this.renderTime = Math.round(performance.now() - startTime)
                        this.$emit('seats-generated', { total: this.totalSeats, seats: this.seats })
                    }
                },
                zoomIn() { this.engine?.zoomIn() },
                zoomOut() { this.engine?.zoomOut() },
                resetZoom() { this.engine?.resetZoom() },
                onMouseDown(e) { this.engine?.handleMouseDown?.(e) },
                onMouseMove(e) { this.engine?.handleMouseMove?.(e) },
                onMouseUp(e) { this.engine?.handleMouseUp?.(e) },
                onWheel(e) { this.engine?.handleWheel?.(e) }
            }
        }
        
        createApp({
            components: {
                SeatMapViewer
            },
            data() {
                return {
                    eventLogs: []
                }
            },
            methods: {
                addLog(message) {
                    this.eventLogs.unshift({
                        time: new Date().toLocaleTimeString(),
                        message: message
                    })
                    if (this.eventLogs.length > 50) {
                        this.eventLogs.pop()
                    }
                },
                clearLogs() {
                    this.eventLogs = []
                },
                onSeatsGenerated(data) {
                    this.addLog(`✅ 座位生成完成: ${data.total} 个座位`)
                },
                onSeatClick(seat) {
                    this.addLog(`🖱️ 点击座位: ${seat.number || seat.id}`)
                },
                onSeatHover(seat) {
                    this.addLog(`👆 悬停座位: ${seat.number || seat.id}`)
                },
                onViewChange(view) {
                    this.addLog(`🔍 视图变化: 缩放 ${Math.round((view.zoom || 1) * 100)}%`)
                }
            }
        }).mount('#app')
    </script>
</body>
</html>
