!function(e){e.fn.selectSeat=function(){e(this).find("span").addClass("selected")},e.fn.unselectSeat=function(){e(this).find("span").removeClass("selected")},e.fn.hasSelected=function(){return e(this).find("span").hasClass("selected")};var t=document.scripts,s=t[t.length-1],a=s.src,c=a.substring(0,a.lastIndexOf("/")+1)+"css/seat.css";e("head:eq(0)").append('<link href="'+c+'" rel="stylesheet" type="text/css" />');var i=function(t){function s(){for(var s=e('<table class="seat-table animated-seat" cellpadding="0" cellspacing="0"></table>'),a=1;a<=t.rows;a++){for(var c=e("<tr></tr>"),r=1;r<=t.cols;r++){var o=a+"_"+r,h=e('<td data-row="'+a+'" data-col="'+r+'" id="'+o+'"></td>'),f=e('<span class="seat"></span>');f.css({width:t.size+"px",height:t.size+"px"});try{h.data("thre_id",i[o].thre_id),h.data("hall_id",i[o].hall_id),h.data("movie_id",i[o].movie_id),h.data("price",i[o].price),i[o].color&&(f.css({"border-color":i[o].color}),h.data("color",i[o].color))}catch(u){}h.on("click",function(){e(this).find("span").toggleClass("selected");var s=e(this).data("row"),a=e(this).data("col"),c=s+"_"+a;switch(t.step){case 1:e(this).hasSelected()?i[c]={row:s,col:a,price:0,color:"",thre_id:t.thre_id,hall_id:t.hall_id,movie_id:t.hall_id}:delete i[c];break;case 2:e(this).hasSelected()?n[c]=1:delete n[c],console.log(n);break;case 3:var r={row:e(this).data("row"),col:e(this).data("col"),thre_id:e(this).data("thre_id"),hall_id:e(this).data("hall_id"),movie_id:e(this).data("movie_id"),price:e(this).data("price")};if(e(this).hasSelected()){if(l>=t.maxTicketNumber)return e(this).unselectSeat(),void t.onerror("一次最多选择 "+t.maxTicketNumber+" 个座位");t.selected[c]=1,l++,t.onSelected(r)}else d.unselect(s,a),t.onUnselected(r)}}),1!=t.step&&void 0==i[o]&&(f.css({border:"none",background:"none"}),h.off("click")),3==t.step&&t.selected&&t.selected[o]&&(h.off("click"),f.addClass("disabled")),h.append(f),c.append(h),1==t.step&&h.trigger("click")}s.append(c)}e(t.box).css({width:t.cols*(t.size+1+4)+"px"}),e(t.box).append(s)}function a(e){var t=0;for(var s in e)t++;return t}var c={rows:20,cols:30,size:15,thre_id:0,hall_id:0,movie_id:0,step:1,onSelected:function(e){console.log(e)},onUnselected:function(e){console.log(e)},onerror:function(e){},maxTicketNumber:5,selected:{},datas:{}};t=e.extend(c,t);var i={},d={},l=0;a(t.datas)>0&&(i=t.datas);var n={};return d.getSeats=function(){return i},d.setPrice=function(t,s){if(0==n.length)return alert("你没有选中任何座位");for(var a in n)i[a].color=s,i[a].price=t,e("#"+a).find("span").css({"border-color":s}).removeClass("selected");n={}},d.getSelectedSeats=function(){return t.selected},d.unselect=function(s,a){var c=s+"_"+a;delete t.selected[c],e("#"+c).unselectSeat(),l--},s(),d};e.seats=function(e){return new i(e)}}(jQuery);