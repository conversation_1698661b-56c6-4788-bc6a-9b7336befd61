<!DOCTYPE html>
<html lang="ch">
<head>
    <title>设置分区 - Canvas版</title>
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="renderer" content="webkit">
    <meta charset="UTF-8">
    <link rel="stylesheet" type="text/css" href="css/reset.css">
    <link rel="stylesheet" href="css/translateCommon.css">
    <link rel="stylesheet" href="layui/css/layui.css" />
    <link rel="stylesheet" href="css/stepPlug.css" />
    <link rel="stylesheet" href="css/seat3.css" />
    <link rel="stylesheet" href="css/step2only.css" />
    <style>
        /* Canvas容器样式 - 全屏布局 */
        #seatBox {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100vw;
            height: 100vh;
            border: none;
            overflow: hidden;
            background: white;
            z-index: 1;
        }

        /* 确保其他内容不干扰 */
        #section, #content, .main, .main-content, .main-html {
            position: relative;
            z-index: 0;
        }
        
        #seatCanvas {
            display: block;
            cursor: crosshair;
            background: white;
        }
        
        /* 行列标尺样式 */
        .row-headers, .col-headers {
            position: absolute;
            background: #f5f5f5;
            border: 1px solid #ddd;
            z-index: 10;
        }
        
        .row-headers {
            left: 0;
            top: 30px;
            width: 30px;
            height: calc(100% - 30px);
            overflow: hidden;
        }
        
        .col-headers {
            left: 30px;
            top: 0;
            width: calc(100% - 30px);
            height: 30px;
            overflow: hidden;
        }
        
        .header-item {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #666;
            cursor: pointer;
            border-bottom: 1px solid #ddd;
            border-right: 1px solid #ddd;
        }
        
        .header-item:hover {
            background: #e0e0e0;
        }
        
        /* Canvas容器 */
        .canvas-container {
            position: absolute;
            left: 30px;
            top: 30px;
            width: calc(100% - 30px);
            height: calc(100% - 30px);
            overflow: auto;
        }
        
        /* 拖拽选择框 */
        .selection-box {
            position: absolute;
            border: 2px dashed #4CAF50;
            background: rgba(76, 175, 80, 0.1);
            pointer-events: none;
            z-index: 5;
        }
        
        /* 缩放控制 */
        .zoom-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.9);
            padding: 5px;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            z-index: 20;
        }
        
        .zoom-btn {
            padding: 4px 8px;
            margin: 0 2px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 3px;
        }
        
        .zoom-btn:hover {
            background: #f0f0f0;
        }
        
        /* 座位统计 */
        .seat-stats {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 20;
        }

        /* 右上角导航按钮 */
        .top-nav-buttons {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 30;
            display: flex;
            gap: 10px;
        }

        .nav-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .nav-btn-secondary {
            background: #6B7280;
            color: white;
        }

        .nav-btn-secondary:hover {
            background: #4B5563;
        }

        .nav-btn-primary {
            background: #3B82F6;
            color: white;
        }

        .nav-btn-primary:hover {
            background: #2563EB;
        }

        /* 隐藏原有的页面结构 */
        #header {
            display: none;
        }

        /* 隐藏不需要的内容，但保留座位图 */
        .step-html, .seat-setting-tips {
            display: none;
        }

        /* 分区工具栏 */
        .zone-toolbar {
            position: fixed;
            top: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.95);
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 25;
            min-width: 280px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }

        .zone-toolbar h3 {
            margin: 0 0 15px 0;
            font-size: 16px;
            color: #333;
            border-bottom: 2px solid #3B82F6;
            padding-bottom: 8px;
        }

        .zone-section {
            margin-bottom: 20px;
        }

        .zone-section-title {
            font-size: 14px;
            font-weight: bold;
            color: #555;
            margin-bottom: 10px;
        }

        .zone-btn-group {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .zone-btn {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
        }

        .zone-btn:hover {
            background: #f0f0f0;
            border-color: #3B82F6;
        }

        .zone-btn.active {
            background: #3B82F6;
            color: white;
            border-color: #3B82F6;
        }

        .zone-form {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 6px;
            margin-top: 10px;
        }

        .zone-form-group {
            margin-bottom: 12px;
        }

        .zone-form-group label {
            display: block;
            font-size: 12px;
            color: #555;
            margin-bottom: 4px;
        }

        .zone-form-group input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 12px;
        }

        .zone-list {
            max-height: 200px;
            overflow-y: auto;
        }

        .zone-item {
            background: white;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
            padding: 8px;
            margin-bottom: 6px;
            font-size: 11px;
        }

        .zone-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }

        .zone-item-info {
            color: #666;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .zone-color-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 2px;
            vertical-align: middle;
        }

        .zone-delete-btn {
            background: #ff4d4f;
            color: white;
            border: none;
            border-radius: 2px;
            padding: 2px 6px;
            cursor: pointer;
            font-size: 10px;
        }

        .zone-info-display {
            background: #f0f8ff;
            padding: 10px;
            border-radius: 6px;
            font-size: 12px;
        }

        .zone-info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
        }

        .zone-info-label {
            color: #666;
        }

        .zone-info-value {
            font-weight: bold;
            color: #333;
        }
    </style>
</head>
<body>
    <!-- 右上角导航按钮 -->
    <div class="top-nav-buttons">
        <button type="button" class="nav-btn nav-btn-secondary" onclick="location.href='step2-canvas-perfect.html'">上一步</button>
        <button type="button" class="nav-btn nav-btn-primary" id="saveZones">保存分区设置</button>
    </div>

    <!-- 分区工具栏 -->
    <div class="zone-toolbar">
        <h3>🎨 分区设置</h3>
        
        <!-- 工具选择 -->
        <div class="zone-section">
            <div class="zone-section-title">选择工具</div>
            <div class="zone-btn-group">
                <button class="zone-btn active" id="selectTool" data-tool="select">选择座位</button>
                <button class="zone-btn" id="boxSelectTool" data-tool="boxselect">框选座位</button>
            </div>
        </div>

        <!-- 分区操作 -->
        <div class="zone-section">
            <div class="zone-section-title">分区操作</div>
            <div class="zone-btn-group">
                <button class="zone-btn" id="createZoneBtn">创建分区</button>
                <button class="zone-btn" id="clearZonesBtn">清除所有分区</button>
            </div>
        </div>

        <!-- 信息显示 -->
        <div class="zone-section">
            <div class="zone-section-title">当前状态</div>
            <div class="zone-info-display">
                <div class="zone-info-row">
                    <span class="zone-info-label">已选座位:</span>
                    <span class="zone-info-value" id="selectedCount">0</span>
                </div>
                <div class="zone-info-row">
                    <span class="zone-info-label">总座位数:</span>
                    <span class="zone-info-value" id="totalSeats">0</span>
                </div>
                <div class="zone-info-row">
                    <span class="zone-info-label">分区数量:</span>
                    <span class="zone-info-value" id="zoneCount">0</span>
                </div>
            </div>
        </div>

        <!-- 分区表单 -->
        <div class="zone-section" id="zoneFormSection" style="display: none;">
            <div class="zone-section-title">创建新分区</div>
            <div class="zone-form">
                <div class="zone-form-group">
                    <label for="zoneName">分区名称</label>
                    <input type="text" id="zoneName" placeholder="分区1" title="输入分区名称" />
                </div>
                <div class="zone-form-group">
                    <label for="zoneColor">分区颜色</label>
                    <input type="color" id="zoneColor" value="#1890ff" title="选择分区颜色" />
                </div>
                <div class="zone-btn-group">
                    <button class="zone-btn" onclick="createZone()" style="background: #52c41a; color: white;">创建</button>
                    <button class="zone-btn" onclick="cancelZone()">取消</button>
                </div>
            </div>
        </div>

        <!-- 分区列表 -->
        <div class="zone-section">
            <div class="zone-section-title">已创建分区</div>
            <div class="zone-list" id="zoneList">
                <p style="color: #999; text-align: center; font-size: 12px;">暂无分区</p>
            </div>
        </div>
    </div>

    <div id="section">
        <!-- header -->
        <div id="header">
            <div class="hui">
                <img src="images/hui.jpg" alt="">
            </div>
            <div class="logo">
                <h1 class="f_left">
                    <a href="#">
                        <img src="images/logo.png" alt=""></a>
                </h1>
                <div class="f_right logo_r">
                    <ul class="logo_r_show">
                        <li class="translate-box">
                            <a class="translate-box-cn" href="javascript:translate.changeLanguage('english');javascript:void(0);">
                                <img src="images/translateEn.png" alt="">
                            </a>
                            <a class="translate-box-en" style="display: none;"
                                href="javascript:translate.changeLanguage('chinese_simplified');javascript:void(0);">
                                <img src="images/translateCn.png" alt="">
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <div id="content" class="clearfix">
            <div class="c_right">
                <div class="main">
                    <div class="main-content">
                        <div class="main-html">
                            <!-- 步骤条 -->
                            <div class="step-html" style="width: 600px;">
                                <div class="step-box">
                                    <div class="step-list step-act">
                                        <span class="step-number">1</span>
                                    </div>
                                    <div class="step-list-html-text step-act">设置场地排列</div>
                                    <div class="step-line"></div>
                                    <div class="step-list step-act">
                                        <span class="step-number">2</span>
                                    </div>
                                    <div class="step-list-html-text step-act">设置场地布局</div>
                                    <div class="step-line"></div>
                                    <div class="step-list step-act">
                                        <span class="step-number">3</span>
                                    </div>
                                    <div class="step-list-html-text step-act">设置分区</div>
                                </div>
                            </div>

                            <div class="seat-setting-tips">
                                *请选择座位并设置分区信息
                                <span id="seatTotal"></span>
                            </div>

                            <!-- 座位图容器 -->
                            <div id="seatBox">
                                <!-- 行标尺 -->
                                <div class="row-headers" id="rowHeaders"></div>
                                <!-- 列标尺 -->
                                <div class="col-headers" id="colHeaders"></div>
                                <!-- Canvas容器 -->
                                <div class="canvas-container" id="canvasContainer">
                                    <canvas id="seatCanvas"></canvas>
                                </div>
                                <!-- 缩放控制 -->
                                <div class="zoom-controls">
                                    <button class="zoom-btn" onclick="zoomOut()">-</button>
                                    <span id="zoomDisplay">100%</span>
                                    <button class="zoom-btn" onclick="zoomIn()">+</button>
                                    <button class="zoom-btn" onclick="resetZoom()">重置</button>
                                </div>
                                <!-- 座位统计 -->
                                <div class="seat-stats">
                                    <div>总座位: <span id="totalSeatsDisplay">0</span></div>
                                    <div>已选: <span id="selectedSeatsDisplay">0</span></div>
                                    <div>分区: <span id="zoneCountDisplay">0</span></div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载遮罩 -->
    <div class="loading-module" style="display: none;">
        <div class="loading-content">
            <div class="loading-icon"></div>
            <div class="loading-text">正在生成座位图...</div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/jquery-3.6.3.min.js"></script>
    <script src="js/index.js"></script>
    <script src="layui/layui.js"></script>
    <script src="SeatMapEngine.js"></script>

    <script>
        // 全局变量
        let engine = null;
        let seatsData = {};
        let selectedSeats = new Set();
        let zones = [];
        let currentTool = 'select';

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
        });

        // 初始化页面
        function initializePage() {
            // 获取座位配置
            const seatRows = localStorage.getItem("seatRows") || "8";
            const seatCols = localStorage.getItem("seatCols") || "12";

            // 创建座位数据（总是重新创建以确保完整性）
            const testSeats = {};
            for (let row = 0; row < parseInt(seatRows); row++) {
                for (let col = 0; col < parseInt(seatCols); col++) {
                    testSeats[row + "_" + col] = {
                        row: row,
                        col: col
                    };
                }
            }
            seatsData = testSeats;

            // 加载已有分区数据（如果存在）
            const savedSeats = localStorage.getItem("seats");
            if (savedSeats) {
                try {
                    const savedData = JSON.parse(savedSeats);
                    // 合并已有的分区信息到新创建的座位数据中
                    Object.keys(savedData).forEach(seatKey => {
                        if (seatsData[seatKey] && savedData[seatKey].zoneName) {
                            seatsData[seatKey].zoneName = savedData[seatKey].zoneName;
                            seatsData[seatKey].color = savedData[seatKey].color;
                        }
                    });
                } catch (e) {
                    console.log('Failed to load saved seat data:', e);
                }
            }

            // 初始化Canvas
            initializeCanvas();

            // 设置事件监听器
            setupEventListeners();

            // 更新界面
            updateInfo();
            updateZoneList();
        }

        // 初始化Canvas
        function initializeCanvas() {
            // 使用已存在的Canvas元素
            const canvas = document.getElementById('seatCanvas');
            const container = document.querySelector("#canvasContainer");

            // 获取座位配置
            const seatRows = parseInt(localStorage.getItem("seatRows") || "8");
            const seatCols = parseInt(localStorage.getItem("seatCols") || "12");

            // 设置Canvas尺寸（根据座位数量动态计算）
            const seatSize = 20;
            const seatSpacing = 4;
            const canvasWidth = Math.max(800, seatCols * (seatSize + seatSpacing) + 100);
            const canvasHeight = Math.max(600, seatRows * (seatSize + seatSpacing) + 100);

            canvas.width = canvasWidth;
            canvas.height = canvasHeight;
            canvas.style.width = canvasWidth + 'px';
            canvas.style.height = canvasHeight + 'px';
            canvas.style.display = 'block';
            canvas.style.position = 'absolute';
            canvas.style.top = '0';
            canvas.style.left = '0';

            // 创建引擎实例
            engine = new SeatMapEngine(canvas, {
                seatSize: 20,
                seatSpacing: 4,
                showSeatNumbers: true,
                enableRoundedCorners: true,
                showHeaders: true,
                onSeatClick: handleSeatClick,
                onSeatHover: function(seat) {
                    // 座位悬停处理（可选）
                },
                onViewChange: function(viewInfo) {
                    // 视图变化处理（可选）
                    updateZoomDisplay();
                }
            });

            // 设置座位数据
            const seats = [];
            for (let row = 0; row < seatRows; row++) {
                for (let col = 0; col < seatCols; col++) {
                    const seatKey = row + "_" + col;
                    const seat = {
                        id: row + '-' + col,
                        row: row,
                        col: col,
                        x: col * (seatSize + seatSpacing),
                        y: row * (seatSize + seatSpacing),
                        width: seatSize,
                        height: seatSize,
                        status: 'available'
                    };

                    // 如果座位有分区信息，设置颜色
                    if (seatsData[seatKey] && seatsData[seatKey].color) {
                        seat.groupColor = seatsData[seatKey].color;
                        seat.groupName = seatsData[seatKey].zoneName;
                    }

                    seats.push(seat);
                }
            }

            engine.loadSeats(seats);

            // 初始化界面
            updateInfo();
            updateZoneList();
        }

        // 座位点击处理（统一处理点击和框选）
        function handleSeatClick(seat) {
            // 只在选择模式下处理座位点击（包括select和boxselect）
            if (currentTool !== 'select' && currentTool !== 'boxselect') return;

            const seatKey = seat.row + "_" + seat.col;
            if (!seatsData[seatKey]) return;

            if (currentTool === 'select') {
                // 点击选择模式：切换座位状态
                if (selectedSeats.has(seatKey)) {
                    selectedSeats.delete(seatKey);
                    if (seatsData[seatKey].color) {
                        seat.groupColor = seatsData[seatKey].color;
                        seat.status = 'available';
                    } else {
                        seat.groupColor = null;
                        seat.status = 'available';
                    }
                } else {
                    selectedSeats.add(seatKey);
                    seat.status = 'selected';
                }
            } else if (currentTool === 'boxselect') {
                // 框选模式：只添加到选择中，不取消已选中的座位
                if (!selectedSeats.has(seatKey)) {
                    selectedSeats.add(seatKey);
                    seat.status = 'selected';
                }
                // 如果座位已经选中，保持选中状态，不做任何操作
            }

            engine.render();
            updateInfo();
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 工具切换
            document.querySelectorAll('.zone-btn[data-tool]').forEach(btn => {
                btn.addEventListener('click', function() {
                    document.querySelectorAll('.zone-btn[data-tool]').forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    currentTool = this.dataset.tool;
                });
            });

            // 操作按钮
            document.getElementById('createZoneBtn').addEventListener('click', showZoneForm);
            document.getElementById('clearZonesBtn').addEventListener('click', clearAllZones);
            document.getElementById('saveZones').addEventListener('click', saveZones);
        }

        // 更新信息显示
        function updateInfo() {
            const totalSeats = Object.keys(seatsData).length;
            const selectedCount = selectedSeats.size;

            document.getElementById('selectedCount').textContent = selectedCount;
            document.getElementById('totalSeats').textContent = totalSeats;
            document.getElementById('zoneCount').textContent = zones.length;

            // 更新右下角统计
            document.getElementById('totalSeatsDisplay').textContent = totalSeats;
            document.getElementById('selectedSeatsDisplay').textContent = selectedCount;
            document.getElementById('zoneCountDisplay').textContent = zones.length;
        }

        // 显示分区表单
        function showZoneForm() {
            if (selectedSeats.size === 0) {
                layui.use('layer', function(){
                    const layer = layui.layer;
                    layer.msg('请先选择要分区的座位！', {icon: 2});
                });
                return;
            }

            // 显示分区表单
            document.getElementById('zoneFormSection').style.display = 'block';

            // 自动设置默认分区名称（按顺序）
            const defaultName = `分区${zones.length + 1}`;
            document.getElementById('zoneName').value = defaultName;

            // 自动设置随机颜色
            document.getElementById('zoneColor').value = getRandomZoneColor();

            // 聚焦到名称输入框，方便用户修改
            document.getElementById('zoneName').focus();
            document.getElementById('zoneName').select(); // 选中文本，方便用户直接输入新名称
        }

        // 取消分区
        function cancelZone() {
            document.getElementById('zoneFormSection').style.display = 'none';
            document.getElementById('zoneName').value = '';
            document.getElementById('zoneColor').value = '#1890ff';
        }

        // 创建分区
        function createZone() {
            const name = document.getElementById('zoneName').value.trim();
            const color = document.getElementById('zoneColor').value;

            if (!name) {
                layui.use('layer', function(){
                    const layer = layui.layer;
                    layer.msg('请输入分区名称！', {icon: 2});
                });
                document.getElementById('zoneName').focus();
                return;
            }

            // 检查分区名称是否重复
            if (zones.some(zone => zone.name === name)) {
                layui.use('layer', function(){
                    const layer = layui.layer;
                    layer.msg('分区名称已存在，请使用其他名称！', {icon: 2});
                });
                document.getElementById('zoneName').focus();
                return;
            }

            // 创建新分区
            const zone = {
                id: Date.now().toString(),
                name: name,
                color: color,
                seats: Array.from(selectedSeats)
            };

            zones.push(zone);

            // 更新座位数据
            selectedSeats.forEach(seatKey => {
                if (seatsData[seatKey]) {
                    seatsData[seatKey].zoneName = name;
                    seatsData[seatKey].color = color;
                    // 移除价格字段
                    delete seatsData[seatKey].price;
                }
            });

            // 更新座位颜色
            engine.seats.forEach(seat => {
                const seatKey = seat.row + "_" + seat.col;
                if (selectedSeats.has(seatKey)) {
                    seat.groupColor = color;
                    seat.groupName = name;
                    seat.status = 'available';
                }
            });

            selectedSeats.clear();
            updateInfo();
            updateZoneList();
            cancelZone();

            // 强制刷新显示 - 重新设置Canvas来触发重绘
            setTimeout(() => {
                // 重新设置Canvas尺寸强制刷新
                const canvas = engine.canvas;
                const currentWidth = canvas.width;
                const currentHeight = canvas.height;
                canvas.width = currentWidth;
                canvas.height = currentHeight;

                // 重新设置设备像素比缩放
                const dpr = window.devicePixelRatio || 1;
                engine.ctx.scale(dpr, dpr);

                // 调用渲染
                engine.render();
            }, 50);

            layui.use('layer', function(){
                const layer = layui.layer;
                layer.msg(`分区"${name}"创建成功！`, {icon: 1});
            });
        }

        // 更新分区列表
        function updateZoneList() {
            const zoneList = document.getElementById('zoneList');

            if (zones.length === 0) {
                zoneList.innerHTML = '<p style="color: #999; text-align: center; font-size: 12px;">暂无分区</p>';
                return;
            }

            let html = '';
            zones.forEach(zone => {
                html += `
                    <div class="zone-item">
                        <div class="zone-item-header">
                            <strong>${zone.name}</strong>
                            <button class="zone-delete-btn" onclick="deleteZone('${zone.id}')">删除</button>
                        </div>
                        <div class="zone-item-info">
                            <span>座位：${zone.seats.length}个</span>
                            <span class="zone-color-indicator" style="background-color: ${zone.color}"></span>
                        </div>
                    </div>
                `;
            });

            zoneList.innerHTML = html;
        }

        // 删除分区
        function deleteZone(zoneId) {
            layui.use('layer', function(){
                const layer = layui.layer;
                const zone = zones.find(z => z.id === zoneId);
                if (!zone) return;

                layer.confirm(`确定要删除分区"${zone.name}"吗？`, {
                    btn: ['确定删除', '取消']
                }, function(index){
                    layer.close(index);

                    // 从分区列表中移除
                    zones = zones.filter(z => z.id !== zoneId);

                    // 清除座位的分区信息
                    zone.seats.forEach(seatKey => {
                        if (seatsData[seatKey]) {
                            delete seatsData[seatKey].zoneName;
                            delete seatsData[seatKey].color;
                        }
                    });

                    // 更新座位颜色
                    engine.seats.forEach(seat => {
                        const seatKey = seat.row + "_" + seat.col;
                        if (zone.seats.includes(seatKey)) {
                            seat.groupColor = null;
                            seat.groupName = null;
                        }
                    });

                    // 更新界面
                    updateInfo();
                    updateZoneList();
                    engine.render();

                    layer.msg('分区已删除', {icon: 1});
                });
            });
        }

        // 随机分配分区颜色
        function getRandomZoneColor() {
            const presetColors = [
                '#1890ff', '#52c41a', '#faad14', '#f5222d',
                '#722ed1', '#13c2c2', '#eb2f96', '#fa541c'
            ];
            const usedColors = zones.map(zone => zone.color);
            const availableColors = presetColors.filter(color => !usedColors.includes(color));

            if (availableColors.length > 0) {
                return availableColors[Math.floor(Math.random() * availableColors.length)];
            } else {
                // 如果预设颜色都用完了，生成随机颜色
                return '#' + Math.floor(Math.random()*16777215).toString(16);
            }
        }

        // 清除所有分区
        function clearAllZones() {
            if (zones.length === 0) {
                layui.use('layer', function(){
                    const layer = layui.layer;
                    layer.msg('没有分区可清除！', {icon: 2});
                });
                return;
            }

            layui.use('layer', function(){
                const layer = layui.layer;
                layer.confirm(`确定要清除所有 ${zones.length} 个分区吗？`, {
                    btn: ['确定清除', '取消']
                }, function(index){
                    layer.close(index);

                    // 清除所有分区
                    zones = [];

                    // 清除所有座位的分区信息
                    Object.keys(seatsData).forEach(seatKey => {
                        delete seatsData[seatKey].zoneName;
                        delete seatsData[seatKey].color;
                    });

                    // 清除所有座位的分区信息
                    engine.seats.forEach(seat => {
                        seat.groupColor = null;
                        seat.groupName = null;
                    });

                    // 更新界面
                    updateInfo();
                    updateZoneList();
                    engine.render();

                    layer.msg('所有分区已清除', {icon: 1});
                });
            });
        }

        // 保存分区设置
        function saveZones() {
            // 保存座位数据到localStorage
            localStorage.setItem("seats", JSON.stringify(seatsData));

            layui.use('layer', function(){
                const layer = layui.layer;
                layer.msg('分区设置已保存！', {icon: 1});
            });
        }

        // 缩放控制函数
        function zoomIn() {
            if (engine) {
                engine.state.zoom = Math.min(engine.state.zoom * 1.2, 3);
                engine.render();
                updateZoomDisplay();
            }
        }

        function zoomOut() {
            if (engine) {
                engine.state.zoom = Math.max(engine.state.zoom / 1.2, 0.3);
                engine.render();
                updateZoomDisplay();
            }
        }

        function resetZoom() {
            if (engine) {
                engine.state.zoom = 1;
                engine.state.offsetX = 0;
                engine.state.offsetY = 0;
                engine.render();
                updateZoomDisplay();
            }
        }

        function updateZoomDisplay() {
            if (engine) {
                document.getElementById('zoomDisplay').textContent = Math.round(engine.state.zoom * 100) + '%';
            }
        }
    </script>
</body>
</html>
