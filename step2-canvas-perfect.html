<!DOCTYPE html>
<html lang="ch">
<head>
    <title>设置座位 - Canvas完美版</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="renderer" content="webkit">
    <meta charset="UTF-8">
    <link rel="stylesheet" type="text/css" href="css/reset.css">
    <link rel="stylesheet" href="css/translateCommon.css">
    <link rel="stylesheet" href="layui/css/layui.css" />
    <link rel="stylesheet" href="css/stepPlug.css" />
    <link rel="stylesheet" href="css/seat3.css" />
    <link rel="stylesheet" href="css/step2only.css" />
    <style>
        /* Canvas容器样式 - 全屏布局 */
        #seatBox {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            width: 100vw;
            height: 100vh;
            border: none;
            overflow: hidden;
            background: white;
            z-index: 1;
        }

        /* 确保其他内容不干扰 */
        #section, #content, .main, .main-content, .main-html {
            position: relative;
            z-index: 0;
        }
        
        #seatCanvas {
            display: block;
            cursor: crosshair;
            background: white;
        }
        
        /* 行列标尺样式 */
        .row-headers, .col-headers {
            position: absolute;
            background: #f5f5f5;
            border: 1px solid #ddd;
            z-index: 10;
        }
        
        .row-headers {
            left: 0;
            top: 30px;
            width: 30px;
            height: calc(100% - 30px);
            overflow: hidden;
        }
        
        .col-headers {
            left: 30px;
            top: 0;
            width: calc(100% - 30px);
            height: 30px;
            overflow: hidden;
        }
        
        .header-item {
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #666;
            cursor: pointer;
            border-bottom: 1px solid #ddd;
            border-right: 1px solid #ddd;
        }
        
        .header-item:hover {
            background: #e0e0e0;
        }
        
        /* Canvas容器 */
        .canvas-container {
            position: absolute;
            left: 30px;
            top: 30px;
            width: calc(100% - 30px);
            height: calc(100% - 30px);
            overflow: auto;
        }
        
        /* 拖拽选择框 */
        .selection-box {
            position: absolute;
            border: 2px dashed #4CAF50;
            background: rgba(76, 175, 80, 0.1);
            pointer-events: none;
            z-index: 5;
        }
        
        /* 缩放控制 */
        .zoom-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.9);
            padding: 5px;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            z-index: 20;
        }
        
        .zoom-btn {
            padding: 4px 8px;
            margin: 0 2px;
            border: 1px solid #ddd;
            background: white;
            cursor: pointer;
            border-radius: 3px;
        }
        
        .zoom-btn:hover {
            background: #f0f0f0;
        }
        
        /* 座位统计 */
        .seat-stats {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 20;
        }

        /* 右上角导航按钮 */
        .top-nav-buttons {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 30;
            display: flex;
            gap: 10px;
        }

        .nav-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .nav-btn-secondary {
            background: #6B7280;
            color: white;
        }

        .nav-btn-secondary:hover {
            background: #4B5563;
        }

        .nav-btn-primary {
            background: #3B82F6;
            color: white;
        }

        .nav-btn-primary:hover {
            background: #2563EB;
        }

        /* 隐藏原有的页面结构 */
        #header {
            display: none;
        }

        /* 隐藏不需要的内容，但保留座位图 */
        .step-html, .seat-setting-tips {
            display: none;
        }
    </style>
</head>
<body>
    <!-- 右上角导航按钮 -->
    <div class="top-nav-buttons">
        <button type="button" class="nav-btn nav-btn-secondary" onclick="location.href='step1.html'">上一步</button>
        <button type="button" class="nav-btn nav-btn-primary" id="save">下一步</button>
    </div>

    <div id="section">
        <!-- header -->
        <div id="header">
            <div class="hui">
                <img src="images/hui.jpg" alt="">
            </div>
            <div class="logo">
                <h1 class="f_left">
                    <a href="#">
                        <img src="images/logo.png" alt=""></a>
                </h1>
                <div class="f_right logo_r">
                    <ul class="logo_r_show">
                        <li class="translate-box">
                            <a class="translate-box-cn" href="javascript:translate.changeLanguage('english');javascript:void(0);">
                                <img src="images/translateEn.png" alt="">
                            </a>
                            <a class="translate-box-en" style="display: none;"
                                href="javascript:translate.changeLanguage('chinese_simplified');javascript:void(0);">
                                <img src="images/translateCn.png" alt="">
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div id="content" class="clearfix">
            <div class="c_right">
                <div class="main">
                    <div class="main-content">
                        <div class="main-html">
                            <!-- 步骤条 -->
                            <div class="step-html" style="width: 600px;">
                                <div class="step-box">
                                    <div class="step-list step-act">
                                        <span class="step-number">1</span>
                                    </div>
                                    <div class="step-list-html-text step-act">设置场地排列</div>
                                    <div class="step-line"></div>
                                    <div class="step-list step-act">
                                        <span class="step-number">2</span>
                                    </div>
                                    <div class="step-list-html-text step-act">设置场地布局</div>
                                    <div class="step-line"></div>
                                    <div class="step-list">
                                        <span class="step-number">3</span>
                                    </div>
                                    <div class="step-list-html-text">设置分区</div>
                                </div>
                            </div>
                            
                            <div class="seat-setting-tips">
                                *请输入您需要的场地行数和列数，生成初始的场地图
                                <span id="seatTotal"></span>
                            </div>
                            
                            <!-- 座位图容器 -->
                            <div id="seatBox">
                                <!-- 行标尺 -->
                                <div class="row-headers" id="rowHeaders"></div>
                                <!-- 列标尺 -->
                                <div class="col-headers" id="colHeaders"></div>
                                <!-- Canvas容器 -->
                                <div class="canvas-container" id="canvasContainer">
                                    <canvas id="seatCanvas"></canvas>
                                </div>
                                <!-- 缩放控制 -->
                                <div class="zoom-controls">
                                    <button class="zoom-btn" onclick="zoomOut()">-</button>
                                    <span id="zoomDisplay">100%</span>
                                    <button class="zoom-btn" onclick="zoomIn()">+</button>
                                    <button class="zoom-btn" onclick="resetZoom()">重置</button>
                                </div>
                                <!-- 座位统计 -->
                                <div class="seat-stats">
                                    <div>总座位: <span id="totalSeats">0</span></div>
                                    <div>已选: <span id="selectedSeats">0</span></div>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载遮罩 -->
    <div class="loading-module" style="display: none;">
        <div class="loading-content">
            <div class="loading-icon"></div>
            <div class="loading-text">正在生成座位图...</div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="js/jquery-3.6.3.min.js"></script>
    <script src="js/index.js"></script>
    <script src="layui/layui.js"></script>
    <script>
        layui.use(function() {
            var element = layui.element;
            element.on('tab(tab-hash)', function(obj) {
                location.hash = hashName + '=' + this.getAttribute('lay-id');
            });
        });
    </script>

    <!-- 核心Canvas座位系统 -->
    <script>
        // 全局变量
        let seatRows, seatCols;
        let canvas, ctx;
        let seats = {}; // 座位数据，与原版格式完全一致
        let seatSize = 20;
        let seatSpacing = 4;
        let zoom = 1;
        let offsetX = 0, offsetY = 0;
        let isDragging = false;
        let isSelecting = false;
        let selectionStart = { x: 0, y: 0 };
        let selectionEnd = { x: 0, y: 0 };
        let selectionBox = null;

        // 初始化
        $(document).ready(function() {
            // 检查localStorage数据
            seatRows = parseInt(localStorage.getItem("seatRows"));
            seatCols = parseInt(localStorage.getItem("seatCols"));

            if (!seatRows || !seatCols) {
                location.href = "step1.html";
                return;
            }

            // 加载已有座位数据
            try {
                const savedSeats = localStorage.getItem("seats");
                if (savedSeats) {
                    seats = JSON.parse(savedSeats);
                }
            } catch (e) {
                console.warn("Failed to load saved seats:", e);
                seats = {};
            }

            // 初始化Canvas
            initCanvas();

            // 创建行列标尺
            createHeaders();

            // 渲染座位
            renderSeats();

            // 更新统计
            updateStats();

            // 绑定保存按钮
            $('#save').on('click', function() {
                console.log("保存座位数据:", seats);
                localStorage.setItem("seats", JSON.stringify(seats));
                localStorage.removeItem("groupArrayObject");
                localStorage.removeItem("selected");
                localStorage.removeItem("channelSelectArrayStorage");
                location.href = "step3.html";
            });
        });

        // 初始化Canvas
        function initCanvas() {
            canvas = document.getElementById('seatCanvas');
            ctx = canvas.getContext('2d');

            // 设置Canvas尺寸
            const container = document.getElementById('canvasContainer');
            const canvasWidth = Math.max(800, seatCols * (seatSize + seatSpacing) * zoom + 100);
            const canvasHeight = Math.max(600, seatRows * (seatSize + seatSpacing) * zoom + 100);

            canvas.width = canvasWidth;
            canvas.height = canvasHeight;
            canvas.style.width = canvasWidth + 'px';
            canvas.style.height = canvasHeight + 'px';

            // 绑定事件
            canvas.addEventListener('mousedown', onMouseDown);
            canvas.addEventListener('mousemove', onMouseMove);
            canvas.addEventListener('mouseup', onMouseUp);
            canvas.addEventListener('wheel', onWheel);

            // 防止右键菜单
            canvas.addEventListener('contextmenu', e => e.preventDefault());
        }

        // 创建行列标尺
        function createHeaders() {
            const rowHeaders = document.getElementById('rowHeaders');
            const colHeaders = document.getElementById('colHeaders');

            // 清空
            rowHeaders.innerHTML = '';
            colHeaders.innerHTML = '';

            // 创建行标尺
            for (let i = 0; i < seatRows; i++) {
                const header = document.createElement('div');
                header.className = 'header-item';
                header.style.height = (seatSize + seatSpacing) + 'px';
                header.textContent = i + 1;
                header.onclick = () => selectRow(i);
                rowHeaders.appendChild(header);
            }

            // 创建列标尺
            for (let i = 0; i < seatCols; i++) {
                const header = document.createElement('div');
                header.className = 'header-item';
                header.style.width = (seatSize + seatSpacing) + 'px';
                header.style.display = 'inline-flex';
                header.textContent = i + 1;
                header.onclick = () => selectCol(i);
                colHeaders.appendChild(header);
            }
        }

        // 渲染座位 - 增强版
        function renderSeats() {
            // 清空画布
            ctx.clearRect(0, 0, canvas.width, canvas.height);

            // 设置字体
            const fontSize = Math.max(9, Math.min(14, seatSize * 0.4));
            ctx.font = `${fontSize}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';

            // 是否显示详细视图
            const showDetails = zoom > 0.7;
            const enableRoundedCorners = showDetails && zoom > 0.8;

            for (let row = 0; row < seatRows; row++) {
                for (let col = 0; col < seatCols; col++) {
                    const seatKey = row + "_" + col;
                    const isSelected = seats[seatKey] !== undefined;

                    const x = col * (seatSize + seatSpacing) + offsetX + 20;
                    const y = row * (seatSize + seatSpacing) + offsetY + 20;

                    // 绘制座位背景
                    ctx.fillStyle = isSelected ? '#10B981' : '#E5E7EB';

                    if (enableRoundedCorners) {
                        // 圆角座位
                        const radius = Math.min(4, seatSize * 0.2);
                        drawRoundedRect(x, y, seatSize, seatSize, radius);
                        ctx.fill();
                    } else {
                        // 普通矩形
                        ctx.fillRect(x, y, seatSize, seatSize);
                    }

                    // 绘制边框
                    if (showDetails) {
                        ctx.strokeStyle = isSelected ? '#059669' : '#9CA3AF';
                        ctx.lineWidth = 1;
                        if (enableRoundedCorners) {
                            const radius = Math.min(4, seatSize * 0.2);
                            drawRoundedRect(x, y, seatSize, seatSize, radius);
                            ctx.stroke();
                        } else {
                            ctx.strokeRect(x, y, seatSize, seatSize);
                        }
                    }

                    // 绘制座位号
                    if (showDetails && seatSize > 15) {
                        ctx.fillStyle = isSelected ? 'white' : '#374151';
                        const seatNumber = String(row + 1).padStart(2, '0') + '-' + String(col + 1).padStart(2, '0');
                        ctx.fillText(seatNumber, x + seatSize/2, y + seatSize/2);
                    }
                }
            }

            // 绘制选择框
            if (isSelecting) {
                drawSelectionBox();
            }
        }

        // 绘制圆角矩形
        function drawRoundedRect(x, y, width, height, radius) {
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.lineTo(x + width - radius, y);
            ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
            ctx.lineTo(x + width, y + height - radius);
            ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
            ctx.lineTo(x + radius, y + height);
            ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
            ctx.lineTo(x, y + radius);
            ctx.quadraticCurveTo(x, y, x + radius, y);
            ctx.closePath();
        }

        // 绘制选择框
        function drawSelectionBox() {
            const left = Math.min(selectionStart.x, selectionEnd.x);
            const top = Math.min(selectionStart.y, selectionEnd.y);
            const width = Math.abs(selectionEnd.x - selectionStart.x);
            const height = Math.abs(selectionEnd.y - selectionStart.y);

            // 绘制选择框
            ctx.strokeStyle = '#10B981';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            ctx.strokeRect(left, top, width, height);

            // 绘制半透明填充
            ctx.fillStyle = 'rgba(16, 185, 129, 0.1)';
            ctx.fillRect(left, top, width, height);

            // 重置线条样式
            ctx.setLineDash([]);
        }

        // 鼠标按下事件 - 智能交互
        function onMouseDown(e) {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            // 检查是否点击了座位
            const clickedSeat = getSeatAtPosition(x, y);
            if (clickedSeat) {
                // 点击座位，直接切换状态
                toggleSeat(clickedSeat.row, clickedSeat.col);
            } else {
                // 没有点击座位，开始框选或拖拽
                isSelecting = true;
                selectionStart = { x, y };
                selectionEnd = { x, y };

                // 开始选择框绘制
                renderSeats();
            }

            isDragging = true;
            selectionStart.lastX = x;
            selectionStart.lastY = y;
        }

        // 鼠标移动事件 - 支持拖拽视图和框选
        function onMouseMove(e) {
            const rect = canvas.getBoundingClientRect();
            const x = e.clientX - rect.left;
            const y = e.clientY - rect.top;

            if (isDragging) {
                if (isSelecting) {
                    // 更新选择框
                    selectionEnd.x = x;
                    selectionEnd.y = y;
                    renderSeats();
                } else {
                    // 拖拽视图
                    const deltaX = x - selectionStart.lastX;
                    const deltaY = y - selectionStart.lastY;

                    offsetX += deltaX;
                    offsetY += deltaY;

                    selectionStart.lastX = x;
                    selectionStart.lastY = y;

                    renderSeats();
                }
            } else {
                // 悬停效果
                const hoveredSeat = getSeatAtPosition(x, y);
                if (hoveredSeat) {
                    canvas.style.cursor = 'pointer';
                } else {
                    canvas.style.cursor = 'grab';
                }
            }
        }

        // 鼠标抬起事件
        function onMouseUp(e) {
            if (isSelecting) {
                // 处理框选
                handleSelection();

                // 清除选择框并重新渲染
                renderSeats();

                isSelecting = false;
            }

            isDragging = false;
            canvas.style.cursor = 'grab';
        }

        // 获取指定位置的座位
        function getSeatAtPosition(canvasX, canvasY) {
            // 转换为座位坐标系
            const x = canvasX - offsetX - 20;
            const y = canvasY - offsetY - 20;

            // 计算座位位置
            const col = Math.floor(x / (seatSize + seatSpacing));
            const row = Math.floor(y / (seatSize + seatSpacing));

            // 检查是否在有效范围内
            if (row >= 0 && row < seatRows && col >= 0 && col < seatCols) {
                // 检查是否真的点击在座位上（而不是间隙）
                const seatX = col * (seatSize + seatSpacing);
                const seatY = row * (seatSize + seatSpacing);

                if (x >= seatX && x <= seatX + seatSize &&
                    y >= seatY && y <= seatY + seatSize) {
                    return { row, col };
                }
            }

            return null;
        }

        // 切换座位选中状态
        function toggleSeat(row, col) {
            const seatKey = row + "_" + col;

            if (seats[seatKey]) {
                // 取消选中
                delete seats[seatKey];
            } else {
                // 选中座位
                seats[seatKey] = {
                    row: row,
                    col: col,
                    price: 0,
                    color: "",
                    groupName: "",
                    thre_id: 10,
                    hall_id: 10,
                    movie_id: 10
                };
            }

            renderSeats();
            updateStats();
        }

        // 选择整行
        function selectRow(row) {
            for (let col = 0; col < seatCols; col++) {
                toggleSeat(row, col);
            }
        }

        // 选择整列
        function selectCol(col) {
            for (let row = 0; row < seatRows; row++) {
                toggleSeat(row, col);
            }
        }



        // 处理框选
        function handleSelection() {
            const left = Math.min(selectionStart.x, selectionEnd.x) - offsetX - 20;
            const top = Math.min(selectionStart.y, selectionEnd.y) - offsetY - 20;
            const right = Math.max(selectionStart.x, selectionEnd.x) - offsetX - 20;
            const bottom = Math.max(selectionStart.y, selectionEnd.y) - offsetY - 20;

            for (let row = 0; row < seatRows; row++) {
                for (let col = 0; col < seatCols; col++) {
                    const seatX = col * (seatSize + seatSpacing);
                    const seatY = row * (seatSize + seatSpacing);
                    const seatRight = seatX + seatSize;
                    const seatBottom = seatY + seatSize;

                    // 检查座位是否在选择框内
                    if (seatX < right && seatRight > left && seatY < bottom && seatBottom > top) {
                        toggleSeat(row, col);
                    }
                }
            }
        }

        // 鼠标滚轮缩放 - 以鼠标位置为中心
        function onWheel(e) {
            e.preventDefault();

            const rect = canvas.getBoundingClientRect();
            const mouseX = e.clientX - rect.left;
            const mouseY = e.clientY - rect.top;

            const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
            const newZoom = Math.max(0.3, Math.min(3, zoom * zoomFactor));

            // 以鼠标位置为中心缩放
            const zoomRatio = newZoom / zoom;
            offsetX = mouseX - (mouseX - offsetX) * zoomRatio;
            offsetY = mouseY - (mouseY - offsetY) * zoomRatio;
            zoom = newZoom;

            updateZoom();
        }

        // 更新缩放
        function updateZoom() {
            const canvasWidth = Math.max(800, seatCols * (seatSize + seatSpacing) * zoom + 100);
            const canvasHeight = Math.max(600, seatRows * (seatSize + seatSpacing) * zoom + 100);

            canvas.width = canvasWidth;
            canvas.height = canvasHeight;
            canvas.style.width = canvasWidth + 'px';
            canvas.style.height = canvasHeight + 'px';

            renderSeats();
            updateZoomDisplay();
        }

        // 更新缩放显示
        function updateZoomDisplay() {
            document.getElementById('zoomDisplay').textContent = Math.round(zoom * 100) + '%';
        }

        // 缩放控制函数
        function zoomIn() {
            zoom = Math.min(3, zoom * 1.2);
            updateZoom();
        }

        function zoomOut() {
            zoom = Math.max(0.3, zoom / 1.2);
            updateZoom();
        }

        function resetZoom() {
            zoom = 1;
            offsetX = 0;
            offsetY = 0;
            updateZoom();
        }

        // 更新统计信息
        function updateStats() {
            const totalSeats = seatRows * seatCols;
            const selectedCount = Object.keys(seats).length;

            document.getElementById('totalSeats').textContent = totalSeats;
            document.getElementById('selectedSeats').textContent = selectedCount;
            document.getElementById('seatTotal').textContent = `（共${totalSeats}个座位，已选择${selectedCount}个）`;
        }

        // 兼容性：提供与原版相同的全局接口
        window.seats = {
            getSeats: function() {
                return seats;
            }
        };

        // jQuery扩展：兼容原版的$.seats()调用
        if (typeof $ !== 'undefined') {
            $.seats = function(options) {
                return window.seats;
            };
        }
    </script>
</body>
</html>
