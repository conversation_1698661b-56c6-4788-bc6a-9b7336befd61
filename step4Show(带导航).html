<!DOCTYPE html>
<html lang="ch">
	<head>
		<title>设置座位预览</title>
		<!-- 设置 viewport -->
		<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
		<!-- IE -->
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<!-- 兼容国产浏览器的高速模式 -->
		<meta name="renderer" content="webkit">
		<meta name="Author" content="<EMAIL>" />
		<meta name="Keywords" content="" />
		<meta name="Description" content="" />
		<meta charset="UTF-8">
		<link rel="stylesheet" type="text/css" href="css/reset.css">
		<link rel="stylesheet" href="css/translateCommon.css">
		<link rel="stylesheet" href="layui/css/layui.css" />
		<link rel="stylesheet" href="css/stepPlug.css" />
		<link rel="stylesheet" href="plugin/spectrum/spectrum.css">
		<link rel="stylesheet" href="css/seat3.css" />
		<link rel="stylesheet" href="css/dragModule.css" />
		<link rel="stylesheet" href="css/step4only.css" />

	</head>
	<body>
		<div id="section">
			<!-- header -->
			<div id="header">
				<div class="hui">
					<img src="images/hui.jpg" alt="">
				</div>
				<div class="logo">
					<h1 class="f_left">
						<a href="#">
							<img src="images/logo.png" alt=""></a>
					</h1>
					<div class="f_right logo_r">
						<ul class="logo_r_show">
							<!-- <li class="search">
								<p>
									<span class="f_left">受邀者</span>
									<input class="f_left" type="text"> <i></i>
								</p>
							</li> -->
							<li class="translate-box">
								<a class="translate-box-cn" href="javascript:translate.changeLanguage('english');javascript:void(0);">
									<img src="images/translateEn.png" alt="">
								</a>
								<a class="translate-box-en" style="display: none;"
									href="javascript:translate.changeLanguage('chinese_simplified');javascript:void(0);">
									<img src="images/translateCn.png" alt="">
								</a>
							</li>
							<li class="h_qiandao">
								<a href="#">微信签到</a>
							</li>
							<li class="prompt">
								<a href="#">
									<span>0</span>
								</a>
							</li>
							<li class="help">帮助与文档</li>
							<li class="tel">18621589099</li>
						</ul>
					</div>
				</div>
			</div>

			<!-- content -->
			<div id="content" class="clearfix">
				<!-- 左边导航 -->
				<div class="c_left">
					<ul class="c_left_nav f_left">
						<li class="current" title="活动">
							<img src="images/icon6.png" alt="">
						</li>
						<li title="应用">
							<img src="images/icon7.png" alt="">
						</li>
						<li title="联系人">
							<img src="images/icon8.png" alt="">
						</li>
						<li title="供应商">
							<img src="images/icon9.png" alt="">
						</li>
						<li title="账户">
							<img src="images/icon10.png" alt="">
						</li>
					</ul>

				</div>

				<!-- 主要模块 -->
				<div class="c_right">
					<div class="main">
						<div class="new-content-top">
							<div class="content-top-btn">
								<button class="layui-btn" type="button" onclick="editSeat()">修改区域</button>
								<button class="layui-btn" type="button" onclick="">保存</button>
								<button class="layui-btn" type="button" onclick="viewMySeat()">预览</button>
							</div>
						</div>
						<div class="main-content">
							<div class="main-left">
								<!-- <div class="step-html" style="width: 600px;">
									<div class="step-box">
										<div class="step-list step-act">
											<span class="step-number">1</span>
										</div>
										<div class="step-list-html-text step-act">设置场地排列</div>
										<div class="step-line"></div>
										<div class="step-list step-act">
											<span class="step-number step-act">2</span>
										</div>
										<div class="step-list-html-text step-act">设置场地布局</div>
										<div class="step-line"></div>
										<div class="step-list step-act">
											<span class="step-number step-act">3</span>
										</div>
										<div class="step-list-html-text step-act">设置分区</div>
										<div class="step-line"></div>
										<div class="step-list step-act">
											<span class="step-number step-act">4</span>
										</div>
										<div class="step-list-html-text step-act">设置效果图</div>
									</div>
								</div> -->

								<!-- <div class="main-left-tab">
									<span class="main-left-tab-act" onclick="viewSrc('step4.html')">分区引导图设置</span>
									<span onclick="viewSrc('step5.html')">座位视图设置</span>
									<span onclick="viewSrc('step6.html')">区域视图设置</span>
								</div> -->

								<div class="zoomBox">
									<i class="layui-icon layui-icon-reduce-circle" onclick="zoomFn('in')"></i>
									<i class="layui-icon layui-icon-add-circle" onclick="zoomFn('out')"></i>
								</div>
								<!-- <div
									style="width: 100%;text-align: center;font-size: 25px;color: #000;font-weight: bold;margin-top: 15px;letter-spacing: 5px;"
									id="mySeat">
								</div> -->
								<!-- 座位遮罩层 -->
								<!-- <div class="seatFixed"></div> -->
								<div class="seatBoxContent seatBoxContentSetp4" id="seatBoxContent">



									<div class="seatBox" id="seatBox" style="width: auto;background: #e5e5e5;" zoom="1">

										<div class="dragContent">
											<div class="dragContent freeContainerModule">
												<!-- 组件  dragItemClicked coorClick-->
												<div class="ui-draggable freeDragItem " id="timestamp" onclick="freeDragli(this,event)">
													<div name="paper">
														<div style="position: relative;" class="freeBtbBox">
															<div class="freeweb_btn"><a src="#">组件</a></div>
															<div class="coor3 "></div>
															<img class="del_img" src="images/delete_hover.png" onclick="freeDeleteImg(this)" />
														</div>
													</div>
												</div>
											</div>
										</div>

									</div>

								</div>
								<!-- <div class="bottom-btn">
									<button class="layui-btn" type="button" onclick="editSeat()">修改区域</button>
									<button class="layui-btn" type="button" onclick="">保存</button>
									<button class="layui-btn" type="button" onclick="viewMySeat()">预览</button>
								</div> -->
							</div>
							<div class="main-right" style="display: none;">
								<div class="container">
									<div class="layui-tab" lay-filter="tab-hash">
										<ul class="layui-tab-title">
											<li lay-id="11">标识设置</li>
											<li class="layui-this" lay-id="22">基础设置</li>
										</ul>
										<div class="layui-tab-content">

											<div class="layui-tab-item">
												<!-- 组件样式设置 -->
												<div class="settingContent">
													<h4 class="text-left">组件样式</h4>
													<ul class="btnModule">
														<li class="btnModuleAct"
															onclick="btnSelect(this,'.dragItemClicked .freeBtbBox ','.dragItemClicked .freeweb_btn>a')">
															<span style="background: #4078cb; color: #fff;">主要按钮</span>
														</li>
														<li
															onclick="btnSelect(this,'.dragItemClicked .freeBtbBox ','.dragItemClicked .freeweb_btn>a')"
															class="">
															<span style="border:1px solid #4078cb;background:#fff;color:#4078cb;">次要按钮</span>
														</li>
														<li buttonid="8283"
															onclick="btnSelect(this,'.dragItemClicked .freeBtbBox ','.dragItemClicked .freeweb_btn>a')"
															class="">
															<span
																style="background:#4078cb;color:#fff;border-radius: 20px;border: 1px solid transparent;">圆角按钮</span>
														</li>
														<li buttonid="8284"
															onclick="btnSelect(this,'.dragItemClicked .freeBtbBox ','.dragItemClicked .freeweb_btn>a')"
															class="">
															<span style="background:#4078cb;color:#fff;border-radius: 50%;">圆按钮</span>
														</li>
													</ul>
													<hr />
													<h4 class="text-left">组件文字</h4>
													<div class="content-input">
														<div class="content-input-box">
															<input id="buttonModule" type="text" class="layui-input" value="组件"
																onchange="btnValue(this,'.dragItemClicked .freeweb_btn a')"
																onkeyup="btnValue(this,'.dragItemClicked .freeweb_btn a')">
														</div>
													</div>
													<hr />
													<h4 class="text-left">字体</h4>
													<div class="content-select-box layui-form">
														<select class="selectText selectTextFontFamily" lay-filter="fontFamily">
															<option value="Microsoft YaHei">微软雅黑</option>
															<option value="SimHei">黑体</option>
															<option value="SimSun">宋体</option>
															<option value="KaiTi">楷体</option>
														</select>
													</div>
													<hr />
													<h4 class="text-left">字号</h4>
													<div class="rangeBoxSetting">
														<div id="rangeFontSize" class="rangeItem"></div>
													</div>
													<hr />
													<h4 class="text-left">文字颜色</h4>
													<div class="content-input">
														<div class="content-input-box">
															<!-- <input type="text" class="form-control" id="btnTextColor"> -->
															<div id="btnTextColor"></div>
														</div>
													</div>
													<hr />
													<h4 class="text-left">组件颜色</h4>
													<div class="content-input">
														<div class="content-input-box">
															<!-- <input type="text" class="form-control" id="btnColor"> -->
															<div id="moduleBgColor"></div>
														</div>
													</div>
													<hr />

													<h4 class="text-left">边框</h4>
													<div class="content-select-box layui-form">
														<select class="selectText selectTextBorderStyle" lay-filter="borderStyle">
															<option value="none">无边框</option>
															<option value="solid">——</option>
															<option value="dotted">-----</option>
														</select>
													</div>
													<hr />
													<h4 class="text-left">边框颜色</h4>
													<div class="content-input">
														<div class="content-input-box">
															<!-- <input type="text" class="form-control" id="btnBorderColor"> -->
															<div id="btnBorderColor"></div>
														</div>
													</div>
													<hr />
													<h4 class="text-left">边框宽度</h4>
													<div class="rangeBoxSetting">
														<div id="rangeBtnBorderWidth" class="rangeItem"></div>
													</div>
													<hr />


													<h4 class="text-left">圆角</h4>
													<div class="rangeBoxSetting">
														<div id="rangeBorderRadius" class="rangeItem"></div>
													</div>
													<h4 class="text-left">宽度</h4>
													<div class="rangeBoxSetting">
														<div id="rangeBorderWidth" class="rangeItem"></div>
													</div>


												</div>

												<div class="bottom-btn">
													<button id="batchAllocation" class="layui-btn" type="button"
														onclick="addModuleItem()">新增组件</button>
												</div>

											</div>
											<!-- 基础设置 -->
											<div class="layui-tab-item layui-show">
												<div class="seatNameBox">
													<span>场地图名称：</span>
													<input type="text" class="layui-input" />
												</div>
												<h4 class="text-danger text-left">*设置座位四周间距</h4>
												<br />
												<h4 class="text-left">设置间距</h4>
												<div class="rangeBox">
													<div class="rangeBoxTitle">上边距</div>
													<div id="rangeTop"></div>
												</div>
												<div class="rangeBox">
													<div class="rangeBoxTitle">右边距</div>
													<div id="rangeRight"></div>
												</div>
												<div class="rangeBox">
													<div class="rangeBoxTitle">下边距</div>
													<div id="rangeBottom"></div>
												</div>
												<div class="rangeBox">
													<div class="rangeBoxTitle">左边距</div>
													<div id="rangeLeft"></div>
												</div>

												<h4 class="text-left">背景尺寸</h4>
												<div class="bgSize">背景宽度：背景高度：</div>

												<h4 class="text-left">背景图片</h4>
												<div class="uploadBg">
													<button type="button" class="layui-btn">上传自定义背景图</button>
												</div>
												<div class="uploadTips">*自定义背景推荐根据实际尺寸等比上传</div>
												<div class="uploadImgBox">
													<img src="images/default.png" alt="" />
												</div>
												<h4 class="text-left">背景颜色</h4>
												<div class="bgBox">
													<!-- <input type="text" class="form-control" id="bgColor"> -->
													<div id="bgColor"></div>
												</div>


											</div>

											<!-- end -->
										</div>
									</div>


								</div>
							</div>
						</div>


					</div>
				</div>
			</div>
		</div>
		<!-- 灰色背景 -->
		<div id="fade" class="black_overlay"></div>

		<!-- 下载弹窗 -->
		<!-- <div id="light2" class="white_content2">
  <div class="title-one">
    <span>下载</span>
    <div class="f_right pop_xx" onclick="cancel('light2');"></div>
  </div>
  <div class="title-two">
      <img src="images/35.gif">
      <p> 正在下载中，请稍后……</p>
    </div>
    <div class="title-three">
      <button class="pop_btn2">取消</button>
    </div>
</div> -->







		<!-- <script src="js/jquery-1.7.2.min.js"></script> -->
		<script src="js/jquery-3.6.3.min.js"></script>
		<script src="js/index.js"></script>
		<script src="js/index.js"></script>
		<!-- <script src="js/jquery-ui-1.9.2.custom.min.js"></script> -->
		<script src="js/moveZoom.js"></script>
		<script src="js/jquery-ui.js"></script>
		<script src="js/dragModule.js"></script>


		<script>
			$(function() {
				// 动态加载可放大功能
				$.getScript('js/dragModule.js', function() {});
				// 动态加载可放大功能
				$.getScript('js/moveZoom.js', function() {
					all();
				});
			})
		</script>

		<script src="translateJs/translate.js"></script>
		<script src="translateJs/translateCommon.js"></script>
		<script src="plugin/spectrum/spectrum.js"></script>
		<script src="plugin/jdialog/JDialog.min.js"></script>
		<script src="js/seat.js"></script>
		<script src="layui/layui.js"></script>
		<script>
			var layer = "";
			var slider = "";
			var colorpickerRender = "";
			var setWidthSlider = ""; // 定义宽度滑块
			var setBorderWidthSlider = ""; // 边框宽度
			var setRadiusSlider = ""; // 定义圆角滑块
			var setFontSizeSlider = ""; // 定义字体大小滑块
			var noGroupNumber = 0;
			var noGroupHasAssign = 0; // 未分组已分配数量
			var form = "";
			var channelNumber = 0; // 批量通道人数
			var canUseNumber = 0; // 可用分配数量
			var canUseGroup = []; // 可用分配数组
			var channelSelectArray = JSON.parse(localStorage.getItem("channelSelectArrayStorage")) || []; // 已经分配过的通道

			var dragModuleWidth = 100;
			var dragModuleBorderWidth = 0


			layui.use(function() {
				form = layui.form;
				var element = layui.element;
				layer = layui.layer;

				element.on('tab(tab-hash)', function(obj) {
					let thisIndex = obj.index
					if (thisIndex == 1) {
						$(".dragItemClicked").removeClass("dragItemClicked")
						$(".coorClick").removeClass("coorClick")
					}
				});

				// 获取padding 值
				var div = document.getElementById("seatBox");
				var computedStyle = getComputedStyle(div, null);
				var paddingTop = Number(computedStyle.paddingTop.replace("px", ""))
				var paddingLeft = Number(computedStyle.paddingLeft.replace("px", ""))
				var paddingRight = Number(computedStyle.paddingRight.replace("px", ""))
				var paddingBottom = Number(computedStyle.paddingBottom.replace("px", ""))


				// 滑块
				slider = layui.slider;
				// 渲染
				slider.render({
					elem: '#rangeTop',
					min: 0,
					max: 300,
					value: paddingTop, //初始值
					input: true, //输入框
					change: function(value) {
						// console.log(value)
						$("#seatBox").css({
							"paddingTop": value + "px"
						})
						getSize()
					}
				});
				// 右边
				slider.render({
					elem: '#rangeRight',
					min: 0,
					max: 300,
					value: paddingRight, //初始值
					input: true, //输入框
					change: function(value) {
						// console.log(value)
						$("#seatBox").css({
							"paddingRight": value + "px"
						})
						getSize()
					}
				});
				// 右边
				slider.render({
					elem: '#rangeBottom',
					min: 0,
					max: 300,
					value: paddingBottom, //初始值
					input: true, //输入框
					change: function(value) {
						// console.log(value)
						$("#seatBox").css({
							"paddingBottom": value + "px"
						})
						getSize()
					}
				});
				// 左边边
				slider.render({
					elem: '#rangeLeft',
					min: 0,
					max: 300,
					value: paddingLeft, //初始值
					input: true, //输入框
					change: function(value) {
						// console.log(value)
						$("#seatBox").css({
							"paddingLeft": value + "px"
						})
						getSize()
					}
				});

				// 圆角设置
				setRadiusSlider = slider.render({
					elem: '#rangeBorderRadius',
					min: 0,
					max: 50,
					value: 0, //初始值
					input: true, //输入框
					change: function(value) {
						// console.log(value)
						$(".dragItemClicked").find(".freeweb_btn>a").css({
							"borderRadius": value + "px"
						})
						getSize()
					}
				});
				// 宽度设置
				setWidthSlider = slider.render({
					elem: '#rangeBorderWidth',
					min: 0,
					max: 1000,
					value: dragModuleWidth, //初始值
					input: true, //输入框
					change: function(value) {
						// console.log(value)
						$(".dragItemClicked").find(".freeBtbBox").css({
							"width": value + "px"
						})
					}
				});
				// 字体大小
				setFontSizeSlider = slider.render({
					elem: '#rangeFontSize',
					min: 10,
					max: 30,
					value: 14, //初始值
					input: true, //输入框
					change: function(value) {
						// console.log(value)
						$(".dragItemClicked").find(".freeBtbBox").find(".freeweb_btn>a").css({
							"font-size": value + "px"
						})
					}
				});

				// 20240828 新增
				// 设置字体类型
				form.on('select(fontFamily)', function(data) {
					var elem = data.elem; // 获得 select 原始 DOM 对象
					var value = data.value; // 获得被选中的值
					console.log(value)
					$(".dragItemClicked .freeweb_btn a").css("font-family", value)
				});

				// 设置边框 "
				form.on('select(borderStyle)', function(data) {
					var elem = data.elem; // 获得 select 原始 DOM 对象
					var value = data.value; // 获得被选中的值
					console.log(value)
					$(".dragItemClicked .freeweb_btn a").css("border-style", value)
				});
				// 边框宽度设置
				setBorderWidthSlider = slider.render({
					elem: '#rangeBtnBorderWidth',
					min: 0,
					max: 50,
					value: dragModuleBorderWidth, //初始值
					input: true, //输入框
					change: function(value) {
						// console.log(value)
						$(".dragItemClicked").find(".freeweb_btn a").css({
							"border-width": value + "px"
						})
					}
				});
				// 颜色选择器
				colorpickerRender = layui.colorpicker;

				// 大件背景色
				colorpickerRender.render({ // 
					elem: '#bgColor',
					color: '#f5f5f5', // hex
					alpha: true, // 开启透明度
					format: 'rgb',
					predefine: true, // 开启预定义颜色
					colors: ["rgb(152, 0, 0)", "rgb(255, 0, 0)", "rgb(255, 153, 0)", "rgb(255, 255, 0)", "rgb(0, 255, 0)",
						"rgb(0, 255, 255)", "rgb(74, 134, 232)", "rgb(0, 0, 255)", "rgb(255, 0, 255)"
					], //自定义预定义颜色项
					done: function(value) {
						$("#seatBox").css({
							background: value
						})
					}
				});

				// 组件背景色
				colorpickerRender.render({ // 
					elem: '#moduleBgColor',
					color: '#4078cb', // hex
					alpha: true, // 开启透明度
					format: 'rgb',
					predefine: true, // 开启预定义颜色
					colors: ["rgb(152, 0, 0)", "rgb(255, 0, 0)", "rgb(255, 153, 0)", "rgb(255, 255, 0)", "rgb(0, 255, 0)",
						"rgb(0, 255, 255)", "rgb(74, 134, 232)", "rgb(0, 0, 255)", "rgb(255, 0, 255)"
					], //自定义预定义颜色项
					done: function(value) {
						console.log(value); // 当前选中的颜色值
						$(".dragItemClicked").find(".freeweb_btn>a").css({
							background: value
						})
					}
				});
				// 组件字体颜色
				colorpickerRender.render({ //
					elem: '#btnTextColor',
					color: '#ffffff', // hex
					// alpha: true, // 开启透明度
					format: 'rgb',
					predefine: true, // 开启预定义颜色
					colors: ["rgb(152, 0, 0)", "rgb(255, 0, 0)", "rgb(255, 153, 0)", "rgb(255, 255, 0)", "rgb(0, 255, 0)",
						"rgb(0, 255, 255)", "rgb(74, 134, 232)", "rgb(0, 0, 255)", "rgb(255, 0, 255)"
					], //自定义预定义颜色项
					done: function(value) {
						// console.log(value); // 当前选中的颜色值
						$(".dragItemClicked").find(".freeweb_btn>a").css({
							color: value
						})
					}
				});
				// 组件边框颜色
				colorpickerRender.render({ //
					elem: '#btnBorderColor',
					color: '#ffffff', // hex
					// alpha: true, // 开启透明度
					format: 'rgb',
					predefine: true, // 开启预定义颜色
					colors: ["rgb(152, 0, 0)", "rgb(255, 0, 0)", "rgb(255, 153, 0)", "rgb(255, 255, 0)", "rgb(0, 255, 0)",
						"rgb(0, 255, 255)", "rgb(74, 134, 232)", "rgb(0, 0, 255)", "rgb(255, 0, 255)"
					], //自定义预定义颜色项
					done: function(value) {
						// console.log(value); // 当前选中的颜色值
						$(".dragItemClicked").find(".freeweb_btn>a").css({
							"border-color": value
						})
					}
				});



			});

			$(function() {
				getSize()
			})
			// 获取初始宽高
			function getSize() {
				var div = document.getElementById("seatBox");
				var computedStyle = getComputedStyle(div, null);

				let w = $("#seatBox").width() + Number(computedStyle.paddingLeft.replace("px", "")) + Number(computedStyle
					.paddingRight.replace("px", ""))
				let h = $("#seatBox").height() + Number(computedStyle.paddingTop.replace("px", "")) + Number(computedStyle
					.paddingBottom.replace("px", ""))
				$(".bgSize").html(`背景宽度：${w} 背景高度：${h}`)
			}

			// 放大缩小
			function zoomFn(obj) {
				let zoomVal = Number($("#seatBox").attr("zoom"))
				if (obj == "in") {
					if (zoomVal >= 0.2) {
						zoomVal = zoomVal - 0.1
						$("#seatBox").css({
							transform: "scale(" + zoomVal + ")"
						})
					}

				} else if (obj == "out") {

					if (zoomVal < 1) {
						zoomVal = zoomVal + 0.1
						$("#seatBox").css({
							transform: "scale(" + zoomVal + ")"
						})
					}

				}
				$("#seatBox").attr("zoom", zoomVal)
			}
		</script>
		<script>
			let seatRows = localStorage.getItem("seatRows");
			let seatCols = localStorage.getItem("seatCols");

			// var groupObject = JSON.parse(localStorage.getItem('groupArrayObject')) || []
			try {
				var datas = JSON.parse(localStorage.getItem("seats"));
				var selected = JSON.parse(localStorage.getItem("selected"));
			} catch (e) {}
			var seats = $.seats({
				box: "#seatBox",
				rows: seatRows,
				cols: seatCols,
				size: 20, //座位的尺寸，单位：像素	
				hall_id: 10, //厅，如：5号厅
				thre_id: 20, //剧场，如：大地电影院中天广场沃尔玛店
				movie_id: 30, //场次ID
				step: 5,
				maxTicketNumber: 1000, //最多购票张数
				datas: datas,
				selected: selected || {},
				onSelected: function(seat) {

					/*************/
					var id = "selected_" + seat.row + "_" + seat.col;
					// 获取分区名称
					console.log(seat.color)
					let getGroupName = seat.groupName
					let getGroupLength = $(`span[name='${getGroupName}']`).length
					// console.log(getGroupLength)

					// 判断是否已经分配的座位
					let hasDisabled = $(`#${seat.row}_${seat.col}`).find("span").hasClass("disabledCancel")
					console.log(hasDisabled)
					// var selectedHtml = ''
					// 获取真实的座位号（去除空格的)
					let realSeat = $(`#${seat.row}_${seat.col}`).attr("seat").split("-")
					console.log(realSeat)

					/*********************************/
					return true;
				},
				onUnselected: function(seat) {
					var id = "selected_" + seat.row + "_" + seat.col;
					$("#" + id).remove();
				},
				onerror: function(msg) {
					// JDialog.msg({
					// 	type: 'error',
					// 	content: msg,
					// 	container: "#seatBox"
					// });
					layer.msg(msg, {
						icon: 2,
						time: 1000 // 设置 2 秒后自动关闭
					});
				}
			});

			/*************
			$("#btnColor").spectrum({
				showPalette: true, // 显示选择器面板
				// showPaletteOnly: true, //只显示选择器面板	
				color: '#4078cb',
				palette: [
					["rgb(152, 0, 0)", "rgb(255, 0, 0)", "rgb(255, 153, 0)", "rgb(255, 255, 0)", "rgb(0, 255, 0)",
						"rgb(0, 255, 255)", "rgb(74, 134, 232)", "rgb(0, 0, 255)", "rgb(255, 0, 255)"
					],
				],
				change: function(color) {
					console.log(color)
					$("#btnColor").val(color.toHexString());
					$(".dragItemClicked").find(".freeweb_btn>a").css({
						background: color.toHexString()
					})
				}
			});
			// 背景颜色
			$("#bgColor").spectrum({
				showPalette: true, // 显示选择器面板
				// showPaletteOnly: true, //只显示选择器面板	
				color: '#f5f5f5',
				palette: [
					["rgb(152, 0, 0)", "rgb(255, 0, 0)", "rgb(255, 153, 0)", "rgb(255, 255, 0)", "rgb(0, 255, 0)",
						"rgb(0, 255, 255)", "rgb(74, 134, 232)", "rgb(0, 0, 255)", "rgb(255, 0, 255)", "rgb(229, 229, 229)"
					],
				],
				change: function(color) {
					console.log(color)
					$("#bgColor").val(color.toHexString());
					$("#seatBox").css({
						background: color.toHexString()
					})
				}
			});

			// 20240828新增
			// 字体颜色
			$("#btnTextColor").spectrum({
				showPalette: true, // 显示选择器面板
				// showPaletteOnly: true, //只显示选择器面板	
				color: '#4078cb',
				palette: [
					["rgb(152, 0, 0)", "rgb(255, 0, 0)", "rgb(255, 153, 0)", "rgb(255, 255, 0)", "rgb(0, 255, 0)",
						"rgb(0, 255, 255)", "rgb(74, 134, 232)", "rgb(0, 0, 255)", "rgb(255, 0, 255)"
					],
				],
				change: function(color) {
					console.log(color)
					$("#btnTextColor").val(color.toHexString());
					$(".dragItemClicked").find(".freeweb_btn>a").css({
						color: color.toHexString()
					})
				}
			});
			// 边框颜色
			$("#btnBorderColor").spectrum({
				showPalette: true, // 显示选择器面板
				// showPaletteOnly: true, //只显示选择器面板	
				color: '#4078cb',
				palette: [
					["rgb(152, 0, 0)", "rgb(255, 0, 0)", "rgb(255, 153, 0)", "rgb(255, 255, 0)", "rgb(0, 255, 0)",
						"rgb(0, 255, 255)", "rgb(74, 134, 232)", "rgb(0, 0, 255)", "rgb(255, 0, 255)"
					],
				],
				change: function(color) {
					console.log(color)
					$("#btnBorderColor").val(color.toHexString());
					$(".dragItemClicked").find(".freeweb_btn>a").css({
						"border-color": color.toHexString()
					})
				}
			});
			**********************/


			// 所属区域高亮

			$(function() {
				// let hrefSrc = window.location.href.split("?")[1].split("-")
				let row = 4
				let col = 18
				let seat = `${row}-${col}`
				console.log(seat)
				let groupName = $(`td[seat='${seat}']`).find("span").attr("name")
				console.log(groupName)
				// 回显座位到页面
				$("#mySeat").html(`我的座位：${groupName}区 - ${row}排${col}座`)

				// $(`td[seat='${seat}']`).find("span").html(seat).css({
				// 	color: "#fff",
				// 	fontSize: "10px",
				// 	lineHeight: "20px"
				// })
				// console.log($("td").find(`span[name='${groupName}']`).)
				let groupBg = $("td").find(`span[name='${groupName}']`).attr("style").split("background")[1].replace(":", "")
				groupBg = groupBg.replace(";", "")
				// 遍历所有可选座位
				$("td").each(function() {
					let backNone = $(this).find("span").attr("style")
					if (!backNone.includes("background: none;")) {
						$(this).find("span").css({
							// background: '#d7d7d7'
							opacity: 0.5
						})
					}
				})

				// $("td").find("span").css({
				// 	background: '#d7d7d7'
				// })
				$("td").find("span").removeClass("disabled")


				$("td").find(`span[name='${groupName}']`).css({
					background: groupBg,
					opacity: 0.5
				})
				// $(`td[seat='${seat}']`).find("span").addClass("disabled")
				// $(`td[seat='${seat}']`).find("span").css({
				// 	opacity: 1
				// })
			})

			function editSeat() {
				window.location.href = "step3.html"
			}
			// 预览
			function viewMySeat() {
				window.location.href = "mySeat.html"
			}

			function viewSrc(src) {
				window.location.href = src
			}
		</script>

		<!-- 新增组件设置js -->
		<script src="js/seatModule4.js"></script>



	</body>
</html>