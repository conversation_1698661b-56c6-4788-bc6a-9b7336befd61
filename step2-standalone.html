<!DOCTYPE html>
<html lang="ch">
<head>
    <title>设置座位 - 独立版本</title>
    <meta charset="UTF-8">
    <link rel="stylesheet" type="text/css" href="css/reset.css">
    <link rel="stylesheet" href="css/translateCommon.css">
    <link rel="stylesheet" href="layui/css/layui.css" />
    <link rel="stylesheet" href="css/stepPlug.css" />
    <link rel="stylesheet" href="css/seat3.css" />
    <link rel="stylesheet" href="css/step2only.css" />
    <style>
        .demo-canvas {
            border: 2px solid #333;
            background: white;
            cursor: crosshair;
        }
        .demo-controls {
            margin: 10px 0;
        }
        .demo-controls button {
            margin: 5px;
            padding: 8px 16px;
        }
        .demo-info {
            background: #f0f0f0;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div id="section">
        <div id="content" class="clearfix">
            <div class="c_right">
                <div class="main">
                    <div class="main-content">
                        <div class="main-html">
                            <!-- 步骤条 -->
                            <div class="step-html">
                                <div class="step-box">
                                    <div class="step-item step-item-active">
                                        <div class="step-item-tail"></div>
                                        <div class="step-item-icon">
                                            <i class="layui-icon layui-icon-ok"></i>
                                        </div>
                                        <div class="step-item-content">
                                            <div class="step-item-title">设置场地排列</div>
                                            <div class="step-item-description">设置场地的行数和列数</div>
                                        </div>
                                    </div>
                                    <div class="step-item step-item-active">
                                        <div class="step-item-tail"></div>
                                        <div class="step-item-icon">
                                            <span class="step-item-icon-index">2</span>
                                        </div>
                                        <div class="step-item-content">
                                            <div class="step-item-title">设置场地布局</div>
                                            <div class="step-item-description">选择实际有座位的位置</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 演示信息 -->
                            <div class="demo-info">
                                <h3>🎯 新Canvas座位系统演示</h3>
                                <p><strong>操作说明：</strong></p>
                                <ul>
                                    <li>✅ <strong>点击座位</strong> - 选择/取消单个座位</li>
                                    <li>✅ <strong>拖拽框选</strong> - 按住鼠标拖拽选择多个座位</li>
                                    <li>✅ <strong>缩放视图</strong> - 使用下方按钮放大/缩小</li>
                                    <li>✅ <strong>行列选择</strong> - 点击行号/列号选择整行/整列</li>
                                </ul>
                                <p id="seatTotal">当前座位总数：0个</p>
                            </div>
                            
                            <!-- 控制按钮 -->
                            <div class="demo-controls">
                                <button onclick="zoomIn()" class="layui-btn layui-btn-sm">放大 🔍+</button>
                                <button onclick="zoomOut()" class="layui-btn layui-btn-sm">缩小 🔍-</button>
                                <button onclick="selectRow(0)" class="layui-btn layui-btn-sm layui-btn-normal">选择第1行</button>
                                <button onclick="selectCol(0)" class="layui-btn layui-btn-sm layui-btn-normal">选择第1列</button>
                                <button onclick="clearAll()" class="layui-btn layui-btn-sm layui-btn-warm">清空选择</button>
                                <button onclick="saveData()" class="layui-btn layui-btn-sm layui-btn-danger">保存数据</button>
                            </div>
                            
                            <!-- 座位图容器 -->
                            <div class="seatBoxContent seatBoxContentSetp2" id="seatBoxContent">
                                <div class="newSeatBox">
                                    <div class="seatBox" id="seatBox" style="width: auto;" zoom="1">
                                        <canvas id="seatCanvas" class="demo-canvas" width="800" height="500"></canvas>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- 底部按钮 -->
                            <div class="bottom-btn">
                                <button type="button" class="layui-btn layui-btn-primary" onclick="goBack()">上一步</button>
                                <button type="button" class="layui-btn" onclick="goNext()">下一步</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="layui/layui.js"></script>
    <script src="js/jquery-3.3.1.min.js"></script>
    
    <script>
        // 简化的座位系统演示
        class SimpleSeatDemo {
            constructor(canvas) {
                this.canvas = canvas
                this.ctx = canvas.getContext('2d')
                this.seats = []
                this.selectedSeats = new Set()
                this.zoom = 1
                this.offsetX = 50
                this.offsetY = 50
                this.isSelecting = false
                this.selectionStart = { x: 0, y: 0 }
                this.selectionEnd = { x: 0, y: 0 }
                
                this.rows = parseInt(localStorage.getItem("seatRows")) || 8
                this.cols = parseInt(localStorage.getItem("seatCols")) || 10
                
                this.init()
            }
            
            init() {
                this.generateSeats()
                this.bindEvents()
                this.render()
            }
            
            generateSeats() {
                this.seats = []
                const seatSize = 24
                const spacing = 4
                
                for (let row = 0; row < this.rows; row++) {
                    for (let col = 0; col < this.cols; col++) {
                        this.seats.push({
                            id: `${row}_${col}`,
                            row: row,
                            col: col,
                            x: col * (seatSize + spacing),
                            y: row * (seatSize + spacing),
                            width: seatSize,
                            height: seatSize
                        })
                    }
                }
            }
            
            bindEvents() {
                this.canvas.addEventListener('mousedown', (e) => this.onMouseDown(e))
                this.canvas.addEventListener('mousemove', (e) => this.onMouseMove(e))
                this.canvas.addEventListener('mouseup', (e) => this.onMouseUp(e))
            }
            
            onMouseDown(e) {
                const rect = this.canvas.getBoundingClientRect()
                const x = e.clientX - rect.left
                const y = e.clientY - rect.top
                
                const seat = this.getSeatAt(x, y)
                if (seat) {
                    this.toggleSeat(seat)
                } else {
                    this.isSelecting = true
                    this.selectionStart = { x, y }
                    this.selectionEnd = { x, y }
                }
            }
            
            onMouseMove(e) {
                if (this.isSelecting) {
                    const rect = this.canvas.getBoundingClientRect()
                    this.selectionEnd = {
                        x: e.clientX - rect.left,
                        y: e.clientY - rect.top
                    }
                    this.render()
                }
            }
            
            onMouseUp(e) {
                if (this.isSelecting) {
                    this.selectInBox()
                    this.isSelecting = false
                    this.render()
                }
            }
            
            getSeatAt(x, y) {
                const seatX = (x - this.offsetX) / this.zoom
                const seatY = (y - this.offsetY) / this.zoom
                
                return this.seats.find(seat => 
                    seatX >= seat.x && seatX <= seat.x + seat.width &&
                    seatY >= seat.y && seatY <= seat.y + seat.height
                )
            }
            
            toggleSeat(seat) {
                if (this.selectedSeats.has(seat.id)) {
                    this.selectedSeats.delete(seat.id)
                } else {
                    this.selectedSeats.add(seat.id)
                }
                this.updateStats()
                this.render()
            }
            
            selectInBox() {
                const startX = Math.min(this.selectionStart.x, this.selectionEnd.x)
                const startY = Math.min(this.selectionStart.y, this.selectionEnd.y)
                const endX = Math.max(this.selectionStart.x, this.selectionEnd.x)
                const endY = Math.max(this.selectionStart.y, this.selectionEnd.y)
                
                this.seats.forEach(seat => {
                    const seatScreenX = seat.x * this.zoom + this.offsetX
                    const seatScreenY = seat.y * this.zoom + this.offsetY
                    
                    if (seatScreenX >= startX && seatScreenX <= endX &&
                        seatScreenY >= startY && seatScreenY <= endY) {
                        if (this.selectedSeats.has(seat.id)) {
                            this.selectedSeats.delete(seat.id)
                        } else {
                            this.selectedSeats.add(seat.id)
                        }
                    }
                })
                
                this.updateStats()
            }
            
            selectRow(rowIndex) {
                this.seats.forEach(seat => {
                    if (seat.row === rowIndex) {
                        if (this.selectedSeats.has(seat.id)) {
                            this.selectedSeats.delete(seat.id)
                        } else {
                            this.selectedSeats.add(seat.id)
                        }
                    }
                })
                this.updateStats()
                this.render()
            }
            
            selectColumn(colIndex) {
                this.seats.forEach(seat => {
                    if (seat.col === colIndex) {
                        if (this.selectedSeats.has(seat.id)) {
                            this.selectedSeats.delete(seat.id)
                        } else {
                            this.selectedSeats.add(seat.id)
                        }
                    }
                })
                this.updateStats()
                this.render()
            }
            
            clearAll() {
                this.selectedSeats.clear()
                this.updateStats()
                this.render()
            }
            
            updateStats() {
                document.getElementById('seatTotal').textContent = 
                    `当前座位总数：${this.selectedSeats.size}个`
            }
            
            render() {
                this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height)
                
                this.ctx.save()
                this.ctx.translate(this.offsetX, this.offsetY)
                this.ctx.scale(this.zoom, this.zoom)
                
                // 绘制座位
                this.seats.forEach(seat => {
                    const isSelected = this.selectedSeats.has(seat.id)
                    
                    this.ctx.fillStyle = isSelected ? '#3B82F6' : '#E5E7EB'
                    this.ctx.strokeStyle = '#9CA3AF'
                    this.ctx.lineWidth = 1
                    
                    this.ctx.fillRect(seat.x, seat.y, seat.width, seat.height)
                    this.ctx.strokeRect(seat.x, seat.y, seat.width, seat.height)
                    
                    // 绘制座位号
                    if (this.zoom > 0.5) {
                        this.ctx.fillStyle = isSelected ? 'white' : '#374151'
                        this.ctx.font = '12px Arial'
                        this.ctx.textAlign = 'center'
                        this.ctx.textBaseline = 'middle'
                        this.ctx.fillText(
                            `${seat.row + 1}-${seat.col + 1}`,
                            seat.x + seat.width / 2,
                            seat.y + seat.height / 2
                        )
                    }
                })
                
                this.ctx.restore()
                
                // 绘制选择框
                if (this.isSelecting) {
                    const startX = Math.min(this.selectionStart.x, this.selectionEnd.x)
                    const startY = Math.min(this.selectionStart.y, this.selectionEnd.y)
                    const width = Math.abs(this.selectionEnd.x - this.selectionStart.x)
                    const height = Math.abs(this.selectionEnd.y - this.selectionStart.y)
                    
                    this.ctx.strokeStyle = '#3B82F6'
                    this.ctx.fillStyle = 'rgba(59, 130, 246, 0.1)'
                    this.ctx.lineWidth = 2
                    this.ctx.setLineDash([5, 5])
                    
                    this.ctx.fillRect(startX, startY, width, height)
                    this.ctx.strokeRect(startX, startY, width, height)
                    this.ctx.setLineDash([])
                }
            }
            
            zoomIn() {
                this.zoom = Math.min(this.zoom * 1.2, 3)
                this.render()
            }
            
            zoomOut() {
                this.zoom = Math.max(this.zoom / 1.2, 0.3)
                this.render()
            }
            
            getData() {
                const data = {}
                this.selectedSeats.forEach(seatId => {
                    const [row, col] = seatId.split('_').map(Number)
                    const key = `${row + 1}_${col + 1}`
                    data[key] = {
                        row: row + 1,
                        col: col + 1,
                        price: 0,
                        color: '',
                        groupName: '',
                        thre_id: 20,
                        hall_id: 10,
                        movie_id: 30
                    }
                })
                return data
            }
        }
        
        // 全局变量
        let seatDemo = null
        
        // 全局函数
        function zoomIn() {
            if (seatDemo) seatDemo.zoomIn()
        }
        
        function zoomOut() {
            if (seatDemo) seatDemo.zoomOut()
        }
        
        function selectRow(rowIndex) {
            if (seatDemo) seatDemo.selectRow(rowIndex)
        }
        
        function selectCol(colIndex) {
            if (seatDemo) seatDemo.selectColumn(colIndex)
        }
        
        function clearAll() {
            if (seatDemo) seatDemo.clearAll()
        }
        
        function saveData() {
            if (seatDemo) {
                const data = seatDemo.getData()
                localStorage.setItem("seats", JSON.stringify(data))
                alert(`保存成功！选中了 ${Object.keys(data).length} 个座位`)
            }
        }
        
        function goBack() {
            location.href = "step1.html"
        }
        
        function goNext() {
            if (seatDemo) {
                const data = seatDemo.getData()
                localStorage.setItem("seats", JSON.stringify(data))
                location.href = "step3.html"
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            const canvas = document.getElementById('seatCanvas')
            seatDemo = new SimpleSeatDemo(canvas)
        })
    </script>
</body>
</html>
