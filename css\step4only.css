.uploadBg {
	width: 100%;
	overflow: hidden;
	margin-top: 10px;
}

.uploadTips {
	font-size: 13px;
	color: #1890ff;
	margin-top: 5px;
}

.uploadImgBox,
.bgBox {
	width: 100%;
	overflow: hidden;
	margin: 10px auto;
}

.uploadImgBox img {
	width: 150px;
}

.seatNameBox {
	width: 100%;
	overflow: hidden;
	display: flex;
	align-items: center;
	margin-bottom: 10px;
}

.seatNameBox input {
	flex: 1;
	width: 100px;
}

.rangeBox {
	width: 100%;
	overflow: hidden;
	padding: 15px 10px;
	box-sizing: border-box;
}

.rangeBoxTitle {
	width: 100%;
	overflow: hidden;
	font-size: 14px;
	color: #000;
	margin-bottom: 10px;
}

.bgSize {
	margin: 10px auto;
}

.zoomBox {
	position: absolute;
	right: 15px;
	top: 20px;
}

.zoomBox .layui-icon {
	font-size: 25px;
	cursor: pointer;
	color: #666;
}

.freeContainerModule {
	position: relative;
}

.btnModule {
	width: 100%;
	overflow: hidden;
	margin-top: 15px;
}

.btnModule li {
	float: left;
	width: 47%;
	margin-left: 2%;
	height: 70px;
	border: 1px solid #ccc;
	margin-bottom: 15px;
	cursor: pointer;
}

.btnModule li span {
	width: 100px;
	height: 40px;
	display: block;
	margin: 15px auto;
	text-align: center;
	line-height: 40px;
	background: #4078cb;
	color: #fff;
	text-align: center;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.btnModule li:nth-child(2) span {
	width: 100px;
	height: 38px;
	border: 1px solid #4078cb;
	display: block;
	margin: 15px auto;
	text-align: center;
	line-height: 38px;
	text-align: center;
}

.btnModule li:nth-child(3) span {
	width: 100px;
	height: 40px;
	display: block;
	margin: 15px auto;
	text-align: center;
	line-height: 40px;
	text-align: center;
	background: #4078cb;
	color: #fff;
}

.btnModule li:nth-child(4) span {
	width: 60px;
	height: 60px;
	display: block;
	margin: 5px auto;
	text-align: center;
	line-height: 60px;
	text-align: center;
	background: #4078cb;
	color: #fff;
}

.btnModule .btnModuleAct,
.btnModule .btnModuleAct2,
.btnModule .btnModuleAct3 {
	border: 1px solid #4078cb;
}

.content-select-box {
	width: 100%;
	margin: 10px 0;
}

.new-content-top {
	width: 100%;
	overflow: hidden;
	margin-bottom: 10px;
	margin-top: 20px;
}

.content-top-btn {
	float: right;
	overflow: hidden;
}

.main-right {
	height: calc(100vh - 110px);
}

.main-left-tab {
	width: 400px;
	display: flex;
	border: 1px solid #4078cb;
	color: #4078cb;
}

.main-left-tab span {
	flex: 1;
	line-height: 40px;
	text-align: center;
	cursor: pointer;
	font-size: 14px;
}

.main-left-tab span.main-left-tab-act {
	background: #4078cb;
	color: #fff;
}

.layui-btn {
	background: #4078cb !important;
}

.layui-slider-wrap-btn {
	border-color: #4078cb !important;
}

.layui-slider-bar {
	background: #4078cb !important;
}