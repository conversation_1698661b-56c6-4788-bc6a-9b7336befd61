.main-right {
	overflow: hidden;
	overflow-y: auto;
	height: calc(100vh - 130px);
}

.groupRulesUlBox {
	width: 100%;
	overflow: hidden;
}

.groupRulesUlBox li {
	width: 100%;
	overflow: hidden;
	display: flex;
	align-items: center;
	margin-bottom: 20px;
}

.groupRulesUlBox li .colorSpan {
	width: 20px;
	height: 20px;
	margin-right: 5px;
}

body .seatBox .seat-table tr .selected {
	background-color: none !important;
	background-image: url(../images/selectIcon.png) !important;
	background-position: 0 0 !important;
	background-size: 100% 100% !important;
}

.seatBoxContent {
	position: relative;
}

.zoomBox {
	position: absolute;
	right: 15px;
	top: 20px;
}

.zoomBox .layui-icon {
	font-size: 25px;
	cursor: pointer;
	color: #666;
}

.new-content-top {
	width: 100%;
	overflow: hidden;
	margin-bottom: 10px;
	margin-top: 10px;
}

.content-top-btn {
	float: right;
	overflow: hidden;
}

.edit-module-box {
	width: 100%;
	overflow: initial;
	margin: 20px auto;
	padding: 0 15px;
	box-sizing: border-box;
}

.edit-module-flex {
	width: 100%;
	display: flex;
	align-items: center;
	margin: 20px auto;
}

.select-box {
	flex: 1;
	margin-right: 10px;
}

.module-tips {
	width: 100%;
	overflow: hidden;
	font-size: 13px;
	color: #4078cb;
}

.module-tips-text {
	width: 100%;
	overflow: hidden;
	margin: 5px auto;
}

.file-list {
	width: 100%;
	overflow: hidden;
	margin-top: -10px;
}

.file-list li {
	width: 100%;
	display: flex;
	padding: 5px 10px;
	border: 1px solid #ddd;
	box-sizing: border-box;
	align-items: center;
	margin-bottom: 10px;
}

.file-list li .file-list-text {
	flex: 1;
	overflow: hidden;
	margin-right: 5px;
}

.file-list li .layui-icon {
	font-size: 24px;
	color: red;
	cursor: pointer;
}

.loading-module-box {
	width: 100%;
	overflow: hidden;
	padding: 100px 15px;
	box-sizing: border-box;
	text-align: center;
}

.loading-module-text {
	display: inline-block;
	font-size: 14px;
	vertical-align: top;
	margin: 45px 0 0 15px;
}

.loading-img {
	width: 100px;
}

.loadingSix {
	width: 100px;
	height: 100px;
	position: relative;
	margin: 0 auto;
	display: inline-block;
}

.loadingSix span {
	display: inline-block;
	width: 16px;
	height: 16px;
	border-radius: 50%;
	background: #4078cb;
	position: absolute;
	-webkit-animation: load 1.04s ease infinite;
}

@-webkit-keyframes load {
	0% {
		opacity: 1;
	}

	100% {
		opacity: 0.2;
	}
}

.loadingSix span:nth-child(1) {
	left: 0;
	top: 50%;
	margin-top: -8px;
	-webkit-animation-delay: 0.13s;
}

.loadingSix span:nth-child(2) {
	left: 14px;
	top: 14px;
	-webkit-animation-delay: 0.26s;
}

.loadingSix span:nth-child(3) {
	left: 50%;
	top: 0;
	margin-left: -8px;
	-webkit-animation-delay: 0.39s;
}

.loadingSix span:nth-child(4) {
	top: 14px;
	right: 14px;
	-webkit-animation-delay: 0.52s;
}

.loadingSix span:nth-child(5) {
	right: 0;
	top: 50%;
	margin-top: -8px;
	-webkit-animation-delay: 0.65s;
}

.loadingSix span:nth-child(6) {
	right: 14px;
	bottom: 14px;
	-webkit-animation-delay: 0.78s;
}

.loadingSix span:nth-child(7) {
	bottom: 0;
	left: 50%;
	margin-left: -8px;
	-webkit-animation-delay: 0.91s;
}

.loadingSix span:nth-child(8) {
	bottom: 14px;
	left: 14px;
	-webkit-animation-delay: 1.04s;
}