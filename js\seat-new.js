/**
 * 新的座位系统 - 保持原有接口完全不变
 * 使用Canvas引擎替换DOM实现，但对外API完全兼容
 */

// 导入新的引擎和适配器
import { CompatibleSeatWrapper } from '../CompatibleSeatWrapper.js'

// 保持原有的$.seats接口
(function($) {
    // 替换原有的$.seats函数，但保持接口完全一致
    $.seats = function(options) {
        return new CompatibleSeatWrapper(options)
    }
    
    // 保持原有的hasSelected方法
    $.fn.hasSelected = function() {
        return $(this).find('span').hasClass('selected')
    }
    
})(jQuery)

// 保持原有的全局函数
function zoomFn(direction) {
    // 获取当前的座位实例
    const seatBox = $("#seatBox")
    if (seatBox.length === 0) return
    
    const canvas = seatBox.find('canvas')[0]
    if (!canvas || !canvas.seatEngine) return
    
    const engine = canvas.seatEngine
    
    if (direction === 'in') {
        engine.zoomOut() // 缩小
    } else if (direction === 'out') {
        engine.zoomIn()  // 放大
    }
}

// 保持原有的getScaleplate函数
function getScaleplate() {
    // 这个函数在新实现中由CompatibleSeatWrapper自动处理
    // 保留空函数以防止错误
}

// 保持原有的其他全局函数
window.zoomFn = zoomFn
window.getScaleplate = getScaleplate

// 确保在DOM加载完成后初始化
$(document).ready(function() {
    // 如果页面有座位容器，确保样式正确
    const seatBox = $("#seatBox")
    if (seatBox.length > 0) {
        // 确保容器样式适合Canvas
        seatBox.css({
            'overflow': 'hidden',
            'position': 'relative',
            'background': 'white'
        })
    }
    
    // 保持原有的统计更新逻辑
    $(document).on("click", '.main-left', function() {
        // 这个事件会被CompatibleSeatWrapper触发
        // 保留原有的统计显示逻辑
    })
})

// 导出兼容性包装器供其他模块使用
window.CompatibleSeatWrapper = CompatibleSeatWrapper
