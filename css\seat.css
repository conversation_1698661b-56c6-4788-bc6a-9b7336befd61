.c_right {
	background: #fff;
}

body {
	text-align: center;
	overflow: auto;
	padding: 0;
}

body .animated-seat {
	animation-duration: .9s;
	animation-fill-mode: both;
}

.main-content {
	width: 100%;
	overflow: hidden;
	height: calc(100vh - 100px);
	display: flex;
	box-sizing: border-box;
}

.main-left {
	flex: 1;
	/* width: 800px; */
	height: 100%;
	overflow-y: auto;
	overflow-x: auto;
	padding: 15px 15px;
	box-sizing: border-box;
}

.main-right {
	width: 380px;
	height: 100%;
	overflow-y: auto;
	border-left: 1px solid #ddd;
}

@keyframes zoomIn {
	from {
		opacity: 0;
		transform: scale3d(0.3, 0.3, 0.3);
	}

	50% {
		opacity: 1;
	}
}

body .seatBox {
	background-color: #f5f5f5;
	padding: 10px;
	position: relative;
	margin: 30px auto;
	box-sizing: content-box;
}

.dragBox {
	border: 1px solid green;
	position: absolute;
}

body .seatBox .seat-table {
	margin: auto auto 20px auto;
	padding: 0;
	/* animation-name: zoomIn; */
}

body .seatBox .seat-table tr td {
	padding: 2px;
	margin: 0;
}

body .seatBox .seat-table tr .seat {
	display: block;
	/* border: 1px solid #cccccc; */
	cursor: pointer;
	background-color: #ffffff;
	border-radius: 3px;
	box-sizing: border-box;
	margin: 4px 0;
}

body .seatBox .seat-table tr .selected {
	background-color: #B9DEA0;
}

body .seatBox .seat-table tr .disabled {
	background: url("checked.png") no-repeat center center;
	background-size: 13px 13px !important;
	border: 1px solid #cc0000 !important;
}

.layui-tab {
	margin-top: 0;
}

.layui-tab-content {
	padding: 20px 10px;
	box-sizing: border-box;
}

.bottom-btn {
	width: 100%;
	overflow: hidden;
	text-align: center;
	margin-top: 30px;
}