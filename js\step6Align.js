// 监听freeContainerModuleOther 内框选组件

// block -  freeDragItem 

document.getElementById('freeContainerModuleOther').addEventListener('mousedown', function(e) {
	if (e.target.classList.contains('freeDragItem')) {
		return;
	}

	// 点击空白区域时取消所有方块的选中状态
	document.querySelectorAll('.freeDragItem').forEach(b => b.classList.remove('highlight'));
	document.querySelectorAll('.freeDragItem').forEach(b => b.classList.remove('dragItemClicked'));

	isSelecting = true;
	const container = document.getElementById('freeContainerModuleOther');
	const startX = e.clientX - container.getBoundingClientRect().left;
	const startY = e.clientY - container.getBoundingClientRect().top;

	let selectionBox = document.createElement('div');
	selectionBox.classList.add('selection-box');
	selectionBox.style.left = startX + 'px';
	selectionBox.style.top = startY + 'px';
	container.appendChild(selectionBox);

	document.addEventListener('mousemove', onMouseMove);
	document.addEventListener('mouseup', onMouseUp, {
		once: true
	});

	function onMouseMove(event) {
		const currentX = event.clientX - container.getBoundingClientRect().left;
		const currentY = event.clientY - container.getBoundingClientRect().top;

		selectionBox.style.left = Math.min(startX, currentX) + 'px';
		selectionBox.style.top = Math.min(startY, currentY) + 'px';
		selectionBox.style.width = Math.abs(currentX - startX) + 'px';
		selectionBox.style.height = Math.abs(currentY - startY) + 'px';
	}

	function onMouseUp() {
		isSelecting = false;
		const selectionRect = selectionBox.getBoundingClientRect();
		container.querySelectorAll('.freeDragItem').forEach(block => {
			const blockRect = block.getBoundingClientRect();
			if (
				blockRect.left >= selectionRect.left &&
				blockRect.right <= selectionRect.right &&
				blockRect.top >= selectionRect.top &&
				blockRect.bottom <= selectionRect.bottom
			) {
				block.classList.add('highlight');
			}
		});
		selectionBox.remove();
		document.removeEventListener('mousemove', onMouseMove);
	}
});


function alignTop() {
	const highlightedBlocks = Array.from(document.querySelectorAll('.highlight'));
	if (highlightedBlocks.length === 0) return;
	const minTop = Math.min(...highlightedBlocks.map(b => b.offsetTop));
	highlightedBlocks.forEach(b => {
		b.style.top = minTop + 'px';
	});
}

function alignBottom() {
	const highlightedBlocks = Array.from(document.querySelectorAll('.highlight'));
	if (highlightedBlocks.length === 0) return;
	const maxBottom = Math.max(...highlightedBlocks.map(b => b.offsetTop + b.offsetHeight));
	highlightedBlocks.forEach(b => {
		b.style.top = (maxBottom - b.offsetHeight) + 'px';
	});
}

function alignLeft() {
	const highlightedBlocks = Array.from(document.querySelectorAll('.highlight'));
	if (highlightedBlocks.length === 0) return;
	const minLeft = Math.min(...highlightedBlocks.map(b => b.offsetLeft));
	highlightedBlocks.forEach(b => {
		b.style.left = minLeft + 'px';
	});
}

function alignRight() {
	const highlightedBlocks = Array.from(document.querySelectorAll('.highlight'));
	if (highlightedBlocks.length === 0) return;
	const maxRight = Math.max(...highlightedBlocks.map(b => b.offsetLeft + b.offsetWidth));
	highlightedBlocks.forEach(b => {
		b.style.left = (maxRight - b.offsetWidth) + 'px';
	});
}