<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试数据检查</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .data-item { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 数据调试检查</h1>
        
        <button onclick="checkData()">检查数据</button>
        <button onclick="setTestData()">设置测试数据</button>
        <button onclick="clearAllData()">清除所有数据</button>
        <button onclick="testStep2()">测试Step2</button>
        
        <div id="results"></div>
    </div>

    <script>
        function checkData() {
            const results = document.getElementById('results');
            let html = '<h2>当前localStorage数据:</h2>';
            
            // 检查基础数据
            const hallName = localStorage.getItem('hallName');
            const seatRows = localStorage.getItem('seatRows');
            const seatCols = localStorage.getItem('seatCols');
            
            html += `<div class="data-item ${seatRows && seatCols ? 'success' : 'error'}">`;
            html += `<strong>基础数据:</strong><br>`;
            html += `场地名称: ${hallName || '未设置'}<br>`;
            html += `行数: ${seatRows || '未设置'}<br>`;
            html += `列数: ${seatCols || '未设置'}`;
            html += `</div>`;
            
            // 检查座位数据
            const seats = localStorage.getItem('seats');
            if (seats) {
                try {
                    const seatsData = JSON.parse(seats);
                    const seatCount = Object.keys(seatsData).length;
                    html += `<div class="data-item success">`;
                    html += `<strong>座位数据:</strong> ${seatCount}个座位<br>`;
                    html += `<pre>${JSON.stringify(seatsData, null, 2).substring(0, 500)}...</pre>`;
                    html += `</div>`;
                } catch (e) {
                    html += `<div class="data-item error">座位数据格式错误: ${e.message}</div>`;
                }
            } else {
                html += `<div class="data-item">座位数据: 未设置</div>`;
            }
            
            // 检查分区数据
            const zones = localStorage.getItem('zones');
            if (zones) {
                try {
                    const zonesData = JSON.parse(zones);
                    html += `<div class="data-item success">`;
                    html += `<strong>分区数据:</strong> ${zonesData.length}个分区<br>`;
                    html += `<pre>${JSON.stringify(zonesData, null, 2)}</pre>`;
                    html += `</div>`;
                } catch (e) {
                    html += `<div class="data-item error">分区数据格式错误: ${e.message}</div>`;
                }
            } else {
                html += `<div class="data-item">分区数据: 未设置</div>`;
            }
            
            results.innerHTML = html;
        }
        
        function setTestData() {
            // 设置最基本的测试数据
            localStorage.setItem('hallName', '测试影厅');
            localStorage.setItem('seatRows', '5');
            localStorage.setItem('seatCols', '8');
            
            // 清除可能有问题的座位和分区数据
            localStorage.removeItem('seats');
            localStorage.removeItem('zones');
            
            alert('✅ 测试数据已设置：5行×8列，无座位数据');
            checkData();
        }
        
        function clearAllData() {
            localStorage.clear();
            alert('✅ 所有数据已清除');
            checkData();
        }
        
        function testStep2() {
            const seatRows = localStorage.getItem('seatRows');
            const seatCols = localStorage.getItem('seatCols');
            
            if (!seatRows || !seatCols) {
                alert('❌ 请先设置测试数据');
                return;
            }
            
            // 在新窗口打开Step2，这样可以看到控制台输出
            window.open('step2.html', '_blank');
        }
        
        // 页面加载时自动检查
        window.onload = checkData;
    </script>
</body>
</html>
