/*$(function() {*/
function moveXY1() {
	$(document).mousemove(function(e) {
		if (!!this.move) {
			var posix = !document.move_target ? {
					'x': 0,
					'y': 0
				} : document.move_target.posix,
				callback = document.call_down || function() {
					$(this.move_target).css({
						'left': '0'
					});
				};

			callback.call(this, e, posix);
		}
	}).mouseup(function(e) {
		if (!!this.move) {
			var callback = document.call_up || function() {};
			callback.call(this, e);
			$.extend(this, {
				'move': false,
				'move_target': null,
				'call_down': false,
				'call_up': false
			});
		}
	});
	/*文字*/
	var $box = $('.freecClicked .wenziBox').mousedown(function(e) {

		// console.log($(this).html())

		var offset = $(this).offset();

		this.posix = {
			'x': e.pageX - offset.left,
			'y': e.pageY - offset.top
		};
		$.extend(document, {
			'move': true,
			'move_target': this
		});
		// ,.coor_right,.coor_topR,.coor_left,.coor_leftTop,.coor_leftBottom
	}).on('mousedown', '.freecClicked .coor1', function(e) {
		var posix = {
			'w': $box.width(),
			'h': $box.height(),
			'x': e.pageX,
			'y': e.pageY
		};

		$.extend(document, {
			'move': true,
			'call_down': function(e) {
				$box.css({
					'width': Math.max(30, e.pageX - posix.x + posix.w),
					'height': Math.max(30, e.pageY - posix.y + posix.h)
				});
			}
		});
		return false;
	});
};
// 自由容器图片
function moveXY2() {
	$(document).mousemove(function(e) {
		if (!!this.move) {
			var posix = !document.move_target ? {
					'x': 0,
					'y': 0
				} : document.move_target.posix,
				callback = document.call_down || function() {
					$(this.move_target).css({
						'left': '0'
					});
				};

			callback.call(this, e, posix);
		}
	}).mouseup(function(e) {
		if (!!this.move) {
			var callback = document.call_up || function() {};
			callback.call(this, e);
			$.extend(this, {
				'move': false,
				'move_target': null,
				'call_down': false,
				'call_up': false
			});
		}
	});
	/*图片*/
	var $box = $('.freecClicked .freeImgBox').mousedown(function(e) {

		var offset = $(this).offset();

		this.posix = {
			'x': e.pageX - offset.left,
			'y': e.pageY - offset.top
		};
		$.extend(document, {
			'move': true,
			'move_target': this
		});
		// ,.coor_right,.coor_topR,.coor_left,.coor_leftTop,.coor_leftBottom
	}).on('mousedown', '.freecClicked .coor2', function(e) {
		var posix = {
			'w': $box.width(),
			'h': $box.height(),
			'x': e.pageX,
			'y': e.pageY
		};

		$.extend(document, {
			'move': true,
			'call_down': function(e) {
				$box.css({
					'width': Math.max(30, e.pageX - posix.x + posix.w),
					'height': Math.max(30, e.pageY - posix.y + posix.h)
				});
			}
		});
		return false;
	});
};
// 自由容器按钮
function moveXY3() {
	$(document).mousemove(function(e) {
		if (!!this.move) {
			var posix = !document.move_target ? {
					'x': 0,
					'y': 0
				} : document.move_target.posix,
				callback = document.call_down || function() {
					$(this.move_target).css({
						'left': '0'
					});
				};

			callback.call(this, e, posix);
		}
	}).mouseup(function(e) {
		if (!!this.move) {
			var callback = document.call_up || function() {};
			callback.call(this, e);
			$.extend(this, {
				'move': false,
				'move_target': null,
				'call_down': false,
				'call_up': false
			});
		}
	});
	/*按钮*/
	var $box = $('.dragItemClicked .freeBtbBox').mousedown(function(e) {
		var offset = $(this).offset();

		this.posix = {
			'x': e.pageX - offset.left,
			'y': e.pageY - offset.top
		};
		$.extend(document, {
			'move': true,
			'move_target': this
		});
		// ,.coor_right,.coor_topR,.coor_left,.coor_leftTop,.coor_leftBottom
	}).on('mousedown', '.coorClick.coor3', function(e) {
		var posix = {
			'w': $box.width(),
			'h': $box.height(),
			'x': e.pageX,
			'y': e.pageY
		};

		$.extend(document, {
			'move': true,
			'call_down': function(e) {
				$box.css({
					'width': Math.max(30, e.pageX - posix.x + posix.w),
					'height': Math.max(30, e.pageY - posix.y + posix.h)
				});
			}
		});
		return false;
	});
};

function all() {
	moveXY1();
	moveXY2();
	moveXY3();
}
all();
/*});*/