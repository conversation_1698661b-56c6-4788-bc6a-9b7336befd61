/**
 * 兼容性适配器 - 保持原项目接口完全不变
 * 内部使用新的Canvas引擎，但对外提供完全相同的API
 */

import { SeatMapEngine, SeatStatus } from './SeatMapEngine.js'

// 兼容性包装器类
class CompatibleSeatWrapper {
    constructor(options) {
        this.options = options
        this.seats = {} // 原项目的数据格式
        this.seatColors = {} // 用于Step2的选择状态
        this.ticketNumber = 0
        
        // 创建Canvas元素
        this.canvas = this.createCanvas()
        
        // 初始化引擎
        this.engine = new SeatMapEngine(this.canvas, {
            seatSize: options.size || 20,
            seatSpacing: 4,
            onSeatClick: this.handleSeatClick.bind(this),
            onSeatHover: this.handleSeatHover.bind(this),
            onViewChange: this.handleViewChange.bind(this)
        })

        // 将引擎保存到canvas元素上，供全局函数访问
        this.canvas.seatEngine = this.engine
        
        // 生成座位
        this.generateSeats()
        
        // 加载已有数据
        if (options.datas && Object.keys(options.datas).length > 0) {
            this.loadExistingData(options.datas)
        }
        
        // 创建行列标尺
        this.createRowColHeaders()
    }
    
    createCanvas() {
        // 找到目标容器
        const container = document.querySelector(this.options.box)
        if (!container) {
            throw new Error(`Container ${this.options.box} not found`)
        }

        // 清空容器
        container.innerHTML = ''

        // 创建Canvas
        const canvas = document.createElement('canvas')
        canvas.width = 800
        canvas.height = 500
        canvas.style.display = 'block'
        canvas.style.background = 'white'
        canvas.style.cursor = 'grab'

        // 添加到容器
        container.appendChild(canvas)

        return canvas
    }
    
    generateSeats() {
        // 使用引擎生成座位
        this.engine.generateSeats(this.options.rows, this.options.cols)
        
        // 初始化seats数据结构（原项目格式）
        this.seats = {}
        for (let row = 1; row <= this.options.rows; row++) {
            for (let col = 1; col <= this.options.cols; col++) {
                const key = `${row}_${col}`
                // 注意：这里不添加到seats中，只有被选中的座位才会添加
            }
        }
    }
    
    loadExistingData(datas) {
        // 加载原项目的数据格式
        this.seats = { ...datas }
        
        // 转换为引擎格式并设置状态
        Object.entries(datas).forEach(([key, seatData]) => {
            const [row, col] = key.split('_').map(Number)
            const engineSeat = this.engine.seats.find(s => 
                s.row === row - 1 && s.col === col - 1
            )
            
            if (engineSeat) {
                engineSeat.status = SeatStatus.SELECTED
                engineSeat.price = seatData.price || 0
                engineSeat.groupName = seatData.groupName || ''
                engineSeat.groupColor = seatData.color || ''
            }
        })
        
        this.engine.render()
    }
    
    createRowColHeaders() {
        const container = document.querySelector(this.options.box).parentElement

        // 创建列标尺
        const colHeaders = document.createElement('div')
        colHeaders.className = 'tdBox'
        for (let i = 1; i <= this.options.cols; i++) {
            const span = document.createElement('span')
            span.textContent = i
            colHeaders.appendChild(span)
        }
        container.insertBefore(colHeaders, container.firstChild)

        // 创建行标尺
        const rowHeaders = document.createElement('div')
        rowHeaders.className = 'trBox'
        for (let i = 1; i <= this.options.rows; i++) {
            const span = document.createElement('span')
            span.textContent = i
            rowHeaders.appendChild(span)
        }
        const seatBox = document.querySelector(this.options.box)
        seatBox.parentElement.insertBefore(rowHeaders, seatBox)

        // 绑定行列点击事件（保持原有行为）
        this.bindRowColEvents()
    }
    
    bindRowColEvents() {
        // 列点击事件
        document.addEventListener('click', (e) => {
            if (e.target.matches('.tdBox span')) {
                const colIndex = parseInt(e.target.textContent) - 1
                this.engine.selectColumn(colIndex)
                this.updateSeatsData()
            }
        })

        // 行点击事件
        document.addEventListener('click', (e) => {
            if (e.target.matches('.trBox span')) {
                const rowIndex = parseInt(e.target.textContent) - 1
                this.engine.selectRow(rowIndex)
                this.updateSeatsData()
            }
        })
    }
    
    handleSeatClick(seat) {
        // 更新原项目的数据格式
        this.updateSeatsData()
        
        // 调用原有回调
        if (this.options.onSelected) {
            const seatData = {
                row: seat.row + 1,
                col: seat.col + 1,
                thre_id: this.options.thre_id,
                hall_id: this.options.hall_id,
                movie_id: this.options.movie_id,
                price: seat.price || 0,
                groupName: seat.groupName || '',
                color: seat.groupColor || ''
            }
            this.options.onSelected(seatData)
        }
    }
    
    handleSeatHover(seat) {
        if (this.options.onSeatHover) {
            this.options.onSeatHover(seat)
        }
    }
    
    handleViewChange(view) {
        if (this.options.onViewChange) {
            this.options.onViewChange(view)
        }
    }
    
    updateSeatsData() {
        // 根据引擎状态更新原项目数据格式
        this.seats = {}
        this.seatColors = {}

        this.engine.seats.forEach(seat => {
            if (seat.status === SeatStatus.SELECTED) {
                const key = `${seat.row + 1}_${seat.col + 1}`

                this.seats[key] = {
                    row: seat.row + 1,
                    col: seat.col + 1,
                    price: seat.price || 0,
                    color: seat.groupColor || '',
                    groupName: seat.groupName || '',
                    thre_id: this.options.thre_id || 0,
                    hall_id: this.options.hall_id || 0,
                    movie_id: this.options.movie_id || 0
                }

                // Step2需要的seatColors
                if (this.options.step === 2 || this.options.step === 101) {
                    this.seatColors[key] = 1
                }
            }
        })

        // 更新统计
        this.updateStats()

        // 触发原项目的事件
        this.triggerLegacyEvents()
    }

    triggerLegacyEvents() {
        // 模拟原项目的点击事件，更新统计显示
        $(document).trigger('click', '.main-left')
    }
    
    updateStats() {
        const selectedCount = Object.keys(this.seats).length
        const seatTotalElement = document.getElementById('seatTotal')
        if (seatTotalElement) {
            seatTotalElement.innerHTML = ` 当前座位总数：${selectedCount}个`
        }

        const groupNumberElement = document.getElementById('groupNumber')
        if (groupNumberElement) {
            groupNumberElement.textContent = Object.keys(this.seatColors).length
        }
    }
    
    // ========== 原项目的公共接口 ==========
    
    // 获取所有座位信息（保持原接口）
    getSeats() {
        this.updateSeatsData()
        return this.seats
    }
    
    // 获取已选择的座位（保持原接口）
    getSelectedSeats() {
        return this.options.selected || {}
    }
    
    // 设置价格（保持原接口）
    setPrice(price, color, groupName) {
        if (Object.keys(this.seatColors).length === 0) {
            return this.options.onerror && this.options.onerror("你没有选中任何座位")
        }
        
        // 更新引擎中的座位数据
        this.engine.seats.forEach(seat => {
            if (seat.status === SeatStatus.SELECTED) {
                seat.price = price
                seat.groupColor = color
                seat.groupName = groupName
            }
        })
        
        // 更新原项目数据
        Object.keys(this.seatColors).forEach(key => {
            if (this.seats[key]) {
                this.seats[key].color = color
                this.seats[key].price = price
                this.seats[key].groupName = groupName
            }
        })
        
        this.seatColors = {} // 清空选择状态
        this.engine.render()
    }
    
    // 释放某个座位（保持原接口）
    unselect(row, col) {
        const key = `${row}_${col}`
        delete this.options.selected[key]
        
        // 更新引擎状态
        const engineSeat = this.engine.seats.find(s => 
            s.row === row - 1 && s.col === col - 1
        )
        if (engineSeat) {
            engineSeat.status = SeatStatus.AVAILABLE
        }
        
        this.ticketNumber--
        this.engine.render()
    }
}

// 替换原有的$.seats函数
window.CompatibleSeatWrapper = CompatibleSeatWrapper

export { CompatibleSeatWrapper }
