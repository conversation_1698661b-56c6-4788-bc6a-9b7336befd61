<!DOCTYPE html>
<html lang="ch">
	<head>
		<title>区域视图设置</title>
		<!-- 设置 viewport -->
		<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
		<!-- IE -->
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<!-- 兼容国产浏览器的高速模式 -->
		<meta name="renderer" content="webkit">
		<meta name="Author" content="<EMAIL>" />
		<meta name="Keywords" content="" />
		<meta name="Description" content="" />
		<meta charset="UTF-8">
		<link rel="stylesheet" type="text/css" href="css/reset.css">
		<link rel="stylesheet" href="css/translateCommon.css">
		<link rel="stylesheet" href="layui/css/layui.css" />
		<link rel="stylesheet" href="css/stepPlug.css" />
		<link rel="stylesheet" href="plugin/spectrum/spectrum.css">
		<link rel="stylesheet" href="css/modalbox.css" />
		<link rel="stylesheet" href="css/seat3.css" />
		<link rel="stylesheet" href="css/dragModule.css" />
		<link rel="stylesheet" href="css/step6only.css" />

	</head>
	<body>
		<div id="section">
			<!-- header -->
			<div id="header" style="margin-top: 40px;">
				<div class="hui">
					<img src="images/hui.jpg" alt="">
				</div>
				<div class="logo">
					<h1 class="f_left">
						<a href="#">
							<img src="images/logo.png" alt=""></a>
					</h1>
					<div class="f_right logo_r">
						<ul class="logo_r_show">
							<!-- <li class="search">
								<p>
									<span class="f_left">受邀者</span>
									<input class="f_left" type="text"> <i></i>
								</p>
							</li> -->
							<li class="translate-box">
								<a class="translate-box-cn" href="javascript:translate.changeLanguage('english');javascript:void(0);">
									<img src="images/translateEn.png" alt="">
								</a>
								<a class="translate-box-en" style="display: none;"
									href="javascript:translate.changeLanguage('chinese_simplified');javascript:void(0);">
									<img src="images/translateCn.png" alt="">
								</a>
							</li>
							<li class="h_qiandao">
								<a href="#">微信签到</a>
							</li>
							<li class="prompt">
								<a href="#">
									<span>0</span>
								</a>
							</li>
							<li class="help">帮助与文档</li>
							<li class="tel">18621589099</li>
						</ul>
					</div>
				</div>
			</div>

			<!-- content -->
			<div id="content" class="clearfix">
				<!-- 左边导航 -->
				<div class="c_left">
					<ul class="c_left_nav f_left">
						<li class="current" title="活动">
							<img src="images/icon6.png" alt="">
						</li>
						<li title="应用">
							<img src="images/icon7.png" alt="">
						</li>
						<li title="联系人">
							<img src="images/icon8.png" alt="">
						</li>
						<li title="供应商">
							<img src="images/icon9.png" alt="">
						</li>
						<li title="账户">
							<img src="images/icon10.png" alt="">
						</li>
					</ul>

				</div>

				<!-- 主要模块 -->
				<div class="c_right">
					<div class="main">
						<div class="new-content-top">
							<div class="content-top-btn">
								<button class="layui-btn" type="button" onclick="">返回</button>
								<button class="layui-btn" type="button" onclick="">保存</button>
								<button class="layui-btn" type="button" onclick="viewMySeat()">预览</button>
							</div>
						</div>
						<div class="main-content">
							<div class="main-left">
								<!-- <div class="step-html" style="width: 600px;">
									<div class="step-box">
										<div class="step-list step-act">
											<span class="step-number">1</span>
										</div>
										<div class="step-list-html-text step-act">设置场地排列</div>
										<div class="step-line"></div>
										<div class="step-list step-act">
											<span class="step-number step-act">2</span>
										</div>
										<div class="step-list-html-text step-act">设置场地布局</div>
										<div class="step-line"></div>
										<div class="step-list step-act">
											<span class="step-number step-act">3</span>
										</div>
										<div class="step-list-html-text step-act">设置分区</div>
										<div class="step-line"></div>
										<div class="step-list step-act">
											<span class="step-number step-act">4</span>
										</div>
										<div class="step-list-html-text step-act">设置效果图</div>
									</div>
								</div> -->

								<!-- <div class="main-left-tab">
									<span onclick="viewSrc('step4.html')">分区引导图设置</span>
									<span onclick="viewSrc('step5.html')">座位视图设置</span>
									<span class="main-left-tab-act" onclick="viewSrc('step6.html')">区域视图设置</span>
								</div> -->

								<div class="zoomBox">
									<i class="layui-icon layui-icon-reduce-circle" onclick="zoomFn('in')"></i>
									<i class="layui-icon layui-icon-add-circle" onclick="zoomFn('out')"></i>
								</div>
								<!-- <div
									style="width: 100%;text-align: center;font-size: 25px;color: #000;font-weight: bold;margin-top: 15px;letter-spacing: 5px;"
									id="mySeat">
								</div> -->
								<!-- 座位遮罩层 -->
								<!-- <div class="seatFixed"></div> -->
								<div class="seatBoxContent seatBoxContentSetp4" id="seatBoxContent">



									<div class="seatBox" id="seatBox" style="width: auto;background: #e5e5e5;" zoom="1">

										<div class="dragContent">
											<div class="dragContent freeContainerModule">

												<!-- <div class="ui-draggable freeDragItem " id="timestamp" onclick="freeDragli(this,event)">
																								<div name="paper">
																									<div style="position: relative;" class="freeBtbBox">
																										<div class="freeweb_btn"><a src="#">组件</a></div>
																										<div class="coor3 "></div>
																										<img class="del_img" src="images/delete_hover.png" onclick="freeDeleteImg(this)" />
																									</div>
																								</div>
																							</div> -->

												<div class="ui-draggable freeDragItem ui-draggable-handle" name="a1区"
													style="top: 16px; left: 12px; opacity: 1;" id="m1724920369337"
													onclick="freeDragli(this,event)">
													<div name="paper">
														<div style="position: relative; width: 957px; height: 274px; left: 0px;" class="freeBtbBox">
															<div class="freeweb_btn"><a src="#"
																	style="border-radius: 0px; font-size: 14px; background: rgb(255, 0, 255);">a1区</a>
															</div>
															<div class="coor3"></div>
															<img class="del_img" src="images/delete_hover.png" onclick="freeDeleteImg(this)">
														</div>
													</div>
												</div>
												<div class="ui-draggable freeDragItem ui-draggable-handle" name="a2区"
													style="top: 298px; left: 54px; opacity: 1;" id="m1724920389970"
													onclick="freeDragli(this,event)">
													<div name="paper">
														<div style="position: relative; left: 0px; width: 917px; height: 58px;" class="freeBtbBox">
															<div class="freeweb_btn"><a src="#"
																	style="border-radius: 0px; font-size: 14px; background: rgb(65, 198, 60);">a2区</a>
															</div>
															<div class="coor3"></div>
															<img class="del_img" src="images/delete_hover.png" onclick="freeDeleteImg(this)">
														</div>
													</div>
												</div>
												<div class="ui-draggable freeDragItem ui-draggable-handle dragItemClicked" name="a3区"
													style="top: 364px; left: 898px; opacity: 1;" id="m1724920407449"
													onclick="freeDragli(this,event)">
													<div name="paper">
														<div style="position: relative; left: 0px; width: 72px; height: 184px;" class="freeBtbBox">
															<div class="freeweb_btn"><a src="#"
																	style="border-radius: 0px; font-size: 14px; background: rgb(255, 0, 0);">a3区</a></div>
															<div class="coor3 coorClick"></div>
															<img class="del_img" src="images/delete_hover.png" onclick="freeDeleteImg(this)">
														</div>
													</div>
												</div>
											</div>
										</div>

									</div>

								</div>
								<!-- <div class="bottom-btn">
									<button class="layui-btn" type="button" onclick="editSeat()">修改区域</button>
									<button class="layui-btn" type="button" onclick="">保存</button>
									<button class="layui-btn" type="button" onclick="viewMySeat()">预览</button>
								</div> -->
							</div>
							<div class="main-right" style="display: none;">
								<div class="container">
									<div class="layui-tab" lay-filter="tab-hash">
										<ul class="layui-tab-title">
											<li lay-id="11" class="layui-this">模块设置</li>
											<li lay-id="22">展示设置</li>
										</ul>
										<div class="layui-tab-content">

											<div class="layui-tab-item layui-show">
												<!-- 模块设置 -->
												<div class="settingContent">
													<h4 class="text-left">模块内容</h4>
													<div class="content-input">
														<div class="content-input-box">
															<input id="buttonModule" type="text" class="layui-input" value=""
																onchange="btnValue(this,'.dragItemClicked .freeweb_btn a')"
																onkeyup="btnValue(this,'.dragItemClicked .freeweb_btn a')">
														</div>
													</div>
													<hr />
													<h4 class="text-left">分配区域高亮颜色</h4>
													<div class="flex-box">
														<div class="flex-list">
															宽：<input type="number" class="layui-input moduleWidth"
																onchange="moduleSize(this,'width','.dragItemClicked .freeBtbBox')">
														</div>
														<div class="flex-list">
															高：<input type="number" class="layui-input moduleHeight"
																onchange="moduleSize(this,'height','.dragItemClicked .freeBtbBox')">
														</div>
													</div>
													<hr />
													<h4 class="text-left">模块颜色</h4>
													<div class="content-input">
														<!-- <div class="content-input-box">
															<input type="text" class="form-control" id="btnColor">
														</div> -->
														<div id="moduleBgColor"></div>
													</div>
													<hr />
													<h4 class="text-left">模块圆角</h4>
													<div class="rangeBoxSetting">
														<div id="rangeBorderRadius" class="rangeItem"></div>
													</div>
													<hr />
													<h4 class="text-left">模块文字颜色</h4>
													<div class="content-input">
														<!-- <div class="content-input-box">
															<input type="text" class="form-control" id="btnTextColor">
														</div> -->
														<div id="btnTextColor"></div>
													</div>
													<hr />
												</div>
												<!-- addModuleItem() -->
												<div class="bottom-btn">
													<button id="batchAllocation" class="layui-btn" type="button"
														onclick="addNewModuleSelect()">新增模块</button>
													<button class="layui-btn" type="button" onclick="copyNewModuleSelect()">复制模块</button>
												</div>

											</div>
											<!-- 展示设置 -->
											<div class="layui-tab-item">
												<h4 class="text-left">非分区域颜色</h4>
												<div class="flex-box">
													<div class="flex-list">
														<h6 class="text-left">填充颜色</h6>
														<div class="bgBox">
															<!-- <input type="text" class="form-control" id="noGroupBgColor"> -->
															<div id="noGroupBgColor"></div>
														</div>

													</div>
													<div class="flex-list">
														<h6 class="text-left">线段颜色</h6>
														<div class="bgBox">
															<!-- <input type="text" class="form-control" id="noGroupBorderColor"> -->
															<div id="noGroupBorderColor"></div>
														</div>

													</div>
												</div>
												<hr />
												<h4 class="text-left">分配区域高亮颜色</h4>
												<div class="flex-box">
													<div class="flex-list">
														<h6 class="text-left">填充颜色</h6>
														<div class="bgBox">
															<!-- <input type="text" class="form-control" id="groupBgColor"> -->
															<div id="groupBgColor"></div>
														</div>
													</div>
													<div class="flex-list">
														<h6 class="text-left">线段颜色</h6>
														<div class="bgBox">
															<!-- <input type="text" class="form-control" id="groupBorderColor"> -->
															<div id="groupBorderColor"></div>
														</div>

													</div>
												</div>

											</div>

											<!-- end -->
										</div>
									</div>


								</div>
							</div>
						</div>


					</div>
				</div>
			</div>
		</div>
		<!-- 灰色背景 -->
		<div id="f-fade" class="black_overlay" style="display: none;"></div>

		<!-- 新增网页 -->
		<div class="white-contentAll" id="light1" style="display:none;width:400px;overflow: initial;">
			<div class="white-header">
				<span class="white-headerTitle">
					模块关联座位区域
				</span>
				<span class="white-delete" onclick="cancel('light1')">
				</span>
			</div>
			<div class="white-body" style="max-height: 420px;overflow: initial;">
				<div class="data-module">

					<div class="data-list">
						<div class="data-list-title">
							<b class="redSpan">*</b>请选择模块关联座位区域
						</div>
						<div class="data-list-input layui-form">
							<select name="" id="" lay-filter="groupSelectFn">
								<option value="请选择">请选择</option>
								<option value="a1区">a1区</option>
								<option value="a2区">a2区</option>
								<option value="a3区">a3区</option>
								<option value="a4区">a4区</option>
							</select>
						</div>
					</div>

				</div>
			</div>
			<div class="white-footer">
				<button class="white-close" onclick="cancel('light1')">
					取消
				</button>
				<button class="white-sure white-sureAdd" onclick="groupSelectSure()">
					确定
				</button>
			</div>
		</div>

		<div class="white-contentAll" id="light2" style="display:none;width:400px;overflow: initial;">
			<div class="white-header">
				<span class="white-headerTitle">
					模块关联座位区域
				</span>
				<span class="white-delete" onclick="cancel('light2')">
				</span>
			</div>
			<div class="white-body" style="max-height: 420px;overflow: initial;">
				<div class="data-module">

					<div class="data-list">
						<div class="data-list-title">
							<b class="redSpan">*</b>请选择模块关联座位区域
						</div>
						<div class="data-list-input layui-form">
							<select name="" id="" lay-filter="groupCopySelectFn">
								<option value="请选择">请选择</option>
								<option value="a1区">a1区</option>
								<option value="a2区">a2区</option>
								<option value="a3区">a3区</option>
								<option value="a4区">a4区</option>
							</select>
						</div>
					</div>

				</div>
			</div>
			<div class="white-footer">
				<button class="white-close" onclick="cancel('light2')">
					取消
				</button>
				<button class="white-sure white-sureAdd" onclick="groupCopySelectSure()">
					确定
				</button>
			</div>
		</div>







		<!-- <script src="js/jquery-1.7.2.min.js"></script> -->
		<script src="js/jquery-3.6.3.min.js"></script>
		<script src="js/index.js"></script>
		<script src="js/index.js"></script>
		<!-- <script src="js/jquery-ui-1.9.2.custom.min.js"></script> -->
		<script src="js/moveZoom.js"></script>
		<script src="js/jquery-ui.js"></script>
		<script src="js/dragModule6.js"></script>


		<script>
			$(function() {
				// 动态加载可放大功能
				$.getScript('js/dragModule6.js', function() {});
				// 动态加载可放大功能
				$.getScript('js/moveZoom.js', function() {
					all();
				});
			})
		</script>

		<script src="translateJs/translate.js"></script>
		<script src="translateJs/translateCommon.js"></script>
		<script src="plugin/spectrum/spectrum.js"></script>
		<script src="plugin/jdialog/JDialog.min.js"></script>
		<script src="js/seat.js"></script>
		<script src="layui/layui.js"></script>
		<script>
			var layer = "";
			var slider = "";
			var colorpickerRender = ""; //颜色选择器
			// var setWidthSlider = ""; // 定义宽度滑块
			var setBorderWidthSlider = ""; // 边框宽度
			var setRadiusSlider = ""; // 定义圆角滑块
			var setFontSizeSlider = ""; // 定义字体大小滑块
			var noGroupNumber = 0;
			var noGroupHasAssign = 0; // 未分组已分配数量
			var form = "";
			var channelNumber = 0; // 批量通道人数
			var canUseNumber = 0; // 可用分配数量
			var canUseGroup = []; // 可用分配数组
			var channelSelectArray = JSON.parse(localStorage.getItem("channelSelectArrayStorage")) || []; // 已经分配过的通道

			var dragModuleWidth = 100;
			var dragModuleBorderWidth = 0
			// 所选区域
			var groupSelectText = "请选择";
			var groupCopySelectText = "请选择"


			layui.use(function() {
				form = layui.form;
				var element = layui.element;
				layer = layui.layer;

				element.on('tab(tab-hash)', function(obj) {
					let thisIndex = obj.index
					if (thisIndex == 1) {
						$(".dragItemClicked").removeClass("dragItemClicked")
						$(".coorClick").removeClass("coorClick")
					}
				});
				// 所属分组区域选择
				form.on('select(groupSelectFn)', function(data) {
					var elem = data.elem; // 获得 select 原始 DOM 对象
					var value = data.value; // 获得被选中的值
					console.log(value)
					groupSelectText = value
				});
				// 复制所属区域选择
				form.on('select(groupCopySelectFn)', function(data) {
					var elem = data.elem; // 获得 select 原始 DOM 对象
					var value = data.value; // 获得被选中的值
					console.log(value)
					groupCopySelectText = value
				});

				colorpickerRender = layui.colorpicker;
				// 模块背景色
				colorpickerRender.render({ // 
					elem: '#moduleBgColor',
					color: '#4078cb', // hex
					alpha: true, // 开启透明度
					format: 'rgb',
					predefine: true, // 开启预定义颜色
					colors: ["rgb(152, 0, 0)", "rgb(255, 0, 0)", "rgb(255, 153, 0)", "rgb(255, 255, 0)", "rgb(0, 255, 0)",
						"rgb(0, 255, 255)", "rgb(74, 134, 232)", "rgb(0, 0, 255)", "rgb(255, 0, 255)"
					], //自定义预定义颜色项
					done: function(value) {
						console.log(value); // 当前选中的颜色值
						$(".dragItemClicked").find(".freeweb_btn>a").css({
							background: value
						})
					}
				});
				// 模块字体颜色
				colorpickerRender.render({ //
					elem: '#btnTextColor',
					color: '#ffffff', // hex
					// alpha: true, // 开启透明度
					format: 'rgb',
					predefine: true, // 开启预定义颜色
					colors: ["rgb(152, 0, 0)", "rgb(255, 0, 0)", "rgb(255, 153, 0)", "rgb(255, 255, 0)", "rgb(0, 255, 0)",
						"rgb(0, 255, 255)", "rgb(74, 134, 232)", "rgb(0, 0, 255)", "rgb(255, 0, 255)"
					], //自定义预定义颜色项
					done: function(value) {
						// console.log(value); // 当前选中的颜色值
						$(".dragItemClicked").find(".freeweb_btn>a").css({
							color: value
						})
					}
				});
				// 非分区域背景颜色
				colorpickerRender.render({ // 
					elem: '#noGroupBgColor',
					color: '#ffffff', // hex
					alpha: true, // 开启透明度
					format: 'rgb',
					predefine: true, // 开启预定义颜色
					colors: ["rgb(152, 0, 0)", "rgb(255, 0, 0)", "rgb(255, 153, 0)", "rgb(255, 255, 0)", "rgb(0, 255, 0)",
						"rgb(0, 255, 255)", "rgb(74, 134, 232)", "rgb(0, 0, 255)", "rgb(255, 0, 255)"
					], //自定义预定义颜色项
					done: function(value) {
						// console.log(value); // 当前选中的颜色值

						// 非我的分区背景
						let mySeatName = $(".disabled").attr("name")
						$("td").each(function() {
							let spanName = $(this).find("span").attr("name");
							if ((spanName != mySeatName) && (spanName != "noGroup")) {
								$(this).find("span").css({
									background: value
								})
							}
						})


					}
				});
				// 非分区域线框
				colorpickerRender.render({ //
					elem: '#noGroupBorderColor',
					color: '#ffffff', // hex
					// alpha: true, // 开启透明度
					format: 'rgb',
					predefine: true, // 开启预定义颜色
					colors: ["rgb(152, 0, 0)", "rgb(255, 0, 0)", "rgb(255, 153, 0)", "rgb(255, 255, 0)", "rgb(0, 255, 0)",
						"rgb(0, 255, 255)", "rgb(74, 134, 232)", "rgb(0, 0, 255)", "rgb(255, 0, 255)"
					], //自定义预定义颜色项
					done: function(value) {
						console.log(value); // 当前选中的颜色值
						// 非我的分区边框
						let mySeatName = $(".disabled").attr("name")
						$("td").each(function() {
							let spanName = $(this).find("span").attr("name");
							if ((spanName != mySeatName) && (spanName != "noGroup")) {
								$(this).find("span").css({
									border: `1px solid ${value}`
								})
							}
						})
					}
				});

				// 我的分区域背景颜色
				colorpickerRender.render({ // 
					elem: '#groupBgColor',
					color: '#ffffff', // hex
					alpha: true, // 开启透明度
					format: 'rgb',
					predefine: true, // 开启预定义颜色
					colors: ["rgb(152, 0, 0)", "rgb(255, 0, 0)", "rgb(255, 153, 0)", "rgb(255, 255, 0)", "rgb(0, 255, 0)",
						"rgb(0, 255, 255)", "rgb(74, 134, 232)", "rgb(0, 0, 255)", "rgb(255, 0, 255)"
					], //自定义预定义颜色项
					done: function(value) {
						// 获取我所属的区域
						let groupName = $(".disabled").attr("name")
						$("td").find(`span[name='${groupName}']`).css({
							background: value
						})
					}
				});
				// 我的分区域线框
				colorpickerRender.render({ //
					elem: '#groupBorderColor',
					color: '#ffffff', // hex
					// alpha: true, // 开启透明度
					format: 'rgb',
					predefine: true, // 开启预定义颜色
					colors: ["rgb(152, 0, 0)", "rgb(255, 0, 0)", "rgb(255, 153, 0)", "rgb(255, 255, 0)", "rgb(0, 255, 0)",
						"rgb(0, 255, 255)", "rgb(74, 134, 232)", "rgb(0, 0, 255)", "rgb(255, 0, 255)"
					], //自定义预定义颜色项
					done: function(value) {
						console.log(value); // 当前选中的颜色值
						// 获取我所属的区域
						let groupName = $(".disabled").attr("name")
						$("td").find(`span[name='${groupName}']`).css({
							border: `1px solid ${value}`
						})
					}
				});



				// 滑块
				slider = layui.slider;
				// 渲染
				// 圆角设置
				setRadiusSlider = slider.render({
					elem: '#rangeBorderRadius',
					min: 0,
					max: 50,
					value: 0, //初始值
					input: true, //输入框
					change: function(value) {
						// console.log(value)
						$(".dragItemClicked").find(".freeweb_btn>a").css({
							"borderRadius": value + "px"
						})
					}
				});
				// 宽度设置
				setWidthSlider = slider.render({
					elem: '#rangeBorderWidth',
					min: 0,
					max: 1000,
					value: dragModuleWidth, //初始值
					input: true, //输入框
					change: function(value) {
						// console.log(value)
						$(".dragItemClicked").find(".freeBtbBox").css({
							"width": value + "px"
						})
					}
				});
				// 字体大小
				setFontSizeSlider = slider.render({
					elem: '#rangeFontSize',
					min: 10,
					max: 30,
					value: 14, //初始值
					input: true, //输入框
					change: function(value) {
						// console.log(value)
						$(".dragItemClicked").find(".freeBtbBox").find(".freeweb_btn>a").css({
							"font-size": value + "px"
						})
					}
				});







			});



			// 放大缩小
			function zoomFn(obj) {
				let zoomVal = Number($("#seatBox").attr("zoom"))
				if (obj == "in") {
					if (zoomVal >= 0.2) {
						zoomVal = zoomVal - 0.1
						$("#seatBox").css({
							transform: "scale(" + zoomVal + ")"
						})
					}

				} else if (obj == "out") {

					if (zoomVal < 1) {
						zoomVal = zoomVal + 0.1
						$("#seatBox").css({
							transform: "scale(" + zoomVal + ")"
						})
					}

				}
				$("#seatBox").attr("zoom", zoomVal)
			}
		</script>
		<script>
			let seatRows = localStorage.getItem("seatRows");
			let seatCols = localStorage.getItem("seatCols");

			// var groupObject = JSON.parse(localStorage.getItem('groupArrayObject')) || []
			try {
				var datas = JSON.parse(localStorage.getItem("seats"));
				var selected = JSON.parse(localStorage.getItem("selected"));
			} catch (e) {}
			var seats = $.seats({
				box: "#seatBox",
				rows: seatRows,
				cols: seatCols,
				size: 20, //座位的尺寸，单位：像素	
				hall_id: 10, //厅，如：5号厅
				thre_id: 20, //剧场，如：大地电影院中天广场沃尔玛店
				movie_id: 30, //场次ID
				step: 5,
				maxTicketNumber: 1000, //最多购票张数
				datas: datas,
				selected: selected || {},
				onSelected: function(seat) {

					/*************/
					var id = "selected_" + seat.row + "_" + seat.col;
					// 获取分区名称
					console.log(seat.color)
					let getGroupName = seat.groupName
					let getGroupLength = $(`span[name='${getGroupName}']`).length
					// console.log(getGroupLength)

					// 判断是否已经分配的座位
					let hasDisabled = $(`#${seat.row}_${seat.col}`).find("span").hasClass("disabledCancel")
					console.log(hasDisabled)
					// var selectedHtml = ''
					// 获取真实的座位号（去除空格的)
					let realSeat = $(`#${seat.row}_${seat.col}`).attr("seat").split("-")
					console.log(realSeat)

					/*********************************/
					return true;
				},
				onUnselected: function(seat) {
					var id = "selected_" + seat.row + "_" + seat.col;
					$("#" + id).remove();
				},
				onerror: function(msg) {
					// JDialog.msg({
					// 	type: 'error',
					// 	content: msg,
					// 	container: "#seatBox"
					// });
					layer.msg(msg, {
						icon: 2,
						time: 1000 // 设置 2 秒后自动关闭
					});
				}
			});

			// 所属区域高亮

			$(function() {
				// let hrefSrc = window.location.href.split("?")[1].split("-")
				let row = 4
				let col = 18
				let seat = `${row}-${col}`
				console.log(seat)
				let groupName = $(`td[seat='${seat}']`).find("span").attr("name")
				console.log(groupName)
				// 回显座位到页面
				$("#mySeat").html(`我的座位：${groupName}区 - ${row}排${col}座`)

				// $(`td[seat='${seat}']`).find("span").html(seat).css({
				// 	color: "#fff",
				// 	fontSize: "10px",
				// 	lineHeight: "20px"
				// })
				// console.log($("td").find(`span[name='${groupName}']`).)
				let groupBg = $("td").find(`span[name='${groupName}']`).attr("style").split("background")[1].replace(":", "")
				groupBg = groupBg.replace(";", "")
				// 遍历所有可选座位
				// $("td").each(function() {
				// 	let backNone = $(this).find("span").attr("style")
				// 	if (!backNone.includes("background: none;")) {
				// 		$(this).find("span").css({
				// 			// background: '#d7d7d7'
				// 			opacity: 0.5
				// 		})
				// 	}
				// })

				// $("td").find("span").css({
				// 	background: '#d7d7d7'
				// })
				$("td").find("span").removeClass("disabled")


				// $("td").find(`span[name='${groupName}']`).css({
				// 	background: groupBg,
				// 	opacity: 0.5
				// })
				$(`td[seat='${seat}']`).find("span").addClass("disabled")
				// $(`td[seat='${seat}']`).find("span").css({
				// 	opacity: 1
				// })
			})

			function editSeat() {
				window.location.href = "step3.html"
			}
			// 预览
			function viewMySeat() {
				window.location.href = "mySeat.html"
			}

			// 遮罩层
			function viewTask(id) {
				$("div[id^=light]").hide();
				$("#" + id).show();
				$("#f-fade").show();
			}

			function cancel(id) {
				$("#" + id).hide();
				$("#f-fade").hide();
			}

			function viewSrc(src) {
				window.location.href = src
			}
		</script>

		<!-- 新增组件设置js -->
		<script src="js/seatModule6.js"></script>



	</body>
</html>