<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>Step3 布局测试</title>
    <style>
        body { margin: 0; font-family: Arial, sans-serif; }
        
        /* 主布局 */
        .main-content {
            display: flex !important;
            height: calc(100vh - 120px) !important;
            overflow: hidden !important;
            background: #f0f0f0;
        }

        /* 左侧工具栏 */
        .sidebar-toolbar {
            width: 250px;
            background: white;
            border-right: 1px solid #e6e6e6;
            overflow-y: auto;
            padding: 20px 15px;
            box-sizing: border-box;
        }

        .toolbar-section {
            margin-bottom: 25px;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
        }

        .section-title {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e6e6e6;
        }

        .btn-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }

        .sidebar-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 12px;
            min-height: 36px;
            text-decoration: none;
            color: #333;
        }

        .sidebar-btn.primary {
            background: #52c41a;
            border-color: #52c41a;
            color: white;
        }

        /* 座位画布区域 */
        .seat-canvas-container {
            flex: 1;
            position: relative;
            background: #fafafa;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        #seatBox {
            width: 100%;
            height: 100%;
            position: relative;
            overflow: hidden;
            background: white;
            border: 2px dashed #ccc;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            color: #666;
        }

        /* 右侧面板 */
        .main-right {
            width: 300px;
            background: white;
            border-left: 1px solid #e6e6e6;
            overflow-y: auto;
            padding: 20px;
        }

        .panel-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .panel-content {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
        }
    </style>
</head>
<body>
    <div style="height: 120px; background: #001529; color: white; display: flex; align-items: center; justify-content: center;">
        <h1>Step3 布局测试 - 顶部导航区域</h1>
    </div>

    <div class="main-content">
        <!-- 左侧工具栏 -->
        <div class="sidebar-toolbar">
            <div class="toolbar-section">
                <div class="section-title">导航 (步骤3/3)</div>
                <div class="btn-grid">
                    <button type="button" class="sidebar-btn primary">
                        <span>上一步</span>
                    </button>
                    <button type="button" class="sidebar-btn primary">
                        <span>保存设置</span>
                    </button>
                </div>
            </div>

            <div class="toolbar-section">
                <div class="section-title">工具</div>
                <div class="btn-grid">
                    <button type="button" class="sidebar-btn">
                        <span>选择座位</span>
                    </button>
                    <button type="button" class="sidebar-btn">
                        <span>框选座位</span>
                    </button>
                </div>
                <div class="btn-grid">
                    <button type="button" class="sidebar-btn">
                        <span>拖拽</span>
                    </button>
                    <button type="button" class="sidebar-btn">
                        <span>创建分区</span>
                    </button>
                </div>
            </div>

            <div class="toolbar-section">
                <div class="section-title">操作</div>
                <div class="btn-grid">
                    <button type="button" class="sidebar-btn">
                        <span>全选</span>
                    </button>
                    <button type="button" class="sidebar-btn">
                        <span>清除选择</span>
                    </button>
                </div>
                <div class="btn-grid">
                    <button type="button" class="sidebar-btn">
                        <span>反选</span>
                    </button>
                    <button type="button" class="sidebar-btn">
                        <span>撤销</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- 座位画布区域 -->
        <div class="seat-canvas-container">
            <div id="seatBox">
                座位画布区域 (Canvas)
            </div>
        </div>

        <!-- 右侧面板 -->
        <div class="main-right">
            <div class="panel-title">分区设置</div>
            <div class="panel-content">
                <h4>📋 操作说明</h4>
                <ul>
                    <li>🎯 选择座位：点击或框选要分区的座位</li>
                    <li>🎨 创建分区：选择座位后填写分区信息</li>
                    <li>💾 保存设置：完成所有分区后保存</li>
                </ul>
            </div>

            <div class="panel-content" style="margin-top: 15px;">
                <h4>📊 当前选择</h4>
                <p>已选择座位：0 个</p>
                <p>总座位数：0 个</p>
            </div>

            <div class="panel-content" style="margin-top: 15px;">
                <h4>📝 已创建分区</h4>
                <p style="color: #999; text-align: center;">暂无分区</p>
            </div>
        </div>
    </div>
</body>
</html>
