<!DOCTYPE html>
<html lang="ch">
	<head>
		<title>设置分区</title>
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="renderer" content="webkit">
		<meta charset="UTF-8">
		<link rel="stylesheet" type="text/css" href="css/reset.css">
		<link rel="stylesheet" href="css/translateCommon.css">
		<link rel="stylesheet" href="layui/css/layui.css" />
		<link rel="stylesheet" href="css/stepPlug.css" />
		<link rel="stylesheet" href="css/seat3.css" />
		<link rel="stylesheet" href="css/step2only.css" />

		<!-- 左侧工具栏样式 -->
		<style>
			/* 主布局 */
			.main-content {
				display: flex !important;
				height: calc(100vh - 120px) !important;
				overflow: hidden !important;
			}

			/* 左侧工具栏 */
			.sidebar-toolbar {
				width: 200px;
				background: #f8f9fa;
				border-right: 1px solid #e9ecef;
				overflow-y: auto;
				flex-shrink: 0;
				padding: 0;
			}

			/* 工具栏区块 */
			.toolbar-section {
				border-bottom: 1px solid #e9ecef;
				padding: 8px;
			}

			.toolbar-section:last-child {
				border-bottom: none;
			}

			.section-title {
				font-size: 12px;
				font-weight: bold;
				color: #495057;
				margin-bottom: 8px;
				padding: 4px 0;
				border-bottom: 1px solid #dee2e6;
			}

			.btn-grid {
				display: grid;
				grid-template-columns: 1fr 1fr;
				gap: 4px;
				margin-bottom: 8px;
			}

			.sidebar-btn {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				padding: 8px 4px;
				border: 1px solid #ced4da;
				background: white;
				border-radius: 4px;
				cursor: pointer;
				transition: all 0.2s;
				font-size: 10px;
				min-height: 50px;
				text-align: center;
			}

			.sidebar-btn:hover {
				border-color: #007bff;
				background: #e7f3ff;
			}

			.sidebar-btn.active {
				background: #007bff;
				border-color: #007bff;
				color: white;
			}

			.sidebar-btn.primary {
				background: #28a745;
				border-color: #28a745;
				color: white;
			}

			.sidebar-btn.primary:hover {
				background: #218838;
				border-color: #1e7e34;
			}

			.sidebar-btn i {
				font-size: 16px;
				margin-bottom: 2px;
			}

			.sidebar-btn span {
				font-size: 10px;
				line-height: 1.2;
			}

			/* 座位画布区域 */
			.seat-canvas-container {
				flex: 1;
				position: relative;
				background: #fafafa;
				overflow: hidden;
			}

			#seatBox {
				width: 100%;
				height: 100%;
				position: relative;
				overflow: hidden;
			}

			#seatCanvas {
				display: block;
				cursor: grab;
			}

			/* 右侧面板 */
			.main-right {
				width: 300px;
				background: white;
				border-left: 1px solid #e6e6e6;
				overflow-y: auto;
			}

			.container {
				padding: 20px;
			}

			.info-section {
				margin-bottom: 20px;
				background: #f8f9fa;
				padding: 15px;
				border-radius: 6px;
			}

			.zone-form {
				background: #f0f0f0;
				padding: 15px;
				border-radius: 6px;
				margin-bottom: 15px;
			}

			.form-group {
				margin-bottom: 12px;
			}

			.form-group label {
				display: block;
				margin-bottom: 5px;
				font-weight: bold;
			}

			.form-group input {
				width: 100%;
				padding: 8px;
				border: 1px solid #ddd;
				border-radius: 4px;
				box-sizing: border-box;
			}

			.zone-item {
				background: white;
				border: 1px solid #e6e6e6;
				border-radius: 6px;
				padding: 12px;
				margin-bottom: 10px;
			}

			.zone-item h5 {
				margin: 0 0 8px 0;
				font-weight: bold;
			}

			.zone-info {
				font-size: 12px;
				color: #666;
				display: flex;
				justify-content: space-between;
				align-items: center;
			}

			.zone-color {
				width: 20px;
				height: 20px;
				border-radius: 3px;
				border: 1px solid #ddd;
			}
		</style>

	</head>
	<body style="margin: 0; padding: 0; overflow: hidden;">
		<div id="section">
			<!-- header -->
			<div id="header" style="background: #001529; color: white; padding: 20px; text-align: center;">
				<h1 style="margin: 0; color: white;">Step3 - 座位分区设置</h1>
			</div>

			<!-- 主要模块 -->
			<div class="c_right">
				<div class="main">
					<div class="new-content-top">
						<span class="layui-breadcrumb" lay-separator=">">
							<a href="">首页</a>
							<a href="">活动列表</a>
							<a href="">座位规划</a>
							<a><cite>设置分区</cite></a>
						</span>
						<div class="content-top-btn" style="visibility: hidden;">
							<button class="layui-btn" type="button" onclick="">占位</button>
						</div>
					</div>

					<div class="main-content" style="overflow: hidden !important; display: flex;">
						<!-- 左侧工具栏 -->
						<div class="sidebar-toolbar" id="sidebarToolbar">
							<!-- 导航 & 进度 -->
							<div class="toolbar-section">
								<div class="section-title">导航 (步骤3/3)</div>
								<div class="btn-grid">
									<button type="button" class="sidebar-btn primary" onclick="location.href='step2.html'">
										<i class="layui-icon layui-icon-left"></i>
										<span>上一步</span>
									</button>
									<button type="button" class="sidebar-btn primary" id="saveTop" onclick="saveZones()">
										<i class="layui-icon layui-icon-ok"></i>
										<span>保存分区设置</span>
									</button>
								</div>
							</div>

							<!-- 分区工具 -->
							<div class="toolbar-section">
								<div class="section-title">分区工具</div>
								<div class="btn-grid">
									<button type="button" class="sidebar-btn tool-btn active" id="selectTool" data-tool="select">
										<i class="layui-icon layui-icon-ok-circle"></i>
										<span>选择座位</span>
									</button>
									<button type="button" class="sidebar-btn tool-btn" id="boxSelectTool" data-tool="boxselect">
										<i class="layui-icon layui-icon-screen-full"></i>
										<span>框选座位</span>
									</button>
								</div>
								<div class="btn-grid">
									<button type="button" class="sidebar-btn" id="createZoneBtn">
										<i class="layui-icon layui-icon-add-1"></i>
										<span>创建分区</span>
									</button>
									<button type="button" class="sidebar-btn" id="clearZonesBtn">
										<i class="layui-icon layui-icon-delete"></i>
										<span>清除所有分区</span>
									</button>
								</div>
							</div>



							<!-- 信息显示 -->
							<div class="toolbar-section">
								<div class="section-title">信息</div>
								<div class="info-display">
									<div class="info-row">
										<span class="info-label">已选:</span>
										<span class="info-value" id="selectedCount">0</span>
									</div>
									<div class="info-row">
										<span class="info-label">总计:</span>
										<span class="info-value" id="totalSeats">0</span>
									</div>
									<div class="info-row">
										<span class="info-label">分区:</span>
										<span class="info-value" id="zoneCount">0</span>
									</div>
								</div>
							</div>

							<!-- 分区表单（在工具栏中） -->
							<div class="toolbar-section" id="zoneFormSection" style="display: none;">
								<div class="section-title">创建分区</div>
								<div class="form-group">
									<label for="zoneName">分区名称</label>
									<input type="text" id="zoneName" placeholder="分区1" class="layui-input" style="width: 100%; padding: 4px; font-size: 12px;" title="输入分区名称" />
								</div>
								<div class="form-group">
									<label for="zoneColor">分区颜色</label>
									<input type="color" id="zoneColor" value="#1890ff" class="layui-input" style="width: 100%; padding: 4px;" title="选择分区颜色" />
								</div>
								<div class="btn-grid">
									<button type="button" class="sidebar-btn primary" onclick="createZone()">
										<i class="layui-icon layui-icon-ok"></i>
										<span>创建</span>
									</button>
									<button type="button" class="sidebar-btn" onclick="cancelZone()">
										<i class="layui-icon layui-icon-close"></i>
										<span>取消</span>
									</button>
								</div>
							</div>

							<!-- 分区列表（长期显示） -->
							<div class="toolbar-section">
								<div class="section-title">已创建分区</div>
								<div id="zoneList" style="max-height: 200px; overflow-y: auto;">
									<p style="color: #999; text-align: center; font-size: 12px;">暂无分区</p>
								</div>
							</div>
						</div>

						<!-- 座位画布区域（占满剩余空间） -->
						<div class="seat-canvas-container" style="flex: 1; position: relative; background: #fafafa; overflow: hidden;">
							<div id="seatBox" style="width: 100%; height: 100%; position: relative; overflow: hidden;">
								<canvas id="seatCanvas"></canvas>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- JavaScript -->
		<script src="layui/layui.js"></script>
		<script src="js/jquery-3.6.3.min.js"></script>
		<script src="SeatMapEngine.js"></script>
		<script>
			// 全局变量
			let engine;
			let seatsData = {};
			let selectedSeats = new Set();
			let zones = [];
			let currentTool = 'select';

			// 创建测试数据
			function createTestData() {
				const seatRows = localStorage.getItem("seatRows") || "8";
				const seatCols = localStorage.getItem("seatCols") || "12";

				localStorage.setItem("seatRows", seatRows);
				localStorage.setItem("seatCols", seatCols);

				// 加载座位数据
				const savedSeats = localStorage.getItem("seats");
				if (!savedSeats) {
					// 创建测试座位数据
					const testSeats = {};
					for (let row = 0; row < parseInt(seatRows); row++) {
						for (let col = 0; col < parseInt(seatCols); col++) {
							testSeats[row + "_" + col] = {
								row: row,
								col: col
							};
						}
					}
					seatsData = testSeats;
				} else {
					seatsData = JSON.parse(savedSeats);
				}

				// 加载已有分区数据
				const savedZones = localStorage.getItem('zones');
				if (savedZones) {
					zones = JSON.parse(savedZones);
				}

				return { rows: parseInt(seatRows), cols: parseInt(seatCols) };
			}

			$(document).ready(function() {
				// 创建测试数据
				const { rows, cols } = createTestData();

				// 创建Canvas元素，完全适应容器（与Step2一致）
				const canvas = document.createElement('canvas');
				const container = document.querySelector("#seatBox");

				// 设置Canvas样式，确保完全适应容器
				canvas.style.width = '100%';
				canvas.style.height = '100%';
				canvas.style.display = 'block';
				canvas.style.position = 'absolute';
				canvas.style.top = '0';
				canvas.style.left = '0';

				container.appendChild(canvas);

				// 初始化SeatMapEngine
				engine = new SeatMapEngine(canvas, {
					seatSize: 20,
					seatSpacing: 4,
					showSeatNumbers: true,
					enableRoundedCorners: true,
					showHeaders: true, // 启用标尺
					onSeatClick: handleSeatClick,
					onSeatHover: handleSeatHover,
					onViewChange: handleViewChange
				});

				// 确保Canvas完全适应容器（在引擎初始化后）
				fixCanvasSize(canvas, container);

				// 扩展引擎，添加标尺渲染功能
				addHeadersToEngine(engine);

				// 添加标尺点击功能
				addHeaderClickHandlers(engine);

				// 监听窗口大小变化
				window.addEventListener('resize', () => {
					fixCanvasSize(canvas, container);
					centerSeats(engine);
				});

				// 生成座位
				engine.generateSeats(rows, cols);

				// 居中显示座位
				centerSeats(engine);

				// 设置座位数据
				engine.seats.forEach(seat => {
					const seatKey = seat.row + "_" + seat.col;
					if (seatsData[seatKey]) {
						seat.status = 'available';
						// 如果座位已有分区信息，设置颜色
						if (seatsData[seatKey].color) {
							seat.groupColor = seatsData[seatKey].color;
							seat.groupName = seatsData[seatKey].zoneName;
						}
					} else {
						seat.status = 'empty';
					}
				});

				// 设置点击处理
				engine.onSeatClick = handleSeatClick;

				// 重新启用鼠标事件（修复拖拽功能）
				engine.canvas.addEventListener('mousedown', engine.handleMouseDown.bind(engine));
				engine.canvas.addEventListener('mousemove', engine.handleMouseMove.bind(engine));
				engine.canvas.addEventListener('mouseup', engine.handleMouseUp.bind(engine));

				// 初始化界面
				updateSelectionInfo();
				updateZoneList();
				setupEventListeners();

				// 渲染
				engine.render();
			});

			// 座位点击处理（统一处理点击和框选）
			function handleSeatClick(seat) {
				// 只在选择模式下处理座位点击（包括select和boxselect）
				if (currentTool !== 'select' && currentTool !== 'boxselect') return;

				const seatKey = seat.row + "_" + seat.col;
				if (!seatsData[seatKey]) return;

				if (selectedSeats.has(seatKey)) {
					selectedSeats.delete(seatKey);
					if (seatsData[seatKey].color) {
						seat.groupColor = seatsData[seatKey].color;
						seat.status = 'available';
					} else {
						seat.groupColor = null;
						seat.status = 'available';
					}
				} else {
					selectedSeats.add(seatKey);
					seat.status = 'selected';
				}

				engine.render();
				updateSelectionInfo();
			}

			// 更新选择信息
			function updateSelectionInfo() {
				const totalSeats = Object.keys(seatsData).length;
				const selectedCount = selectedSeats.size;

				document.getElementById('selectedCount').textContent = selectedCount;
				document.getElementById('totalSeats').textContent = totalSeats;
				document.getElementById('zoneCount').textContent = zones.length;
			}

			// 设置事件监听器
			function setupEventListeners() {
				// 工具切换
				document.querySelectorAll('.tool-btn').forEach(btn => {
					btn.addEventListener('click', function() {
						document.querySelectorAll('.tool-btn').forEach(b => b.classList.remove('active'));
						this.classList.add('active');
						currentTool = this.dataset.tool;
					});
				});

				// 操作按钮
				document.getElementById('createZoneBtn').addEventListener('click', showZoneForm);
				document.getElementById('clearZonesBtn').addEventListener('click', clearAllZones);
			}





			// 创建分区
			function createZone() {
				const name = document.getElementById('zoneName').value.trim();
				const color = document.getElementById('zoneColor').value;

				if (!name) {
					layui.use('layer', function(){
						const layer = layui.layer;
						layer.msg('请输入分区名称！', {icon: 2});
					});
					document.getElementById('zoneName').focus();
					return;
				}

				// 检查分区名称是否重复
				if (zones.some(zone => zone.name === name)) {
					layui.use('layer', function(){
						const layer = layui.layer;
						layer.msg('分区名称已存在，请使用其他名称！', {icon: 2});
					});
					document.getElementById('zoneName').focus();
					return;
				}

				// 创建分区
				const zone = {
					id: Date.now().toString(),
					name: name,
					color: color,
					seats: Array.from(selectedSeats)
				};

				zones.push(zone);

				// 更新座位数据
				selectedSeats.forEach(seatKey => {
					if (seatsData[seatKey]) {
						seatsData[seatKey].zoneName = name;
						seatsData[seatKey].color = color;
						// 移除价格字段
						delete seatsData[seatKey].price;
					}
				});

				// 更新座位颜色
				engine.seats.forEach(seat => {
					const seatKey = seat.row + "_" + seat.col;
					if (selectedSeats.has(seatKey)) {
						seat.groupColor = color;
						seat.groupName = name;
						seat.status = 'available';
					}
				});

				selectedSeats.clear();
				updateSelectionInfo();
				updateZoneList();
				cancelZone();
				engine.render();

				layui.use('layer', function(){
					const layer = layui.layer;
					layer.msg(`分区"${name}"创建成功！`, {icon: 1});
				});
			}

			// 更新分区列表
			function updateZoneList() {
				const zoneList = document.getElementById('zoneList');
				if (zones.length === 0) {
					zoneList.innerHTML = '<p style="color: #999; text-align: center;">暂无分区</p>';
					return;
				}

				let html = '';
				zones.forEach(zone => {
					html += `
						<div class="zone-item">
							<h5>${zone.name}</h5>
							<div class="zone-info">
								<span>座位：${zone.seats.length}个</span>
								<div class="zone-color" style="background-color: ${zone.color}"></div>
							</div>
						</div>
					`;
				});
				zoneList.innerHTML = html;
			}

			// 显示分区表单
			function showZoneForm() {
				if (selectedSeats.size === 0) {
					layui.use('layer', function(){
						const layer = layui.layer;
						layer.msg('请先选择要分区的座位！', {icon: 2});
					});
					return;
				}

				// 显示分区表单
				document.getElementById('zoneFormSection').style.display = 'block';

				// 自动设置默认分区名称（按顺序）
				const defaultName = `分区${zones.length + 1}`;
				document.getElementById('zoneName').value = defaultName;

				// 自动设置随机颜色
				document.getElementById('zoneColor').value = getRandomZoneColor();

				// 聚焦到名称输入框，方便用户修改
				document.getElementById('zoneName').focus();
				document.getElementById('zoneName').select(); // 选中文本，方便用户直接输入新名称
			}

			// 取消分区
			function cancelZone() {
				document.getElementById('zoneFormSection').style.display = 'none';
				document.getElementById('zoneName').value = '';
				document.getElementById('zoneColor').value = '#1890ff';
			}



			// 随机分配分区颜色
			function getRandomZoneColor() {
				const presetColors = [
					'#1890ff', '#52c41a', '#faad14', '#f5222d',
					'#722ed1', '#13c2c2', '#eb2f96', '#fa541c'
				];
				const usedColors = zones.map(zone => zone.color);
				const availableColors = presetColors.filter(color => !usedColors.includes(color));

				if (availableColors.length > 0) {
					return availableColors[Math.floor(Math.random() * availableColors.length)];
				} else {
					// 如果预设颜色都用完了，生成随机颜色
					return '#' + Math.floor(Math.random()*16777215).toString(16);
				}
			}

			// 清除所有分区
			function clearAllZones() {
				if (zones.length === 0) {
					layui.use('layer', function(){
						const layer = layui.layer;
						layer.msg('没有分区可清除！', {icon: 2});
					});
					return;
				}

				layui.use('layer', function(){
					const layer = layui.layer;
					layer.confirm(`确定要清除所有 ${zones.length} 个分区吗？`, {
						btn: ['确定清除', '取消']
					}, function(index){
						layer.close(index);

						// 清除所有分区
						zones = [];

						// 清除所有座位的分区信息
						Object.keys(seatsData).forEach(seatKey => {
							delete seatsData[seatKey].zoneName;
							delete seatsData[seatKey].color;
						});

						// 更新座位显示
						engine.seats.forEach(seat => {
							seat.groupColor = null;
							seat.groupName = null;
						});

						// 更新界面
						updateSelectionInfo();
						updateZoneList();
						engine.render();

						layer.msg('所有分区已清除', {icon: 1});
					});
				});
			}

			// 保存分区设置
			function saveZones() {
				if (zones.length === 0) {
					alert('请至少创建一个分区！');
					return;
				}
				localStorage.setItem('seats', JSON.stringify(seatsData));
				localStorage.setItem('zones', JSON.stringify(zones));
				alert('分区设置已保存！');
			}

			// 修复Canvas尺寸，确保完全适应容器（与Step2一致）
			function fixCanvasSize(canvas, container) {
				const seatBoxElement = document.getElementById('seatBox');
				const seatBoxRect = seatBoxElement.getBoundingClientRect();
				const windowHeight = window.innerHeight;
				const seatBoxTop = seatBoxRect.top;
				const availableHeight = windowHeight - seatBoxTop;

				// 设置容器高度为剩余的全部空间
				seatBoxElement.style.height = availableHeight + 'px';

				// 获取更新后的容器尺寸
				const rect = container.getBoundingClientRect();
				const dpr = window.devicePixelRatio || 1;

				// 设置Canvas的实际尺寸（考虑设备像素比）
				canvas.width = rect.width * dpr;
				canvas.height = rect.height * dpr;

				// 设置Canvas的显示尺寸
				canvas.style.width = rect.width + 'px';
				canvas.style.height = rect.height + 'px';

				// 缩放绘图上下文以适应设备像素比
				const ctx = canvas.getContext('2d');
				ctx.scale(dpr, dpr);

				// 通知引擎Canvas尺寸已更改
				if (engine && engine.handleResize) {
					engine.handleResize();
				}
			}

			// 居中显示座位（与Step2一致）
			function centerSeats(engine) {
				if (!engine || !engine.seats || engine.seats.length === 0) {
					return;
				}

				// 计算座位区域的边界
				let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

				engine.seats.forEach(seat => {
					const x = seat.col * (engine.seatSize + engine.seatSpacing);
					const y = seat.row * (engine.seatSize + engine.seatSpacing);

					minX = Math.min(minX, x);
					minY = Math.min(minY, y);
					maxX = Math.max(maxX, x + engine.seatSize);
					maxY = Math.max(maxY, y + engine.seatSize);
				});

				// 计算座位区域的中心
				const seatAreaWidth = maxX - minX;
				const seatAreaHeight = maxY - minY;
				const seatAreaCenterX = minX + seatAreaWidth / 2;
				const seatAreaCenterY = minY + seatAreaHeight / 2;

				// 计算Canvas的中心
				const canvas = engine.canvas;
				const canvasCenterX = canvas.width / (window.devicePixelRatio || 1) / 2;
				const canvasCenterY = canvas.height / (window.devicePixelRatio || 1) / 2;

				// 计算偏移量
				const offsetX = canvasCenterX - seatAreaCenterX;
				const offsetY = canvasCenterY - seatAreaCenterY;

				// 设置视图偏移
				if (engine.setViewOffset) {
					engine.setViewOffset(offsetX, offsetY);
				}

				// 重新渲染
				engine.render();
			}

			// 扩展引擎，添加标尺渲染功能（与Step2一致）
			function addHeadersToEngine(engine) {
				// 保存原始的render方法
				const originalRender = engine.render.bind(engine);

				// 重写render方法，正确处理变换
				engine.render = function() {
					const startTime = performance.now()

					// 清空画布
					this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height)

					// 设置变换
					this.ctx.save()
					this.ctx.translate(this.state.offsetX, this.state.offsetY)
					this.ctx.scale(this.state.zoom, this.state.zoom)

					// 先渲染标尺（在变换内）
					renderHeaders(engine);

					// 然后渲染座位（在变换内，但不包括选择框）
					this.renderSeatsOnly()

					this.ctx.restore()

					// 在变换外绘制选择框
					if (this.state.isSelecting) {
						this.renderSelectionBox()
					}

					// 更新性能统计
					const endTime = performance.now()
					this.stats.renderTime = endTime - startTime
					this.updateFPS()

					// 触发视图变化回调
					this.callbacks.onViewChange({
						zoom: this.state.zoom,
						offsetX: this.state.offsetX,
						offsetY: this.state.offsetY,
						stats: this.stats
					})
				};

				// 创建只渲染座位的方法（不包括选择框）
				engine.renderSeatsOnly = function() {
					// 视口裁剪 - 只绘制可见座位
					const visibleSeats = this.getVisibleSeats()
					this.stats.visibleSeats = visibleSeats.length

					// 根据数量选择绘制模式
					if (visibleSeats.length > 1200 || this.state.isHighPerformanceMode) {
						this.renderSeatsBatch(visibleSeats)
					} else {
						this.renderSeatsIndividual(visibleSeats)
					}

					// 绘制文字
					if (this.shouldShowDetailedView()) {
						this.renderTexts(visibleSeats)
					}
				};
			}

			// 渲染标尺（在变换内，会跟座位一起缩放）
			function renderHeaders(engine) {
				const ctx = engine.ctx;
				const config = engine.config;

				// 设置标尺样式（注意：现在在变换内，字体大小会被缩放）
				ctx.fillStyle = '#666';
				// 字体大小需要根据缩放调整，保持可读性
				const fontSize = Math.max(8, Math.min(16, 12 / engine.state.zoom));
				ctx.font = `${fontSize}px Arial`;
				ctx.textAlign = 'center';
				ctx.textBaseline = 'middle';

				// 计算标尺位置（在座位坐标系内）
				const headerOffset = 15;
				const seatSize = config.seatSize;
				const spacing = config.seatSpacing;

				// 获取数据
				const seatRows = localStorage.getItem("seatRows") || "8";
				const seatCols = localStorage.getItem("seatCols") || "12";

				// 绘制行标尺（左侧）
				for (let row = 0; row < parseInt(seatRows); row++) {
					const y = row * (seatSize + spacing) + seatSize/2;
					const x = -headerOffset;
					ctx.fillText((row + 1).toString(), x, y);
				}

				// 绘制列标尺（顶部）
				for (let col = 0; col < parseInt(seatCols); col++) {
					const x = col * (seatSize + spacing) + seatSize/2;
					const y = -headerOffset;
					ctx.fillText((col + 1).toString(), x, y);
				}
			}

			// 添加标尺点击功能
			function addHeaderClickHandlers(engine) {
				// 标尺点击功能可以在这里实现
				// 暂时留空，保持与Step2兼容
			}

			// 空函数占位符（与Step2兼容）
			function handleSeatHover(seat) {
				// 座位悬停处理
			}

			function handleViewChange() {
				// 视图变化处理
			}
		</script>
	</body>
</html>
	</div>
