/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 17-8-15.
 */

(function($) {

	//选中座位
	$.fn.selectSeat = function() {
		$(this).find('span').addClass("selected");
	}

	//取消选中座位
	$.fn.unselectSeat = function() {
		$(this).find('span').removeClass("selected");
	}

	//判断座位是否被选中
	$.fn.hasSelected = function() {
		return $(this).find('span').hasClass("selected");
	}

	// 加载 css 文件
	// var js = document.scripts,
	// 	script = js[js.length - 1],
	// 	jsPath = script.src;
	// var cssPath = jsPath.substring(0, jsPath.lastIndexOf("/") + 1) + "css/seat.css"
	// console.log(cssPath)
	// $("head:eq(0)").append('<link href="' + cssPath + '" rel="stylesheet" type="text/css" />');

	var __seats__ = function(options) {

		var defaults = {
			rows: 20, //座位排数
			cols: 30, //座位列数
			size: 20, //座位的尺寸，单位：像素
			thre_id: 0, //剧场ID
			hall_id: 0, //厅ID
			movie_id: 0, //电影场次ID
			step: 1, //操作步骤，1：排座，2：设置票价，3：购票
			onSelected: function(seat) { //选中座位时候的回调，仅当 step = 3(购票)时有效
				console.log(seat);
			},
			onUnselected: function(seat) { //取消座位的时候回调
				console.log(seat);
			},
			onerror: function(msg) {

			},
			maxTicketNumber: 5, //最多购票数量
			selected: {}, //已选中的座位
			datas: {}
		};
		options = $.extend(defaults, options);
		var seats = {};
		var o = {};
		var ticketNumber = 0; //已购票数
		if (getObjLength(options.datas) > 0) {
			seats = options.datas;
		}
		var seatColors = {};

		//创建元素
		function create() {
			var $table = $('<table class="seat-table animated-seat" cellpadding="0" cellspacing="0"></table>');
			for (var r = 1; r <= options.rows; r++) {
				var $tr = $('<tr></tr>');
				for (var c = 1; c <= options.cols; c++) {
					var sid = r + "_" + c; //座位ID
					var $td = $('<td data-row="' + r + '" data-col="' + c + '" id="' + sid + '"></td>');
					var $seat = $(`<span class="seat"></span>`);
					$seat.css({
						"width": options.size + 'px',
						"height": options.size + 'px'
					});
					try { //设置边框
						$td.data('thre_id', seats[sid].thre_id);
						$td.data('hall_id', seats[sid].hall_id);
						$td.data('movie_id', seats[sid].movie_id);
						$td.data("price", seats[sid].price);
						$td.data("groupName", seats[sid].groupName);
						// 设置标签分组name
						if (seats[sid].groupName) {
							$seat.attr("name", seats[sid].groupName)
						}
						//更改座位边框颜色
						if (seats[sid].color) {
							$seat.css({
								// "border-color": seats[sid].color,
								"background": seats[sid].color,
							});
							$td.data("color", seats[sid].color);
						}
					} catch (e) {}

					var td = document.querySelector('html');
					//绑定选座事件
					$td.on("click", function() {
						$(this).find('span').toggleClass("selected");
						var row = $(this).data('row'),
							col = $(this).data('col');
						var key = row + "_" + col;

						//根据不同的步骤执行不同的操作
						switch (options.step) {

							case 1: //排座
								if ($(this).hasSelected()) {
									seats[key] = {
										"row": row,
										"col": col,
										"price": 0,
										"color": "",
										"groupName": "",
										"thre_id": options.thre_id, //剧场ID
										"hall_id": options.hall_id, //厅ID
										"movie_id": options.hall_id, //电影场次ID
									};
								} else {
									delete seats[key];
								}
								break;

							case 2: //设置票价
								if ($(this).hasSelected()) {
									seatColors[key] = 1;
								} else {
									delete seatColors[key];
								}
								console.log(seatColors);
								console.log(Object.keys(seatColors).length);
								$("#groupNumber").text(Object.keys(seatColors).length)
								break;

							case 3: //购票
								var seat = {
									row: $(this).data("row"),
									col: $(this).data("col"),
									thre_id: $(this).data("thre_id"),
									hall_id: $(this).data("hall_id"),
									movie_id: $(this).data("movie_id"),
									price: $(this).data("price"),
									groupName: $(this).data("groupName"),
									color: $(this).data("color"),
								};
								//触发选中事件
								if ($(this).hasSelected()) {
									if (ticketNumber >= options.maxTicketNumber) {
										$(this).unselectSeat(); //选座失败,状态回滚
										options.onerror("一次最多选择 " + options.maxTicketNumber + " 个座位");
										return;
									} else { //选座成功
										options.selected[key] = 1;
										ticketNumber++;
										options.onSelected(seat);
									}
								} else {
									o.unselect(row, col);
									options.onUnselected(seat);
								}
								break;
							case 4: //系统分配座位
								var seat = {
									row: $(this).data("row"),
									col: $(this).data("col"),
									thre_id: $(this).data("thre_id"),
									hall_id: $(this).data("hall_id"),
									movie_id: $(this).data("movie_id"),
									price: $(this).data("price"),
									groupName: $(this).data("groupName"),
									color: $(this).data("color"),
								};
								//触发选中事件
								if ($(this).hasSelected()) {
									if (ticketNumber >= options.maxTicketNumber) {
										$(this).unselectSeat(); //选座失败,状态回滚
										options.onerror("一次最多选择 " + options.maxTicketNumber + " 个座位");
										return;
									} else { //选座成功
										options.selected[key] = 1;
										ticketNumber++;
										options.onSelected(seat);
									}
								} else {
									o.unselect(row, col);
									options.onUnselected(seat);
								}
								break;
						}

					}); //end onclick

					//隐藏没有座位的位置
					if (options.step != 1 && seats[sid] == undefined) {
						$seat.css({
							"border": "none",
							"background": "none",
							"cursor": "auto",
						});
						$td.off("click"); //卸载事件
						$td.off("mousedown"); //卸载事件
						$td.off("mouseup"); //卸载事件
					}

					//处理已选中座位
					if ((options.step == 3 || options.step == 4) && options.selected && options.selected[sid]) {
						$td.off("click"); //卸载事件
						$seat.addClass("disabled");
					}

					$td.append($seat);
					$tr.append($td);

					//如果是排作为，则默认选择全部座位
					if (options.step == 1) {
						$td.trigger("click");
					}
				}
				$table.append($tr);
			}

			//设置电影厅的宽度
			$(options.box).css({
				width: (options.cols * (options.size + 0 + 4)) + "px"
			});
			$(options.box).append($table);
		}

		//获取所有的座位信息
		o.getSeats = function() {
			return seats;
		}

		/**
		 * 设置价格
		 * @param price
		 * @param color
		 * @param groupName // 分组名称
		 */
		o.setPrice = function(price, color, groupName) {
			if (seatColors.length == 0) {
				return options.onerror("你没有选中任何座位");
			}
			for (var key in seatColors) {
				seats[key].color = color;
				seats[key].price = price;
				seats[key].groupName = groupName;
				//更改座位边框颜色
				$('#' + key).find("span").css({
					// "border-color": color,
					"background": color,
				}).removeClass("selected");
				// 设置分组class
				$('#' + key).find("span").attr("name", groupName)
			}
			seatColors = {}; //本次设置完毕，刷新缓存
		}

		//获取已选择的座位
		o.getSelectedSeats = function() {
			return options.selected;
		}

		//释放某个座位
		o.unselect = function(row, col) {
			var key = row + "_" + col;
			delete options.selected[key];
			$("#" + key).unselectSeat();
			ticketNumber--;
		}

		//获取对象元素个数
		function getObjLength(o) {
			var c = 0;
			for (var k in o) {
				c++;
			}
			return c;
		}

		// 框选方法
		function boxSelect(seatBoxId) {
			var oDiv;
			var selectedTD = new Array(); //创建被选中表格数组
			var TD = $("td"); //获取所有表格信息

			for (var i = 0; i < TD.length; i++) {
				selectedTD.push(TD[i]);
			}
			// console.log(selectedTD)
			// 获取要框选的范围id
			var oBox = document.getElementById(seatBoxId);
			// document.querySelector()
			//鼠标按下，获取初始点
			oBox.onmousedown = function(ev) {
				ev = window.event || ev;
				//1.获取按下的点
				var x1 = ev.clientX - oBox.offsetLeft;
				var y1 = ev.clientY - oBox.offsetTop;
				//2.创建div
				oDiv = document.createElement("div");
				oDiv.className = 'dragBox'
				oBox.appendChild(oDiv);
				oBox.onmousemove = function(ev) {
					ev = window.event || ev;
					var x2 = ev.clientX - oBox.offsetLeft;
					var y2 = ev.clientY - oBox.offsetTop;
					//3.设置div的样式
					oDiv.style.left = (x2 > x1 ? x1 : x2) + "px";
					oDiv.style.top = (y2 > y1 ? y1 : y2) + "px";
					oDiv.style.width = Math.abs(x2 - x1) + "px";
					oDiv.style.height = Math.abs(y2 - y1) + "px";


				}
				return false; //解除在划动过程中鼠标样式改变的BUG
			}
			//在鼠标抬起后终止onmousemove事件
			document.onmouseup = function() {
				console.log('end')

				// 通过框选来选中或取消
				let selectDiv = oDiv
				if (selectDiv == undefined) {
					return
				}
				var left = selectDiv.offsetLeft - 10;
				var top = selectDiv.offsetTop - 10;
				var width = selectDiv.offsetWidth;
				var height = selectDiv.offsetHeight;
				for (var i = 0; i < selectedTD.length; i++) {
					var sl = selectedTD[i].offsetWidth + selectedTD[i].offsetLeft + 2; // 鼠标x距离
					var st = selectedTD[i].offsetHeight + selectedTD[i].offsetTop + 3; // 鼠标y距离
					if (sl > left && st > top && selectedTD[i].offsetLeft < left + width && selectedTD[i].offsetTop <
						top +
						height) {
						if (selectedTD[i].className.indexOf("selected") == -1) {
							// selectedTD[i].className = "selected";
							let thisTd = selectedTD[i]
							let thisSpan = selectedTD[i].querySelector('span') // 获取dom元素
							// console.log($(thisSpan).attr("style"))

							if ($(thisSpan).attr("style").includes("background: none;")) {
								console.log(111)
							} else {
								// 添加或删除class
								$(thisSpan).toggleClass('selected')

								var row = $(thisTd).data('row');
								var col = $(thisTd).data('col');
								var key = row + "_" + col;

								//根据不同的步骤执行不同的操作
								switch (options.step) {
									case 1: //排座
										if ($(thisTd).hasSelected()) {
											seats[key] = {
												"row": row,
												"col": col,
												"price": 0,
												"color": "",
												'groupName': '',
												"thre_id": options.thre_id, //剧场ID
												"hall_id": options.hall_id, //厅ID
												"movie_id": options.hall_id, //电影场次ID
											};
										} else {
											delete seats[key];
										}
										break;
									case 2: //设置票价
										if ($(thisTd).hasSelected()) {
											seatColors[key] = 1;
										} else {
											delete seatColors[key];
										}
										console.log(seatColors);
										// 获取选中的个数
										console.log(Object.keys(seatColors).length);
										$("#groupNumber").text(Object.keys(seatColors).length)

										break;
								}
							}





						}
					} else {}
				}
				// 绘制完成 毁灭绘制框
				$(".dragBox").remove()
				oBox.onmousemove = null;
			}
		}

		create();
		// 框选方法
		if (options.step == 4) {

		} else {
			boxSelect('seatBox')
		}
		return o;
	}

	$.seats = function(options) {
		return new __seats__(options);
	};
})(jQuery);