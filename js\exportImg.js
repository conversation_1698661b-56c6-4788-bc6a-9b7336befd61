var main = {
	init: function(selectId) {
		main.setListener(selectId);
	},
	//设置监听事件
	setListener: function(selectId) {
		main.html2Canvas(selectId);
	},
	//获取像素密度
	getPixelRatio: function(context) {
		var backingStore = context.backingStorePixelRatio ||
			context.webkitBackingStorePixelRatio ||
			context.mozBackingStorePixelRatio ||
			context.msBackingStorePixelRatio ||
			context.oBackingStorePixelRatio ||
			context.backingStorePixelRatio || 1;
		return (window.devicePixelRatio || 1) / backingStore;
	},
	//绘制dom 元素，生成截图canvas
	html2Canvas: function(selectId) {
		var shareContent = $(selectId)[0]; // 须要绘制的部分的 (原生）dom 对象 ，注意容器的宽度不要使用百分比，使用固定宽度，避免缩放问题
		var width = shareContent.offsetWidth; // 获取(原生）dom 宽度
		var height = shareContent.offsetHeight; // 获取(原生）dom 高
		var offsetTop = shareContent.offsetTop; //元素距离顶部的偏移量

		var canvas = document.createElement('canvas'); //建立canvas 对象
		var context = canvas.getContext('2d');
		var scaleBy = main.getPixelRatio(context); //获取像素密度的方法 (也能够采用自定义缩放比例)
		canvas.width = width * scaleBy; //这里 因为绘制的dom 为固定宽度，居中，因此没有偏移
		canvas.height = (height + offsetTop) * scaleBy; // 注意高度问题，因为顶部有个距离因此要加上顶部的距离，解决图像高度偏移问题
		context.scale(scaleBy, scaleBy);

		// var canvas = document.createElement("canvas");
		// 获取元素相对于视窗的偏移量
		var rect = shareContent.getBoundingClientRect();
		// 设置context位置, 值为相对于视窗的偏移量的负值, 实现图片复位
		context.translate(-rect.left, -rect.top);
		var opts = {
			allowTaint: true,
			// 容许加载跨域的图片
			useCORS: true,
			tainttest: true, //检测每张图片都已经加载完成
			scale: scaleBy, // 添加的scale 参数
			canvas: canvas, //自定义 canvas
			logging: false, //日志开关，发布的时候记得改为false
			width: width, //dom 原始宽度
			height: height, //dom 原始高度
			scale: 1
		};
		html2canvas(shareContent, opts).then(function(canvas) {
			var img = Canvas2Image.convertToImage(canvas, canvas.width, canvas.height);
			var body = document.getElementsByTagName("body");
			img.setAttribute('width', width);
			img.setAttribute('height', height);
			$(".saveImg").attr("src", img.src);
			console.log(321)

			// 回显照片
			// $(selectId+"Img").find("img").attr("src",img.src);
			// $("#echartsImg").find("img").attr("src",img.src);
			// 下载图片
			var fileName = "img.png";
			saveFile(img.src, fileName);

			$(".saveTips").show()
		});
	}
};
// 保存文件函数
var saveFile = function(data, filename) {
	var save_link = document.createElementNS('http://www.w3.org/1999/xhtml', 'a');
	save_link.href = data;
	save_link.download = filename;
	var event = document.createEvent('MouseEvents');
	event.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
	save_link.dispatchEvent(event);
};