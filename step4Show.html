<!DOCTYPE html>
<html lang="ch">
	<head>
		<title>设置座位预览</title>
		<!-- 设置 viewport -->
		<!-- <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" /> -->
		<!-- IE -->
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<!-- 兼容国产浏览器的高速模式 -->
		<meta name="renderer" content="webkit">
		<meta name="Author" content="<EMAIL>" />
		<meta name="Keywords" content="" />
		<meta name="Description" content="" />
		<meta charset="UTF-8">
		<link rel="stylesheet" type="text/css" href="css/reset.css">
		<link rel="stylesheet" href="css/translateCommon.css">
		<link rel="stylesheet" href="layui/css/layui.css" />
		<link rel="stylesheet" href="css/stepPlug.css" />
		<link rel="stylesheet" href="plugin/spectrum/spectrum.css">
		<link rel="stylesheet" href="css/seat3.css" />
		<link rel="stylesheet" href="css/dragModule.css" />
		<link rel="stylesheet" href="css/step4only.css" />
		<link rel="stylesheet" href="css/step4Show.css" />
		<link rel="stylesheet" href="css/stepShowCommon.css" />
		<style>
			* {
				touch-action: pan-x pan-y;
			}
		</style>

	</head>
	<body>
		<div class="main-content">
			<div class="main-left">
				<!-- <div class="step-html" style="width: 600px;">
					<div class="step-box">
						<div class="step-list step-act">
							<span class="step-number">1</span>
						</div>
						<div class="step-list-html-text step-act">设置场地排列</div>
						<div class="step-line"></div>
						<div class="step-list step-act">
							<span class="step-number step-act">2</span>
						</div>
						<div class="step-list-html-text step-act">设置场地布局</div>
						<div class="step-line"></div>
						<div class="step-list step-act">
							<span class="step-number step-act">3</span>
						</div>
						<div class="step-list-html-text step-act">设置分区</div>
						<div class="step-line"></div>
						<div class="step-list step-act">
							<span class="step-number step-act">4</span>
						</div>
						<div class="step-list-html-text step-act">设置效果图</div>
					</div>
				</div> -->

				<!-- <div class="main-left-tab">
					<span class="main-left-tab-act" onclick="viewSrc('step4.html')">分区引导图设置</span>
					<span onclick="viewSrc('step5.html')">座位视图设置</span>
					<span onclick="viewSrc('step6.html')">区域视图设置</span>
				</div> -->

				<div class="zoomBox">
					<i class="layui-icon layui-icon-reduce-circle" onclick="zoomFn('in')"></i>
					<i class="layui-icon layui-icon-add-circle" onclick="zoomFn('out')"></i>
				</div>
				<div
					style="width: 100%;text-align: center;font-size: 25px;color: #000;font-weight: bold;margin-top: 15px;letter-spacing: 5px;"
					id="mySeat">
				</div>
				<!-- 座位遮罩层 -->
				<!-- <div class="seatFixed"></div> -->
				<div class="seatBoxContent seatBoxContentSetp4" id="seatBoxContent">



					<div class="seatBox" id="seatBox" style="width: auto;background: #e5e5e5;" zoom="1">

						<div class="dragContent">
							<div class="dragContent freeContainerModule">
								<!-- 组件  dragItemClicked coorClick-->
								<div class="ui-draggable freeDragItem " id="timestamp" onclick="freeDragli(this,event)">
									<div name="paper">
										<div style="position: relative;" class="freeBtbBox">
											<div class="freeweb_btn"><a src="#">组件</a></div>
											<div class="coor3 "></div>
											<img class="del_img" src="images/delete_hover.png" onclick="freeDeleteImg(this)" />
										</div>
									</div>
								</div>
							</div>
						</div>

					</div>

				</div>
				<!-- <div class="bottom-btn">
					<button class="layui-btn" type="button" onclick="editSeat()">修改区域</button>
					<button class="layui-btn" type="button" onclick="">保存</button>
					<button class="layui-btn" type="button" onclick="viewMySeat()">预览</button>
				</div> -->
			</div>
			<div class="main-right" style="display: none;">

			</div>
		</div>



		<!-- 灰色背景 -->
		<div id="fade" class="black_overlay"></div>



		<!-- <script src="js/jquery-1.7.2.min.js"></script> -->
		<script src="js/jquery-3.6.3.min.js"></script>
		<script src="js/index.js"></script>
		<script src="js/index.js"></script>
		<!-- <script src="js/jquery-ui-1.9.2.custom.min.js"></script> -->
		<script src="js/moveZoom.js"></script>
		<!-- <script src="js/jquery-ui.js"></script> -->
		<!-- <script src="js/dragModule.js"></script> -->


		<!-- <script>
			$(function() {
				// 动态加载可放大功能
				$.getScript('js/dragModule.js', function() {});
				// 动态加载可放大功能
				$.getScript('js/moveZoom.js', function() {
					all();
				});
			})
		</script> -->

		<script src="translateJs/translate.js"></script>
		<script src="translateJs/translateCommon.js"></script>
		<script src="plugin/spectrum/spectrum.js"></script>
		<script src="plugin/jdialog/JDialog.min.js"></script>
		<script src="js/seat.js"></script>
		<script src="layui/layui.js"></script>
		<script>
			var layer = "";
			var slider = "";
			var colorpickerRender = "";
			var setWidthSlider = ""; // 定义宽度滑块
			var setBorderWidthSlider = ""; // 边框宽度
			var setRadiusSlider = ""; // 定义圆角滑块
			var setFontSizeSlider = ""; // 定义字体大小滑块
			var noGroupNumber = 0;
			var noGroupHasAssign = 0; // 未分组已分配数量
			var form = "";
			var channelNumber = 0; // 批量通道人数
			var canUseNumber = 0; // 可用分配数量
			var canUseGroup = []; // 可用分配数组
			var channelSelectArray = JSON.parse(localStorage.getItem("channelSelectArrayStorage")) || []; // 已经分配过的通道

			var dragModuleWidth = 100;
			var dragModuleBorderWidth = 0


			layui.use(function() {
				form = layui.form;
				var element = layui.element;
				layer = layui.layer;

				element.on('tab(tab-hash)', function(obj) {
					let thisIndex = obj.index
					if (thisIndex == 1) {
						$(".dragItemClicked").removeClass("dragItemClicked")
						$(".coorClick").removeClass("coorClick")
					}
				});

				// 获取padding 值
				var div = document.getElementById("seatBox");
				var computedStyle = getComputedStyle(div, null);
				var paddingTop = Number(computedStyle.paddingTop.replace("px", ""))
				var paddingLeft = Number(computedStyle.paddingLeft.replace("px", ""))
				var paddingRight = Number(computedStyle.paddingRight.replace("px", ""))
				var paddingBottom = Number(computedStyle.paddingBottom.replace("px", ""))


				// 滑块
				slider = layui.slider;

				// 颜色选择器
				colorpickerRender = layui.colorpicker;









			});

			$(function() {
				// getSize()
			})
			// 获取初始宽高
			function getSize() {
				console.log(123)
				var div = document.getElementById("seatBox");
				var computedStyle = getComputedStyle(div, null);

				let w = $("#seatBox").width() + Number(computedStyle.paddingLeft.replace("px", "")) + Number(computedStyle
					.paddingRight.replace("px", ""))
				let h = $("#seatBox").height() + Number(computedStyle.paddingTop.replace("px", "")) + Number(computedStyle
					.paddingBottom.replace("px", ""))
				$(".bgSize").html(`背景宽度：${w} 背景高度：${h}`)
			}

			// 放大缩小
			function zoomFn(obj) {
				let zoomVal = Number((Number($("#seatBox").attr("zoom"))).toFixed(1))
				if (obj == "in") {
					if (zoomVal >= 0.2) {
						zoomVal = zoomVal - 0.1
						$("#seatBox").css({
							transform: "scale(" + zoomVal + ")"
						})
					}

				} else if (obj == "out") {

					if (zoomVal < 1) {
						zoomVal = zoomVal + 0.1
						$("#seatBox").css({
							transform: "scale(" + zoomVal + ")"
						})
					}

				}
				$("#seatBox").attr("zoom", zoomVal)
			}
		</script>
		<script>
			let seatRows = localStorage.getItem("seatRows");
			let seatCols = localStorage.getItem("seatCols");
			console.log(seatRows)
			console.log(seatCols)

			// var groupObject = JSON.parse(localStorage.getItem('groupArrayObject')) || []
			try {
				var datas = JSON.parse(localStorage.getItem("seats"));
				var selected = JSON.parse(localStorage.getItem("selected"));
			} catch (e) {}
			var seats = $.seats({
				box: "#seatBox",
				rows: seatRows,
				cols: seatCols,
				size: 20, //座位的尺寸，单位：像素	
				hall_id: 10, //厅，如：5号厅
				thre_id: 20, //剧场，如：大地电影院中天广场沃尔玛店
				movie_id: 30, //场次ID
				step: 5,
				maxTicketNumber: 1000, //最多购票张数
				datas: datas,
				selected: selected || {},
				onSelected: function(seat) {

					/*************/
					var id = "selected_" + seat.row + "_" + seat.col;
					// 获取分区名称
					console.log(seat.color)
					let getGroupName = seat.groupName
					let getGroupLength = $(`span[name='${getGroupName}']`).length
					// console.log(getGroupLength)

					// 判断是否已经分配的座位
					let hasDisabled = $(`#${seat.row}_${seat.col}`).find("span").hasClass("disabledCancel")
					console.log(hasDisabled)
					// var selectedHtml = ''
					// 获取真实的座位号（去除空格的)
					let realSeat = $(`#${seat.row}_${seat.col}`).attr("seat").split("-")
					console.log(realSeat)

					/*********************************/
					return true;
				},
				onUnselected: function(seat) {
					var id = "selected_" + seat.row + "_" + seat.col;
					$("#" + id).remove();
				},
				onerror: function(msg) {
					// JDialog.msg({
					// 	type: 'error',
					// 	content: msg,
					// 	container: "#seatBox"
					// });
					layer.msg(msg, {
						icon: 2,
						time: 1000 // 设置 2 秒后自动关闭
					});
				}
			});



			// 所属区域高亮

			$(function() {
				// let hrefSrc = window.location.href.split("?")[1].split("-")
				let row = 4
				let col = 18
				let seat = `${row}-${col}`
				console.log(seat)
				let groupName = $(`td[seat='${seat}']`).find("span").attr("name")
				console.log(groupName)
				// 回显座位到页面
				$("#mySeat").html(`我的座位：${groupName}区 - ${row}排${col}座`)

				// $(`td[seat='${seat}']`).find("span").html(seat).css({
				// 	color: "#fff",
				// 	fontSize: "10px",
				// 	lineHeight: "20px"
				// })
				// console.log($("td").find(`span[name='${groupName}']`).)
				let groupBg = $("td").find(`span[name='${groupName}']`).attr("style").split("background")[1].replace(":", "")
				groupBg = groupBg.replace(";", "")
				// 遍历所有可选座位
				$("td").each(function() {
					let backNone = $(this).find("span").attr("style")
					if (!backNone.includes("background: none;")) {
						$(this).find("span").css({
							background: '#d7d7d7',
							opacity: 0.5
						})
					}
				})

				// $("td").find("span").css({
				// 	background: '#d7d7d7'
				// })
				$("td").find("span").removeClass("disabled")


				$("td").find(`span[name='${groupName}']`).css({
					background: groupBg,
					opacity: 0.5
				})
				$(`td[seat='${seat}']`).find("span").addClass("disabled")
				$(`td[seat='${seat}']`).find("span").css({
					opacity: 1
				})
			})


			// $(function() {
			// 	// let hrefSrc = window.location.href.split("?")[1].split("-")
			// 	let row = 4
			// 	let col = 18
			// 	let seat = `${row}-${col}`
			// 	console.log(seat)
			// 	let groupName = $(`td[seat='${seat}']`).find("span").attr("name")
			// 	console.log(groupName)
			// 	// 回显座位到页面
			// 	$("#mySeat").html(`我的座位：${groupName}区 - ${row}排${col}座`)

			// 	// $(`td[seat='${seat}']`).find("span").html(seat).css({
			// 	// 	color: "#fff",
			// 	// 	fontSize: "10px",
			// 	// 	lineHeight: "20px"
			// 	// })
			// 	// console.log($("td").find(`span[name='${groupName}']`).)
			// 	let groupBg = $("td").find(`span[name='${groupName}']`).attr("style").split("background")[1].replace(":", "")
			// 	groupBg = groupBg.replace(";", "")
			// 	// 遍历所有可选座位
			// 	$("td").each(function() {
			// 		let backNone = $(this).find("span").attr("style")

			// 		if (!backNone.includes("background: none;")) {
			// 			$(this).find("span").css({
			// 				background: '#666',
			// 				// background: 'transparent',
			// 				// border: "1px solid #666",
			// 				opacity: 0.5
			// 			})
			// 			// 父级td也加上背景
			// 			// $(this).find("span").parent().css({
			// 			// 	// background: '#666',
			// 			// 	background: 'transparent',
			// 			// 	"border-bottom": "3px solid #666",
			// 			// 	// opacity: 0.5
			// 			// })
			// 		}
			// 	})

			// 	// $("td").find("span").css({
			// 	// 	background: '#d7d7d7'
			// 	// })
			// 	$("td").find("span").removeClass("disabled")


			// 	$("td").find(`span[name='${groupName}']`).css({
			// 		// background: groupBg,
			// 		// background: '#333',
			// 		background: 'transparent',
			// 		// opacity: 0.5,
			// 		border: "1px solid #666"
			// 	})
			// 	// // 所属区域父级td也加上背景
			// 	$("td").find(`span[name='${groupName}']`).parent().css({
			// 		background: 'transparent',
			// 		// background: groupBg,
			// 		// opacity: 0.5
			// 	})

			// 	$(`td[seat='${seat}']`).find("span").addClass("disabled")
			// 	$(`td[seat='${seat}']`).find("span").css({
			// 		opacity: 1,
			// 		background: groupBg,
			// 	})
			// })

			function editSeat() {
				window.location.href = "step3.html"
			}
			// 预览
			function viewMySeat() {
				window.location.href = "mySeat.html"
			}

			function viewSrc(src) {
				window.location.href = src
			}


			// 滚动条滚到到正中间
			function scrollCenter() {
				// console.log(1234)
				var div = document.getElementById("seatBox");
				var computedStyle = getComputedStyle(div, null);
				var paddingTop = Number(computedStyle.paddingTop.replace("px", ""))
				var paddingLeft = Number(computedStyle.paddingLeft.replace("px", ""))
				var paddingRight = Number(computedStyle.paddingRight.replace("px", ""))
				var paddingBottom = Number(computedStyle.paddingBottom.replace("px", ""))
				// let tableWidth = $(".dragContent").width()
				// let tableHeight = $(".dragContent").height()

				let tableWidth = $("#seatBox").width() + Number(computedStyle.paddingLeft.replace("px", "")) + Number(
					computedStyle.paddingRight.replace("px", ""))
				let tableHeight = $("#seatBox").height() + Number(computedStyle.paddingTop.replace("px", "")) + Number(
					computedStyle.paddingBottom.replace("px", ""))

				let scrollWidth = $(".seatBoxContent").width()
				// let scrollHeight = $(".seatBoxContent").height()
				// 可见区域的高度
				let scrollHeight = document.documentElement.clientHeight

				let srollLeft = (tableWidth - scrollWidth) / 2
				let srollTop = (tableHeight - scrollHeight) / 2
				// console.log("left:" + srollLeft)
				// console.log("top:" + srollTop)
				if (srollLeft > 0) {
					$(".seatBoxContent").scrollLeft(srollLeft)
				}
				if (srollTop > 0) {
					// $("body,html").scrollTop(srollTop)
					setTimeout(function() {
						$("body,html").scrollTop(srollTop)
					}, 100)
				}

				// console.log("总高度-" + tableHeight)
				// console.log("可见区域高度-" + scrollHeight)
				// console.log($(".seatBox").attr("paading-left"))
			}


			$(function() {
				scrollCenter()
			})
		</script>

		<!-- 新增组件设置js -->
		<script src="js/seatModule4.js"></script>
		<script>
			$(function() {
				let bgColor = $('#seatBox').css("background")
				console.log(bgColor)
				$("body").css({
					background: bgColor
				})
			})
		</script>

	</body>
</html>