<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Step2 逻辑测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border-color: #ffeaa7;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border-color: #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .data-display {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Step2 座位布局逻辑测试</h1>
        
        <div class="test-section success">
            <h3>✅ Step2 清理完成</h3>
            <p><strong>已完成的修改：</strong></p>
            <ul>
                <li>✅ <strong>初始状态</strong>：场地默认为满座状态（所有位置都有座位）</li>
                <li>✅ <strong>操作方式</strong>：点击座位是<strong>取消选择</strong>该位置</li>
                <li>✅ <strong>移除模式切换</strong>：删除了"分区设置"模式按钮</li>
                <li>✅ <strong>简化界面</strong>：移除了分区相关的工具栏和右侧面板</li>
                <li>✅ <strong>专注功能</strong>：Step2现在只专注于座位布局</li>
                <li>🎯 <strong>目的</strong>：留出走廊、过道、安全出口等空间</li>
                <li>💾 <strong>保存结果</strong>：实际需要保留的座位位置</li>
            </ul>
        </div>

        <div class="test-section info">
            <h3>🧪 测试步骤</h3>
            <ol>
                <li><strong>Step 1</strong>: 设置场地参数（如：20行 × 30列 = 600个位置）</li>
                <li><strong>Step 2</strong>: 
                    <ul>
                        <li>初始状态：600个位置全部有座位</li>
                        <li>操作：点击取消不需要的座位（如中间走廊、两侧过道）</li>
                        <li>结果：可能保留500个座位，取消100个位置</li>
                    </ul>
                </li>
                <li><strong>Step 3</strong>: 对这500个座位进行分区设置</li>
            </ol>
        </div>

        <div class="test-section warning">
            <h3>⚠️ 测试要点</h3>
            <ul>
                <li>页面加载后，应该看到所有座位都是选中状态（满座）</li>
                <li>点击座位应该是<strong>取消选择</strong>（座位消失）</li>
                <li>再次点击空位应该是<strong>恢复座位</strong>（座位出现）</li>
                <li>右侧面板应该显示正确的说明文字</li>
                <li>工具按钮应该显示"切换座位"和"批量取消"</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🔗 测试链接</h3>
            <p>点击下面的链接进行测试：</p>
            <button onclick="window.open('step1.html', '_blank')">Step 1: 设置场地排列</button>
            <button onclick="window.open('step2.html', '_blank')">Step 2: 设置场地布局</button>
            <button onclick="window.open('step3.html', '_blank')">Step 3: 设置分区</button>
        </div>

        <div class="test-section">
            <h3>📊 数据检查</h3>
            <button onclick="checkData()">检查localStorage数据</button>
            <button onclick="clearData()">清除所有数据</button>
            <div id="dataDisplay" class="data-display"></div>
        </div>

        <div class="test-section">
            <h3>✅ 清理后的效果</h3>
            <p>现在Step2应该：</p>
            <ul>
                <li>✅ 看到场地初始为满座状态</li>
                <li>✅ 只有座位布局工具（没有分区设置模式）</li>
                <li>✅ 通过点击取消不需要的座位</li>
                <li>✅ 使用"切换座位"和"批量取消"工具</li>
                <li>✅ 轻松留出走廊和过道空间</li>
                <li>✅ 保存实际需要的座位布局</li>
                <li>✅ 界面更简洁，专注于座位布局功能</li>
                <li>🔄 在Step3中对保留的座位进行分区</li>
            </ul>
        </div>
    </div>

    <script>
        function checkData() {
            const seatRows = localStorage.getItem('seatRows');
            const seatCols = localStorage.getItem('seatCols');
            const seats = localStorage.getItem('seats');
            const zones = localStorage.getItem('zones');
            
            let html = '<h4>当前localStorage数据:</h4>';
            html += `<p><strong>场地参数:</strong> ${seatRows || '未设置'}行 × ${seatCols || '未设置'}列</p>`;
            
            if (seats) {
                const seatsData = JSON.parse(seats);
                const totalPositions = (parseInt(seatRows) || 0) * (parseInt(seatCols) || 0);
                const selectedSeats = Object.keys(seatsData).length;
                const emptySpaces = totalPositions - selectedSeats;
                
                html += `<p><strong>座位数据:</strong></p>`;
                html += `<p>- 总位置: ${totalPositions}个</p>`;
                html += `<p>- 已选座位: ${selectedSeats}个</p>`;
                html += `<p>- 空余位置: ${emptySpaces}个</p>`;
                html += `<p>- 座位利用率: ${totalPositions > 0 ? Math.round(selectedSeats/totalPositions*100) : 0}%</p>`;
            } else {
                html += '<p><strong>座位数据:</strong> 未设置</p>';
            }
            
            if (zones) {
                const zonesData = JSON.parse(zones);
                html += `<p><strong>分区数据:</strong> ${zonesData.length}个分区</p>`;
            } else {
                html += '<p><strong>分区数据:</strong> 未设置</p>';
            }
            
            document.getElementById('dataDisplay').innerHTML = html;
        }
        
        function clearData() {
            if (confirm('确定要清除所有数据吗？')) {
                localStorage.removeItem('seatRows');
                localStorage.removeItem('seatCols');
                localStorage.removeItem('seats');
                localStorage.removeItem('zones');
                alert('所有数据已清除');
                checkData();
            }
        }
        
        // 页面加载时检查数据
        window.onload = function() {
            checkData();
        };
        
        // 定期更新数据显示
        setInterval(checkData, 3000);
    </script>
</body>
</html>
