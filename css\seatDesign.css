.c_right {
	background: #fff;
}

.main-content {
	width: 100%;
	height: calc(100vh - 100px);
	position: relative;
}

.main-html {
	width: 80%;
	overflow: initial;
	max-width: 800px;
	margin: 0 auto;
	padding: 30px 20px;
	box-sizing: border-box;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

.seat-setting {
	width: 100%;
	overflow: initial;
	margin-top: 50px;
}

.layui-form-label {
	width: 200px;
}

.layui-input-block {
	margin-left: 200px;
}

.bottom-btn {
	width: 100%;
	overflow: hidden;
	text-align: center;
	margin-top: 20px;
}

.seat-setting-tips {
	width: 100%;
	overflow: hidden;
	margin: 15px auto 30px;
	font-size: 14px;
	color: #1890FF;
}

.layui-btn {
	background: #1890FF;
}