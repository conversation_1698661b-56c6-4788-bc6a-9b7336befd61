<!--
座位图展示组件 - 可复用版本
功能：生成和展示座位图，支持拖动、缩放、圆角、文字等
-->
<template>
  <div class="seat-map-viewer" :style="{ width: width + 'px', height: height + 'px' }">
    <!-- 画布容器 -->
    <div class="canvas-container" ref="canvasContainer">
      <canvas 
        ref="canvas"
        :width="canvasState.width" 
        :height="canvasState.height"
        @mousedown="onMouseDown"
        @mousemove="onMouseMove"
        @mouseup="onMouseUp"
        @wheel="onWheel"
        @contextmenu.prevent
      ></canvas>
    </div>
    
    <!-- 控制面板 -->
    <div class="controls" v-if="showControls">
      <!-- 缩放控制 -->
      <div class="zoom-controls">
        <button class="zoom-btn" @click="zoomOut" title="缩小">-</button>
        <button class="zoom-btn" @click="resetZoom" title="重置">⌂</button>
        <button class="zoom-btn" @click="zoomIn" title="放大">+</button>
        <span class="zoom-display">{{ Math.round(canvasState.zoom * 100) }}%</span>
        <span class="detail-status" :class="{ active: canvasState.showText }">
          {{ canvasState.showText ? '🎯 详细' : '🎯̶ 详细' }}
        </span>
      </div>
      
      <!-- 生成控制 -->
      <div class="generate-controls">
        <label>行数:</label>
        <input v-model.number="rows" type="number" min="1" max="100" />
        <label>列数:</label>
        <input v-model.number="cols" type="number" min="1" max="100" />
        <button @click="generateSeats" class="generate-btn">生成座位</button>
      </div>
      
      <!-- 配置控制 -->
      <div class="config-controls">
        <label>座位大小:</label>
        <input v-model.number="config.seatSize" type="range" min="16" max="48" />
        <span>{{ config.seatSize }}px</span>
        
        <label>座位间距:</label>
        <input v-model.number="config.seatSpacing" type="range" min="2" max="10" />
        <span>{{ config.seatSpacing }}px</span>
      </div>
    </div>
    
    <!-- 统计信息 -->
    <div class="stats" v-if="showStats">
      <div>总座位: {{ stats.totalSeats }}</div>
      <div>可见座位: {{ stats.visibleSeats }}</div>
      <div>渲染时间: {{ stats.renderTime }}ms</div>
      <div>FPS: {{ stats.fps }}</div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue'
import { SeatMapEngine } from './SeatMapEngine.js'

export default {
  name: 'SeatMapViewer',
  props: {
    // 组件尺寸
    width: { type: Number, default: 800 },
    height: { type: Number, default: 600 },
    
    // 初始配置
    initialRows: { type: Number, default: 10 },
    initialCols: { type: Number, default: 10 },
    initialSeatSize: { type: Number, default: 24 },
    initialSeatSpacing: { type: Number, default: 4 },
    
    // 显示控制
    showControls: { type: Boolean, default: true },
    showStats: { type: Boolean, default: true },
    
    // 座位数据
    seatData: { type: Array, default: () => [] },
    
    // 事件回调
    onSeatClick: { type: Function, default: () => {} },
    onSeatHover: { type: Function, default: () => {} },
    onViewChange: { type: Function, default: () => {} }
  },
  
  emits: ['seat-click', 'seat-hover', 'view-change', 'seats-generated'],
  
  setup(props, { emit }) {
    // 响应式数据
    const canvas = ref(null)
    const canvasContainer = ref(null)
    const rows = ref(props.initialRows)
    const cols = ref(props.initialCols)
    
    // 配置
    const config = reactive({
      seatSize: props.initialSeatSize,
      seatSpacing: props.initialSeatSpacing,
      showSeatNumbers: true,
      enableRoundedCorners: true
    })
    
    // Canvas状态
    const canvasState = reactive({
      width: props.width,
      height: props.height,
      zoom: 1,
      offsetX: 0,
      offsetY: 0,
      isDragging: false,
      lastMouseX: 0,
      lastMouseY: 0,
      isHighPerformanceMode: false,
      showText: true,
      cornerTransition: 1,
      forceRoundedCorners: false
    })
    
    // 统计信息
    const stats = reactive({
      totalSeats: 0,
      visibleSeats: 0,
      renderTime: 0,
      fps: 60
    })
    
    // 座位数据
    const seats = ref([])
    
    // 座位图引擎实例
    let engine = null

    // 初始化引擎
    const initEngine = () => {
      if (!canvas.value) return

      engine = new SeatMapEngine(canvas.value, {
        seatSize: config.seatSize,
        seatSpacing: config.seatSpacing,
        onSeatClick: (seat) => {
          emit('seat-click', seat)
          props.onSeatClick(seat)
        },
        onSeatHover: (seat) => {
          emit('seat-hover', seat)
          props.onSeatHover(seat)
        },
        onViewChange: (view) => {
          Object.assign(stats, view.stats)
          emit('view-change', view)
          props.onViewChange(view)
        }
      })

      // 生成初始座位
      generateSeats()
    }

    // 生成座位
    const generateSeats = () => {
      if (!engine) return
      const seatData = engine.generateSeats(rows.value, cols.value)
      seats.value = seatData
      stats.totalSeats = seatData.length
      emit('seats-generated', { total: seatData.length, seats: seatData })
    }

    // 缩放控制
    const zoomIn = () => engine?.zoomIn()
    const zoomOut = () => engine?.zoomOut()
    const resetZoom = () => engine?.resetZoom()

    // 鼠标事件（委托给引擎处理）
    const onMouseDown = (e) => engine?.handleMouseDown?.(e)
    const onMouseMove = (e) => engine?.handleMouseMove?.(e)
    const onMouseUp = (e) => engine?.handleMouseUp?.(e)
    const onWheel = (e) => engine?.handleWheel?.(e)

    // 生命周期
    onMounted(() => {
      initEngine()
    })

    onUnmounted(() => {
      engine?.destroy?.()
    })

    // 监听配置变化
    watch([() => config.seatSize, () => config.seatSpacing], () => {
      if (engine) {
        engine.config.seatSize = config.seatSize
        engine.config.seatSpacing = config.seatSpacing
        generateSeats()
      }
    })

    return {
      canvas,
      canvasContainer,
      rows,
      cols,
      config,
      canvasState,
      stats,
      seats,
      generateSeats,
      zoomIn,
      zoomOut,
      resetZoom,
      onMouseDown,
      onMouseMove,
      onMouseUp,
      onWheel
    }
  }
}
</script>

<style scoped>
.seat-map-viewer {
  position: relative;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
  background: #f9f9f9;
}

.canvas-container {
  position: relative;
  overflow: hidden;
}

canvas {
  display: block;
  cursor: grab;
  background: white;
}

canvas:active {
  cursor: grabbing;
}

.controls {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(255, 255, 255, 0.9);
  padding: 10px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.zoom-controls, .generate-controls, .config-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.zoom-btn, .generate-btn {
  padding: 4px 8px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
}

.zoom-btn:hover, .generate-btn:hover {
  background: #f0f0f0;
}

.zoom-display {
  font-size: 12px;
  color: #666;
}

.detail-status {
  font-size: 12px;
  color: #9CA3AF;
}

.detail-status.active {
  color: #10B981;
}

.stats {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
}

.stats div {
  margin-bottom: 2px;
}

input[type="number"] {
  width: 60px;
  padding: 2px 4px;
  border: 1px solid #ddd;
  border-radius: 3px;
}

input[type="range"] {
  width: 80px;
}

label {
  font-size: 12px;
  color: #666;
}
</style>
