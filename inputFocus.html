<!DOCTYPE html>
<html>
	<head lang="en">
		<meta charset="UTF-8">
		<title>输入框</title>
		<!-- 设置 viewport -->
		<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
		<!-- IE -->
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<!-- 兼容国产浏览器的高速模式 -->
		<meta name="renderer" content="webkit">
		<meta name="Author" content="<EMAIL>" />
		<meta name="Keywords" content="" />
		<meta name="Description" content="" />
		<meta charset="UTF-8">
		<style>
			* {
				padding: 0;
				margin: 0;
			}

			.commonBox {
				width: 100%;
				max-width: 800px;
				margin: 0 auto;
			}

			.inputFlex {
				width: 100%;
				display: flex;
				position: relative;
				margin: 20px auto;
				align-items: center;
			}

			.inputFlex input {
				flex: 1;
				width: 100px;
				border: 0;
				outline-style: none;
				height: 35px;
			}

			.inputBox {
				position: relative;
				width: 100%;
				overflow: hidden;
			}

			.positionBox {
				position: absolute;
				width: 100%;
				height: 100%;
				top: 0;
				left: 0;
			}

			#focusInput {
				border: 1px solid #ddd;
			}
		</style>
	</head>
	<body>
		<div class="commonBox">
			<div class="inputFlex">
				<span>输入1：</span>
				<input type="text" />
			</div>
			<div class="inputBox">
				<div class="inputFlex">
					<span>输入2：</span>
					<input type="text" id="inputText" />
				</div>
				<!-- 遮罩 -->
				<div class="positionBox"></div>
			</div>
			<div class="inputFlex">
				<span>获取焦点：</span>
				<input type="text" id="focusInput" />
			</div>
		</div>
		<script>
			document.getElementById("focusInput").focus()
			document.getElementById("inputText").value = "测试数据"
		</script>
	</body>
</html>