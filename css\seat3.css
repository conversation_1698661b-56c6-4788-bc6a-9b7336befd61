body {
	/* text-align: center; */
	overflow: hidden;
	padding: 0;
}

body .animated-seat {
	animation-duration: .9s;
	animation-fill-mode: both;
}

.c_right {
	background: #fff;
}

.main-content {
	width: 100%;
	overflow: hidden;
	height: calc(100vh - 100px);
	display: flex;
	box-sizing: border-box;
}

.main-left {
	flex: 1;
	/* width: 800px; */
	height: 100%;
	overflow: hidden;
	/* overflow-y: auto; */
	/* overflow-x: auto; */
	padding: 15px 15px;
	box-sizing: border-box;
	position: relative;
}

.main-right {
	width: 380px;
	height: 100%;
	overflow-y: auto;
	border-left: 1px solid #ddd;
}

@keyframes zoomIn {
	from {
		opacity: 0;
		transform: scale3d(0.3, 0.3, 0.3);
	}

	50% {
		opacity: 1;
	}
}

.seatBoxContent {
	width: 100%;
	overflow-x: auto;
	overflow-y: auto;
	height: calc(100vh - 200px);
	box-sizing: border-box;
	margin-top: 20px;
	/* position: relative; */
}

.seatBoxContentSetp2 {
	height: calc(100vh - 195px);
}

.seatBoxContentSetp4 {
	height: calc(100vh - 240px);
}

body .seatBox {
	background-color: #f5f5f5;
	padding: 20px;
	position: relative;
	margin: 0 auto;
	box-sizing: content-box;
	border: 1px solid #ddd;
	/* background: url(../images/bg.jpg) 0 0 no-repeat; */
	background-size: 100% 100%;
	transition: transform 0.5s ease;
}

.seatFixed {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: transparent;
	z-index: 111;
}

.dragBox {
	border: 1px solid green;
	position: absolute;
	z-index: 1;
}

body .seatBox .seat-table {
	margin: auto auto 20px auto;
	padding: 0;
	width: 100%;
	/* animation-name: zoomIn; */
}

body .seatBox .seat-table tr td {
	padding: 2px;
	margin: 0;
	height: 32px;
}

body .seatBox .seat-table tr .seat {
	display: block;
	/* border: 1px solid #cccccc; */
	cursor: pointer;
	background-color: #e5e5e5;
	border-radius: 3px;
	box-sizing: border-box;
	margin: 4px auto;
}

body .seatBox .seat-table tr .selected {
	/* background-color: #fff !important; */
	/* 	background-image: url(../images/selectIcon.png) !important;
	background-position: 0 0 !important;
	background-size: 100% 100% !important; */
}

body .seatBox .seat-table tr .disabled {
	/* background: url("checked.png") no-repeat center center !important;
	background-size: 13px 13px !important; */
	background-image: url("checked.png") !important;
	background-size: 100% 100% !important;
	/* border: 1px solid #cc0000 !important; */
	/* cursor: no-drop; */
}

body .seatBox .seat-table tr .disabledCancel {
	background-image: url("checkedCancel.png") !important;
	background-size: 100% 100% !important;
	/* cursor: no-drop; */
}

.layui-tab {
	margin-top: 0;
}

.layui-tab-content {
	padding: 15px 10px !important;
	box-sizing: border-box;
}

.content-input {
	width: 100%;
	overflow: initial;
	margin-top: 10px;
	display: flex;
	align-items: center;
}

.flex-column {
	flex-direction: column;
}

.content-input-title {
	/* width: 100%; */
	overflow: hidden;
	/* margin-bottom: 5px; */
	color: #333;
	font-weight: bold;
	font-size: 14px;
	width: 100px;
	text-align: right;
}

.flex-column .content-input-title {
	width: 100%;
	text-align: left;
	margin-bottom: 5px;
}

.content-input-box {
	width: 100%;
	overflow: initial;
}

.bottom-btn {
	width: 100%;
	overflow: hidden;
	text-align: center;
	margin-top: 30px;
}

.layui-btn {
	background: #1890FF;
}

.groupBox {
	width: 100%;
	overflow: hidden;
	margin-top: 10px;
	border-bottom: 1px solid #ddd;
}

.groupUlBox {
	width: 100%;
	overflow: hidden;
}

.groupUlBox li {
	width: 100%;
	overflow: hidden;
	display: flex;
	align-items: center;
	margin-bottom: 20px;
}

.groupUlBox li .colorSpan {
	width: 20px;
	height: 20px;
	margin-right: 5px;
}

.text-danger {
	color: #ff5722;
}

.groupText,
.groupText2 {
	flex: 1;
	margin-right: 8px;
}

.groupAllocation {
	color: #4078cb;
	cursor: pointer;
}

.seatDatas {
	width: 100%;
	overflow: hidden;
	font-size: 14px;
	color: #333;
	margin-bottom: 10px;
}

.groupDetail {
	width: 100%;
	overflow: inherit;
}

.groupDetailUl {
	width: 100%;
	overflow: hidden;
	margin-top: 10px;
}

.groupDetailUl li {
	width: 100%;
	overflow: hidden;
	display: flex;
	align-items: center;
	margin-bottom: 20px;
}

.groupDetailUl li .colorSpan {
	width: 20px;
	height: 20px;
	margin-right: 5px;
}

.addItem {
	width: 100%;
	overflow: hidden;
}

.canUseBox,
.canUsePerson {
	width: 100%;
	overflow: hidden;
	margin-top: 10px;
}

.selected-ul {
	width: 100%;
	overflow: initial;
}

.selected-ul li {
	width: 100%;
	overflow: initial;
	border: 1px solid #ddd;
	padding: 10px 15px;
	box-sizing: border-box;
	position: relative;
	margin-bottom: 25px;
}

.groupDetailBox {
	width: 100%;
	overflow: hidden;
	display: flex;
	align-items: center;
}

.colorSpan {
	width: 20px;
	height: 20px;
	margin-right: 5px;
	background: #4078cb;
}

.personDetail,
.codeDetail {
	width: 100%;
	overflow: hidden;
	margin-top: 10px;
}

.personDetailSearch {
	width: 100%;
	overflow: hidden;
	margin-top: 10px;
	display: flex;
	align-items: center;
}

.personDetailSearchLeft {
	flex: 1;
	overflow: hidden;
}

.personDetailList {
	width: 100%;
	overflow: hidden;
	display: flex;
	align-items: center;
	margin: 8px auto;
}

.personCancelBtn {
	width: 100%;
	text-align: right;
	margin-top: 10px;
}

.codeDetailList {
	width: 100%;
	overflow: hidden;
}

.codeDetailInput {
	width: 100%;
	overflow: hidden;
	display: flex;
	align-items: center;
}

.codeDetailInput input {
	flex: 1;
	margin-right: 15px;
}

.codeDetailTitle {
	width: 100%;
	overflow: hidden;
	font-size: 14px;
	margin: 8px auto;
}

.selected-delete {
	color: red;
	font-size: 25px;
	position: absolute;
	right: -12px;
	top: -12px;
	cursor: pointer;
}

.sp-hidden {
	/* display: block !important;
	position: absolute;
	top: -150px; */
}

/* .layui-form-label {
	width: 100px;
}

.layui-input-block {
	margin-left: 110px;
} */


.seat-setting-tips {
	width: 100%;
	overflow: hidden;
	margin: 20px auto;
	font-size: 14px;
	color: #1890FF;
	text-align: center;
}

.layui-btn {
	background: #4078cb !important;
}

.newSeatBox {
	position: relative;
	/* margin-left: 15px; */
	margin: 0 auto;
}

.tdBox {
	position: sticky;
	display: flex;
	top: 3px;
	left: 15px;
	margin-top: -18px;
	z-index: 1;
	padding-left: 20px;
}

.trBox {
	position: sticky;
	display: flex;
	flex-direction: column;
	top: -10px;
	left: 1px;
	width: 20px;
	/* padding-top: -15px; */
	margin-left: -20px;
	/* margin-top: -20px; */
	vertical-align: top;
	float: left;
}

.tdBox span {
	width: 18px;
	height: 18px;
	box-sizing: border-box;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 1px solid #ddd;
	border-radius: 2px;
	margin: 0 3px;
	font-size: 11px;
	background: #fff;
}

.trBox span {
	width: 18px;
	height: 18px;
	box-sizing: border-box;
	display: flex;
	align-items: center;
	justify-content: center;
	border: 1px solid #ddd;
	border-radius: 2px;
	margin: 7px 0;
	font-size: 11px;
	background: #fff;
}

.loading-module {
	width: 100%;
	height: 100%;
	position: fixed;
	background: rgba(0, 0, 0, .4);
	top: 0;
	left: 0;
	z-index: 11;
}

.loading-module-bg {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
}

.loadingSix {
	width: 100px;
	height: 100px;
	position: relative;
	margin: 0 auto;
}

.loadingSix span {
	display: inline-block;
	width: 16px;
	height: 16px;
	border-radius: 50%;
	background: #4078cb;
	position: absolute;
	-webkit-animation: load 1.04s ease infinite;
}

@-webkit-keyframes load {
	0% {
		opacity: 1;
	}

	100% {
		opacity: 0.2;
	}
}

.loadingSix span:nth-child(1) {
	left: 0;
	top: 50%;
	margin-top: -8px;
	-webkit-animation-delay: 0.13s;
}

.loadingSix span:nth-child(2) {
	left: 14px;
	top: 14px;
	-webkit-animation-delay: 0.26s;
}

.loadingSix span:nth-child(3) {
	left: 50%;
	top: 0;
	margin-left: -8px;
	-webkit-animation-delay: 0.39s;
}

.loadingSix span:nth-child(4) {
	top: 14px;
	right: 14px;
	-webkit-animation-delay: 0.52s;
}

.loadingSix span:nth-child(5) {
	right: 0;
	top: 50%;
	margin-top: -8px;
	-webkit-animation-delay: 0.65s;
}

.loadingSix span:nth-child(6) {
	right: 14px;
	bottom: 14px;
	-webkit-animation-delay: 0.78s;
}

.loadingSix span:nth-child(7) {
	bottom: 0;
	left: 50%;
	margin-left: -8px;
	-webkit-animation-delay: 0.91s;
}

.loadingSix span:nth-child(8) {
	bottom: 14px;
	left: 14px;
	-webkit-animation-delay: 1.04s;
}

.loading-text-box {
	color: #eee;
	font-size: 14px;
	margin-top: 15px;
}