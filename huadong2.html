<!DOCTYPE html>
<html lang="en">
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<title>Free Multi-Directional Scroll</title>
		<style>
			html,
			body {
				height: 100%;
				margin: 0;
				padding: 0;
			}

			.scroll-container {
				width: 100vw;
				/* 设置容器宽度为视口宽度 */
				height: 100vh;
				/* 设置容器高度为视口高度 */
				overflow: auto;
				/* 启用自动滚动 */
				-webkit-overflow-scrolling: touch;
				/* 允许在移动设备上使用惯性滚动 */
				background-color: lightgray;
			}

			.content {
				width: 2000px;
				/* 超出容器大小以触发滚动条 */
				height: 2000px;
				/* 超出容器大小以触发滚动条 */
				background-color: lightblue;
				display: flex;
				justify-content: center;
				align-items: center;
			}

			h1 {
				color: white;
			}
		</style>
	</head>
	<body>
		<div class="scroll-container">
			<div class="content">
				<h1>Scroll in All Directions</h1>
			</div>
		</div>
	</body>
</html>