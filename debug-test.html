<!DOCTYPE html>
<html>
<head>
    <title>调试测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        #seatBox { 
            border: 2px solid #333; 
            width: 800px; 
            height: 500px; 
            margin: 20px 0;
        }
        .log { 
            background: #f5f5f5; 
            padding: 10px; 
            margin: 10px 0; 
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>调试测试</h1>
    <div id="seatBox"></div>
    <div class="log" id="log"></div>

    <script>
        function log(message) {
            const logDiv = document.getElementById('log')
            const div = document.createElement('div')
            div.textContent = new Date().toLocaleTimeString() + ': ' + message
            logDiv.appendChild(div)
            logDiv.scrollTop = logDiv.scrollHeight
            console.log(message)
        }
        
        log('开始测试...')
        
        // 测试模块导入
        import('./SeatMapEngine.js')
            .then(module => {
                log('✅ SeatMapEngine.js 导入成功')
                log('可用导出: ' + Object.keys(module).join(', '))
                
                const { SeatMapEngine, SeatStatus } = module
                
                // 创建Canvas
                const container = document.getElementById('seatBox')
                const canvas = document.createElement('canvas')
                canvas.width = 800
                canvas.height = 500
                canvas.style.background = 'white'
                canvas.style.display = 'block'
                container.appendChild(canvas)
                log('✅ Canvas 创建成功')
                
                // 创建引擎
                const engine = new SeatMapEngine(canvas, {
                    onSeatClick: (seat) => {
                        log(`点击座位: ${seat.row + 1}-${seat.col + 1}`)
                    }
                })
                log('✅ SeatMapEngine 创建成功')
                
                // 生成座位
                engine.generateSeats(5, 8)
                log('✅ 座位生成成功')
                
                // 测试选择
                setTimeout(() => {
                    engine.selectRow(0)
                    log('✅ 行选择测试完成')
                }, 1000)
                
            })
            .catch(error => {
                log('❌ 导入失败: ' + error.message)
                console.error(error)
            })
    </script>
</body>
</html>
