<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快速设置测试数据</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 快速设置测试数据</h1>
        
        <div class="form-group">
            <label for="hallName">场地名称:</label>
            <input type="text" id="hallName" value="测试影厅" placeholder="输入场地名称">
        </div>
        
        <div class="form-group">
            <label for="rows">行数:</label>
            <input type="number" id="rows" value="10" min="1" max="50" placeholder="输入行数">
        </div>
        
        <div class="form-group">
            <label for="cols">列数:</label>
            <input type="number" id="cols" value="15" min="1" max="50" placeholder="输入列数">
        </div>
        
        <div class="form-group">
            <button type="button" onclick="setupData()">设置数据并跳转到Step2</button>
            <button type="button" onclick="clearData()">清除所有数据</button>
            <button type="button" onclick="checkData()">检查当前数据</button>
        </div>
        
        <div id="status"></div>
        
        <div class="form-group">
            <h3>快速跳转:</h3>
            <button type="button" onclick="window.open('step1.html', '_blank')">Step1: 设置场地排列</button>
            <button type="button" onclick="window.open('step2.html', '_blank')">Step2: 设置座位布局</button>
            <button type="button" onclick="window.open('step3.html', '_blank')">Step3: 设置分区</button>
        </div>
    </div>

    <script>
        function setupData() {
            const hallName = document.getElementById('hallName').value.trim();
            const rows = parseInt(document.getElementById('rows').value);
            const cols = parseInt(document.getElementById('cols').value);
            
            if (!hallName || !rows || !cols) {
                showStatus('请填写完整的场地信息', 'error');
                return;
            }
            
            if (rows < 1 || rows > 50 || cols < 1 || cols > 50) {
                showStatus('行数和列数必须在1-50之间', 'error');
                return;
            }
            
            // 设置Step1数据
            localStorage.setItem('hallName', hallName);
            localStorage.setItem('seatRows', rows.toString());
            localStorage.setItem('seatCols', cols.toString());
            
            showStatus(`✅ 数据设置成功！场地: ${hallName}, 规格: ${rows}行×${cols}列`, 'success');
            
            // 延迟跳转，让用户看到成功消息
            setTimeout(() => {
                window.open('step2.html', '_blank');
            }, 1000);
        }
        
        function clearData() {
            if (confirm('确定要清除所有数据吗？')) {
                localStorage.removeItem('hallName');
                localStorage.removeItem('seatRows');
                localStorage.removeItem('seatCols');
                localStorage.removeItem('seats');
                localStorage.removeItem('zones');
                showStatus('✅ 所有数据已清除', 'success');
            }
        }
        
        function checkData() {
            const hallName = localStorage.getItem('hallName');
            const seatRows = localStorage.getItem('seatRows');
            const seatCols = localStorage.getItem('seatCols');
            const seats = localStorage.getItem('seats');
            const zones = localStorage.getItem('zones');
            
            let html = '<h4>当前数据状态:</h4>';
            html += `<p><strong>场地名称:</strong> ${hallName || '未设置'}</p>`;
            html += `<p><strong>场地规格:</strong> ${seatRows || '?'}行 × ${seatCols || '?'}列</p>`;
            
            if (seats) {
                const seatsData = JSON.parse(seats);
                html += `<p><strong>座位数据:</strong> ${Object.keys(seatsData).length}个座位</p>`;
            } else {
                html += '<p><strong>座位数据:</strong> 未设置</p>';
            }
            
            if (zones) {
                const zonesData = JSON.parse(zones);
                html += `<p><strong>分区数据:</strong> ${zonesData.length}个分区</p>`;
            } else {
                html += '<p><strong>分区数据:</strong> 未设置</p>';
            }
            
            showStatus(html, 'info');
        }
        
        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        // 页面加载时检查数据
        window.onload = function() {
            checkData();
        };
    </script>
</body>
</html>
