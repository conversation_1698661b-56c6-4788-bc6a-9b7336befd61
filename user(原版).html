<!DOCTYPE html>
<html lang="ch">
	<head>
		<title>设置座位</title>
		<!-- 设置 viewport -->
		<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
		<!-- IE -->
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<!-- 兼容国产浏览器的高速模式 -->
		<meta name="renderer" content="webkit">
		<meta name="Author" content="<EMAIL>" />
		<meta name="Keywords" content="" />
		<meta name="Description" content="" />
		<meta charset="UTF-8">
		<link rel="stylesheet" type="text/css" href="css/reset.css">
		<link rel="stylesheet" href="css/translateCommon.css">
		<link rel="stylesheet" href="layui/css/layui.css" />
		<link rel="stylesheet" href="css/stepPlug.css" />
		<link rel="stylesheet" href="plugin/spectrum/spectrum.css">
		<link rel="stylesheet" href="css/seat3.css" />

	</head>
	<body>
		<div id="section">
			<!-- header -->
			<div id="header">
				<div class="hui">
					<img src="images/hui.jpg" alt="">
				</div>
				<div class="logo">
					<h1 class="f_left">
						<a href="#">
							<img src="images/logo.png" alt=""></a>
					</h1>
					<div class="f_right logo_r">
						<ul class="logo_r_show">
							<!-- <li class="search">
								<p>
									<span class="f_left">受邀者</span>
									<input class="f_left" type="text"> <i></i>
								</p>
							</li> -->
							<li class="translate-box">
								<a class="translate-box-cn" href="javascript:translate.changeLanguage('english');javascript:void(0);">
									<img src="images/translateEn.png" alt="">
								</a>
								<a class="translate-box-en" style="display: none;"
									href="javascript:translate.changeLanguage('chinese_simplified');javascript:void(0);">
									<img src="images/translateCn.png" alt="">
								</a>
							</li>
							<li class="h_qiandao">
								<a href="#">微信签到</a>
							</li>
							<li class="prompt">
								<a href="#">
									<span>0</span>
								</a>
							</li>
							<li class="help">帮助与文档</li>
							<li class="tel">18621589099</li>
						</ul>
					</div>
				</div>
			</div>

			<!-- content -->
			<div id="content" class="clearfix">
				<!-- 左边导航 -->
				<div class="c_left">
					<ul class="c_left_nav f_left">
						<li class="current" title="活动">
							<img src="images/icon6.png" alt="">
						</li>
						<li title="应用">
							<img src="images/icon7.png" alt="">
						</li>
						<li title="联系人">
							<img src="images/icon8.png" alt="">
						</li>
						<li title="供应商">
							<img src="images/icon9.png" alt="">
						</li>
						<li title="账户">
							<img src="images/icon10.png" alt="">
						</li>
					</ul>

				</div>

				<!-- 主要模块 -->
				<div class="c_right">
					<div class="main">

						<div class="main-content">
							<div class="main-left">
								<h2>我的座位</h2>
								<div class="zoomBox">
									<i class="layui-icon layui-icon-reduce-circle" onclick="zoomFn('in')"></i>
									<i class="layui-icon layui-icon-add-circle" onclick="zoomFn('out')"></i>
								</div>
								<div
									style="width: 100%;text-align: center;font-size: 25px;color: #000;font-weight: bold;margin-top: 15px;letter-spacing: 5px;"
									id="mySeat">
									<!-- 4排20座 -->
								</div>
								<!-- 座位遮罩层 -->
								<!-- <div class="seatFixed"></div> -->
								<div class="seatBoxContent" id="seatBoxContent">
									<div class="seatBox" id="seatBox" style="width: auto;" zoom="1">

									</div>
								</div>
							</div>
							<div class="main-right" style="display: block;">
								<div class="container">
									<div class="layui-tab" lay-filter="tab-hash">
										<ul class="layui-tab-title">
											<li class="layui-this" lay-id="11">设置</li>
											<!-- <li lay-id="22">批量分配</li> -->
										</ul>
										<div class="layui-tab-content">

											<div class="layui-tab-item layui-show">
												<h4 class="text-danger text-left">*设置座位四周间距</h4>
												<br />
												<!-- <div class="layui-form-item">
													<label class="layui-form-label">左间距：</label>
													<div class="layui-input-block">
														<input type="number" placeholder="请输入" value="10" autocomplete="off" class="layui-input"
															onchange="setPadding(this,'left')">
													</div>
												</div>
												<div class="layui-form-item">
													<label class="layui-form-label">上间距：</label>
													<div class="layui-input-block">
														<input type="number" placeholder="请输入" value="10" autocomplete="off" class="layui-input"
															onchange="setPadding(this,'top')">
													</div>
												</div>
												<div class="layui-form-item">
													<label class="layui-form-label">右间距：</label>
													<div class="layui-input-block">
														<input type="number" placeholder="请输入" value="10" autocomplete="off" class="layui-input"
															onchange="setPadding(this,'right')">
													</div>
												</div>
												<div class="layui-form-item">
													<label class="layui-form-label">下间距：</label>
													<div class="layui-input-block">
														<input type="number" placeholder="请输入" value="10" autocomplete="off" class="layui-input"
															onchange="setPadding(this,'bottom')">
													</div>
												</div> -->
												<h4 class="text-left">设置间距</h4>
												<div class="rangeBox">
													<div class="rangeBoxTitle">上边距</div>
													<div id="rangeTop"></div>
												</div>
												<div class="rangeBox">
													<div class="rangeBoxTitle">右边距</div>
													<div id="rangeRight"></div>
												</div>
												<div class="rangeBox">
													<div class="rangeBoxTitle">下边距</div>
													<div id="rangeBottom"></div>
												</div>
												<div class="rangeBox">
													<div class="rangeBoxTitle">左边距</div>
													<div id="rangeLeft"></div>
												</div>

												<h4 class="text-left">背景尺寸</h4>
												<div class="bgSize">背景宽度：背景高度：</div>


											</div>
											<div class="layui-tab-item">
												<div class="content-input flex-column">
													<div class="content-input-title">请选择要分配的人员：</div>
													<div class="content-input-box layui-form">
														<select class="channelSelect" lay-filter="channelSelect">
															<option value="0">请选择</option>
															<option value="50">通道a(50人)</option>
															<option value="150">通道b(150人)</option>
															<option value="300">通道c(300人)</option>
														</select>
													</div>
												</div>
												<div class="content-input flex-column">
													<div class="content-input-title">请选择要分配的区域：</div>
													<div class="content-input-box layui-form">
														分组情况
														<div class="groupBox">
															<ul class="groupUlBox">
																<li>
																	<span class="colorSpan"></span>
																	<div class="groupText">A区：100</div>
																	<div class="groupText2">已分配：100</div>
																	<div class="groupAllocation">分配座位</div>
																</li>
															</ul>
														</div>
													</div>
												</div>
												<!-- 计算可用分配数量 -->
												<div class="canUsePerson">
													分配人数：<span id="canUsePersonNumber">0</span>
												</div>
												<div class="canUseBox">
													分配座位数：<span id="canUseBoxNumber">0</span>
												</div>

												<div class="bottom-btn">
													<button id="batchAllocation" class="layui-btn" type="button">一键分配</button>
												</div>

											</div>

											<!-- end -->
										</div>
									</div>


								</div>
							</div>
						</div>


					</div>
				</div>
			</div>
		</div>
		<!-- 灰色背景 -->
		<div id="fade" class="black_overlay"></div>

		<!-- 下载弹窗 -->
		<!-- <div id="light2" class="white_content2">
  <div class="title-one">
    <span>下载</span>
    <div class="f_right pop_xx" onclick="cancel('light2');"></div>
  </div>
  <div class="title-two">
      <img src="images/35.gif">
      <p> 正在下载中，请稍后……</p>
    </div>
    <div class="title-three">
      <button class="pop_btn2">取消</button>
    </div>
</div> -->



		<!-- <script src="js/jquery-1.7.2.min.js"></script> -->
		<script src="js/jquery-3.6.3.min.js"></script>

		<!-- 拖动组件 -->
		<script src="js/jquery-ui-1.9.2.custom.min.js"></script>

		<script src="translateJs/translate.js"></script>
		<script src="translateJs/translateCommon.js"></script>

		<script src="plugin/jdialog/JDialog.min.js"></script>
		<script src="js/seat.js"></script>
		<script src="layui/layui.js"></script>
		<script>
			var layer = "";
			var noGroupNumber = 0;
			var noGroupHasAssign = 0; // 未分组已分配数量
			var form = "";
			var channelNumber = 0; // 批量通道人数
			var canUseNumber = 0; // 可用分配数量
			var canUseGroup = []; // 可用分配数组
			var channelSelectArray = JSON.parse(localStorage.getItem("channelSelectArrayStorage")) || []; // 已经分配过的通道

			layui.use(function() {
				form = layui.form;
				var element = layui.element;
				layer = layui.layer;

				// 获取padding 值
				var div = document.getElementById("seatBox");
				var computedStyle = getComputedStyle(div, null);
				var paddingTop = Number(computedStyle.paddingTop.replace("px", ""))
				var paddingLeft = Number(computedStyle.paddingLeft.replace("px", ""))
				var paddingRight = Number(computedStyle.paddingRight.replace("px", ""))
				var paddingBottom = Number(computedStyle.paddingBottom.replace("px", ""))


				// 滑块
				var slider = layui.slider;
				// 渲染
				slider.render({
					elem: '#rangeTop',
					min: 0,
					max: 300,
					value: paddingTop, //初始值
					input: true, //输入框
					change: function(value) {
						// console.log(value)
						$("#seatBox").css({
							"paddingTop": value + "px"
						})
						getSize()
					}
				});
				// 右边
				slider.render({
					elem: '#rangeRight',
					min: 0,
					max: 300,
					value: paddingRight, //初始值
					input: true, //输入框
					change: function(value) {
						// console.log(value)
						$("#seatBox").css({
							"paddingRight": value + "px"
						})
						getSize()
					}
				});
				// 右边
				slider.render({
					elem: '#rangeBottom',
					min: 0,
					max: 300,
					value: paddingBottom, //初始值
					input: true, //输入框
					change: function(value) {
						// console.log(value)
						$("#seatBox").css({
							"paddingBottom": value + "px"
						})
						getSize()
					}
				});
				// 左边边
				slider.render({
					elem: '#rangeLeft',
					min: 0,
					max: 300,
					value: paddingLeft, //初始值
					input: true, //输入框
					change: function(value) {
						// console.log(value)
						$("#seatBox").css({
							"paddingLeft": value + "px"
						})
						getSize()
					}
				});
			});

			$(function() {
				getSize()
			})
			// 获取初始宽高
			function getSize() {
				var div = document.getElementById("seatBox");
				var computedStyle = getComputedStyle(div, null);

				let w = $("#seatBox").width() + Number(computedStyle.paddingLeft.replace("px", "")) + Number(computedStyle
					.paddingRight.replace("px", ""))
				let h = $("#seatBox").height() + Number(computedStyle.paddingTop.replace("px", "")) + Number(computedStyle
					.paddingBottom.replace("px", ""))
				$(".bgSize").html(`背景宽度：${w} 背景高度：${h}`)
			}

			// 放大缩小
			function zoomFn(obj) {
				let zoomVal = Number($("#seatBox").attr("zoom"))
				if (obj == "in") {
					if (zoomVal >= 0.2) {
						zoomVal = zoomVal - 0.1
						$("#seatBox").css({
							transform: "scale(" + zoomVal + ")"
						})
					}

				} else if (obj == "out") {

					if (zoomVal < 1) {
						zoomVal = zoomVal + 0.1
						$("#seatBox").css({
							transform: "scale(" + zoomVal + ")"
						})
					}

				}
				$("#seatBox").attr("zoom", zoomVal)
			}
		</script>
		<script>
			let seatRows = localStorage.getItem("seatRows");
			let seatCols = localStorage.getItem("seatCols");

			// var groupObject = JSON.parse(localStorage.getItem('groupArrayObject')) || []
			try {
				var datas = JSON.parse(localStorage.getItem("seats"));
				var selected = JSON.parse(localStorage.getItem("selected"));
			} catch (e) {}
			var seats = $.seats({
				box: "#seatBox",
				rows: seatRows,
				cols: seatCols,
				size: 20, //座位的尺寸，单位：像素	
				hall_id: 10, //厅，如：5号厅
				thre_id: 20, //剧场，如：大地电影院中天广场沃尔玛店
				movie_id: 30, //场次ID
				step: 5,
				maxTicketNumber: 1000, //最多购票张数
				datas: datas,
				selected: selected || {},
				onSelected: function(seat) {

					/*************/
					var id = "selected_" + seat.row + "_" + seat.col;
					// 获取分区名称
					console.log(seat.color)
					let getGroupName = seat.groupName
					let getGroupLength = $(`span[name='${getGroupName}']`).length
					// console.log(getGroupLength)

					// 判断是否已经分配的座位
					let hasDisabled = $(`#${seat.row}_${seat.col}`).find("span").hasClass("disabledCancel")
					console.log(hasDisabled)
					// var selectedHtml = ''
					// 获取真实的座位号（去除空格的)
					let realSeat = $(`#${seat.row}_${seat.col}`).attr("seat").split("-")
					console.log(realSeat)

					if (hasDisabled) {
						if (seat.color == undefined) {
							selectedHtml = `<li id="${id}">
																<i class="layui-icon layui-icon-clear selected-delete" onclick="hasDisabledSelectedDelete('${id}',${seat.row},${seat.col})"></i>
																<div class="groupDetailBox">
																	<span class="colorSpan" style="background:#B9DEA0"></span>
																	<div class="groupText">未分区：${realSeat[0]} 排 ${realSeat[1]} 座</div>
																	<div class="groupText2">已分配</div>
																</div>
																<div class="personDetail">
																	<div class="personDetailList">
																		<b>姓名：</b>
																		<span>孙悟空</span>
																	</div>
																	<div class="personDetailList">
																		<b>手机号码：</b>
																		<span>17611111111</span>
																	</div>
																	<div class="personCancelBtn">
																		<button type="button" class="layui-btn layui-btn-sm layui-bg-red" onclick="hasDisabledSelectedCancel('${id}',${seat.row},${seat.col})">取消座位</button>
																	</div>
																</div>
															</li>`
						} else {
							selectedHtml = `<li id="${id}">
																<i class="layui-icon layui-icon-clear selected-delete" onclick="hasDisabledSelectedDelete('${id}',${seat.row},${seat.col})"></i>
																<div class="groupDetailBox">
																	<span class="colorSpan" style="background:${seat.color}"></span>
																	<div class="groupText">${seat.groupName}区：${realSeat[0]} 排 ${realSeat[1]} 座</div>
																	<div class="groupText2">已分配</div>
																</div>
																<div class="personDetail">
																	<div class="personDetailList">
																		<b>姓名：</b>
																		<span>孙悟空</span>
																	</div>
																	<div class="personDetailList">
																		<b>手机号码：</b>
																		<span>17611111111</span>
																	</div>
																	<div class="personCancelBtn">
																		<button type="button" class="layui-btn layui-btn-sm layui-bg-red" onclick="hasDisabledSelectedCancel('${id}',${seat.row},${seat.col})">取消座位</button>
																	</div>
																</div>
															</li>`
						}

					} else {
						if (seat.color == undefined) {
							selectedHtml = `<li id="${id}">
																<i class="layui-icon layui-icon-clear selected-delete" onclick="noDisabledSelectedDelete('${id}',${seat.row},${seat.col})"></i>
																<div class="groupDetailBox">
																	<span class="colorSpan" style="background:#B9DEA0"></span>
																	<div class="groupText">未分区：${realSeat[0]} 排 ${realSeat[1]} 座</div>
																	<div class="groupText2">未分配</div>
																</div>
																<div class="codeDetail">
																	<div class="codeDetailList">
																		<div class="codeDetailTitle">请输入姓名:</div>
																		<div class="codeDetailInput">
																			<input type="text" class="layui-input" />
																			<button type="button" class="layui-btn layui-btn-sm" onclick="codeDetailSearch(this)">搜索</button>
																		</div>
																	</div>
																</div>
																<div class="personDetailSearch" style="display:none;">
																	<div class="personDetailSearchLeft">
																			<div class="personDetailList">
																				<b>姓名：</b>
																				<span>孙悟空</span>
																			</div>
																			<div class="personDetailList">
																				<b>手机号码：</b>
																				<span>17611111111</span>
																			</div>
																	</div>
																	<div class="personDetailSearchRight">
																		<button type="button" class="layui-btn layui-btn-sm" onclick="noDisabledSelected('${id}',${seat.row},${seat.col})">分配</button>
																	</div>
																</div>
															</li>`
						} else {
							selectedHtml = `<li id="${id}">
																<i class="layui-icon layui-icon-clear selected-delete" onclick="noDisabledSelectedDelete('${id}',${seat.row},${seat.col})"></i>
																<div class="groupDetailBox">
																	<span class="colorSpan" style="background:${seat.color}"></span>
																	<div class="groupText">${seat.groupName}区：${realSeat[0]} 排 ${realSeat[1]} 座</div>
																	<div class="groupText2">未分配</div>
																</div>
																<div class="codeDetail">
																	<div class="codeDetailList">
																		<div class="codeDetailTitle">请输入姓名:</div>
																		<div class="codeDetailInput">
																			<input type="text" class="layui-input" />
																			<button type="button" class="layui-btn layui-btn-sm" onclick="codeDetailSearch(this)">搜索</button>
																		</div>
																	</div>
																</div>
																<div class="personDetailSearch" style="display:none;">
																	<div class="personDetailSearchLeft">
																			<div class="personDetailList">
																				<b>姓名：</b>
																				<span>孙悟空</span>
																			</div>
																			<div class="personDetailList">
																				<b>手机号码：</b>
																				<span>17611111111</span>
																			</div>
																	</div>
																	<div class="personDetailSearchRight">
																		<button type="button" class="layui-btn layui-btn-sm" onclick="noDisabledSelected('${id}',${seat.row},${seat.col})">分配</button>
																	</div>
																</div>
															</li>`
						}
					}

					$("#selected").find("ul").append(selectedHtml);
					/*********************************/
					console.log(seat);

					return true;
				},
				onUnselected: function(seat) {
					var id = "selected_" + seat.row + "_" + seat.col;
					$("#" + id).remove();
				},
				onerror: function(msg) {
					// JDialog.msg({
					// 	type: 'error',
					// 	content: msg,
					// 	container: "#seatBox"
					// });
					layer.msg(msg, {
						icon: 2,
						time: 1000 // 设置 2 秒后自动关闭
					});
				}
			});

			// 获取分组
			function getGroupObject() {
				// 获取已经保存的组别
				var liHtml = ""
				var groupObject = JSON.parse(localStorage.getItem('groupArrayObject')) || []

				if (groupObject.length > 0) {
					for (let item of groupObject) {
						console.log(item)
						// 获取已经分配的数量
						let hasAssign = $(`span.disabled[name='${item.groupName}']`).length
						noGroupHasAssign += hasAssign
						// console.log(hasAssign)
						noGroupNumber += Number(item.groupNumber)
						liHtml += `<li groupName="${item.groupName}" allocationNumber="${hasAssign}" noAllocationNumber="${item.groupNumber-hasAssign}">
													<span class="colorSpan" style="background:${item.color}"></span>
													<div class="groupText">${item.groupName}区：${item.groupNumber}</div>
													<div class="groupText2">可分配：${item.groupNumber-hasAssign}</div>
													<div class="groupText2">已分配：${hasAssign}</div>
													<input type="checkbox" name="${item.groupName}" title="" lay-filter="allocationArea">
												</li>`
					}
					noGroupNumber = Object.keys(datas).length - noGroupNumber
					noGroupHasAssign = $(`span.disabled`).length - noGroupHasAssign
					liHtml += `<li groupName="未分区" allocationNumber="${noGroupHasAssign}" noAllocationNumber="${noGroupNumber-noGroupHasAssign}">
													<span class="colorSpan" style="background:#B9DEA0;"></span>
													<div class="groupText">
														未分区：<span>${noGroupNumber}</span>
													</div>
													<div class="groupText2">
															可分配：<span>${noGroupNumber-noGroupHasAssign}</span>	
													</div>
													<div class="groupText2">
														已分配：<span>${noGroupHasAssign}</span>
													</div>
													<input type="checkbox" name="未分区" title="" lay-filter="allocationArea">
												</li>`
					$(".groupUlBox").html(liHtml)
					form.render()
				}
			}


			// 所属区域高亮

			$(function() {
				// let hrefSrc = window.location.href.split("?")[1].split("-")
				let row = 4
				let col = 18
				let seat = `${row}-${col}`
				console.log(seat)
				let groupName = $(`td[seat='${seat}']`).find("span").attr("name")
				console.log(groupName)
				// 回显座位到页面
				$("#mySeat").html(`我的座位：${groupName}区 - ${row}排${col}座`)

				// $(`td[seat='${seat}']`).find("span").html(seat).css({
				// 	color: "#fff",
				// 	fontSize: "10px",
				// 	lineHeight: "20px"
				// })
				// console.log($("td").find(`span[name='${groupName}']`).)
				let groupBg = $("td").find(`span[name='${groupName}']`).attr("style").split("background")[1].replace(":", "")
				groupBg = groupBg.replace(";", "")
				// 遍历所有可选座位
				$("td").each(function() {
					let backNone = $(this).find("span").attr("style")
					if (!backNone.includes("background: none;")) {
						$(this).find("span").css({
							background: '#d7d7d7'
						})
					}
				})

				// $("td").find("span").css({
				// 	background: '#d7d7d7'
				// })
				$("td").find("span").removeClass("disabled")


				$("td").find(`span[name='${groupName}']`).css({
					background: groupBg,
					opacity: 0.5
				})
				$(`td[seat='${seat}']`).find("span").addClass("disabled")
				$(`td[seat='${seat}']`).find("span").css({
					opacity: 1
				})
			})

			// 设置padding 
			// function setPadding(obj, type) {
			// 	let thisVal = Number($(obj).val())
			// 	console.log(thisVal)
			// 	if (type == "top") {
			// 		$("#seatBox").css({
			// 			"paddingTop": thisVal + "px"
			// 		})
			// 	} else if (type == "left") {
			// 		$("#seatBox").css({
			// 			"paddingLeft": thisVal + "px"
			// 		})
			// 	} else if (type == "right") {
			// 		$("#seatBox").css({
			// 			"paddingRight": thisVal + "px"
			// 		})
			// 	} else if (type == "bottom") {
			// 		$("#seatBox").css({
			// 			"paddingBottom": thisVal + "px"
			// 		})
			// 	}

			// 	getSize()

			// }
		</script>



	</body>
</html>