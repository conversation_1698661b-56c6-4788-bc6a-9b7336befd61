<!DOCTYPE html>
<html>
<head>
    <title>简单测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        #seatBox { 
            border: 1px solid #ccc; 
            width: 800px; 
            height: 500px; 
            margin: 20px 0;
            position: relative;
        }
        .controls { margin: 10px 0; }
        .controls button { margin: 5px; padding: 8px 16px; }
        .status { 
            background: #f0f0f0; 
            padding: 10px; 
            margin: 10px 0; 
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>座位系统测试</h1>
    
    <div class="status" id="status">
        状态：正在初始化...
    </div>
    
    <div class="controls">
        <button onclick="testZoomIn()">放大</button>
        <button onclick="testZoomOut()">缩小</button>
        <button onclick="testSelectRow()">选择第1行</button>
        <button onclick="testSelectCol()">选择第1列</button>
        <button onclick="testGetData()">获取数据</button>
    </div>
    
    <div id="seatBox"></div>
    
    <div id="output"></div>

    <script type="module">
        // 直接导入引擎
        import { SeatMapEngine, SeatStatus } from './SeatMapEngine.js'
        
        let engine = null
        
        function updateStatus(message) {
            document.getElementById('status').textContent = '状态：' + message
        }
        
        function log(message) {
            const output = document.getElementById('output')
            const div = document.createElement('div')
            div.textContent = new Date().toLocaleTimeString() + ': ' + message
            output.appendChild(div)
        }
        
        // 全局测试函数
        window.testZoomIn = function() {
            if (engine) {
                engine.zoomIn()
                log('放大视图')
            }
        }
        
        window.testZoomOut = function() {
            if (engine) {
                engine.zoomOut()
                log('缩小视图')
            }
        }
        
        window.testSelectRow = function() {
            if (engine) {
                engine.selectRow(0)
                log('选择第1行')
                updateSeatCount()
            }
        }
        
        window.testSelectCol = function() {
            if (engine) {
                engine.selectColumn(0)
                log('选择第1列')
                updateSeatCount()
            }
        }
        
        window.testGetData = function() {
            if (engine) {
                const selected = engine.getSelectedSeats()
                log(`当前选中座位数：${selected.length}`)
            }
        }
        
        function updateSeatCount() {
            if (engine) {
                const selected = engine.getSelectedSeats()
                updateStatus(`已选择 ${selected.length} 个座位`)
            }
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            try {
                updateStatus('创建Canvas...')
                
                // 创建Canvas
                const container = document.getElementById('seatBox')
                const canvas = document.createElement('canvas')
                canvas.width = 800
                canvas.height = 500
                canvas.style.display = 'block'
                canvas.style.background = 'white'
                container.appendChild(canvas)
                
                updateStatus('初始化引擎...')
                
                // 创建引擎
                engine = new SeatMapEngine(canvas, {
                    seatSize: 24,
                    seatSpacing: 4,
                    onSeatClick: (seat) => {
                        log(`点击座位: ${seat.row + 1}-${seat.col + 1}`)
                        updateSeatCount()
                    },
                    onSeatHover: (seat) => {
                        // log(`悬停座位: ${seat.row + 1}-${seat.col + 1}`)
                    }
                })
                
                updateStatus('生成座位...')
                
                // 生成座位
                engine.generateSeats(8, 10)
                
                updateStatus('初始化完成！可以开始测试')
                log('座位系统初始化成功')
                
            } catch (error) {
                updateStatus('初始化失败: ' + error.message)
                log('错误: ' + error.message)
                console.error(error)
            }
        })
    </script>
</body>
</html>
