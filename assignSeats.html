<!DOCTYPE html>
<html lang="ch">
	<head>
		<title>设置座位</title>
		<!-- 设置 viewport -->
		<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
		<!-- IE -->
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<!-- 兼容国产浏览器的高速模式 -->
		<meta name="renderer" content="webkit">
		<meta name="Author" content="<EMAIL>" />
		<meta name="Keywords" content="" />
		<meta name="Description" content="" />
		<meta charset="UTF-8">
		<link rel="stylesheet" type="text/css" href="css/reset.css">
		<link rel="stylesheet" href="css/translateCommon.css">
		<link rel="stylesheet" href="layui/css/layui.css" />
		<link rel="stylesheet" href="css/stepPlug.css" />
		<link rel="stylesheet" href="plugin/spectrum/spectrum.css">
		<link rel="stylesheet" href="css/modalbox.css" />
		<link rel="stylesheet" href="css/seat3.css" />
		<link rel="stylesheet" href="css/assignSeats.css" />

	</head>
	<body>
		<div id="section">
			<!-- header -->
			<div id="header">
				<div class="hui">
					<img src="images/hui.jpg" alt="">
				</div>
				<div class="logo">
					<h1 class="f_left">
						<a href="#">
							<img src="images/logo.png" alt=""></a>
					</h1>
					<div class="f_right logo_r">
						<ul class="logo_r_show">
							<!-- <li class="search">
								<p>
									<span class="f_left">受邀者</span>
									<input class="f_left" type="text"> <i></i>
								</p>
							</li> -->
							<li class="translate-box">
								<a class="translate-box-cn" href="javascript:translate.changeLanguage('english');javascript:void(0);">
									<img src="images/translateEn.png" alt="">
								</a>
								<a class="translate-box-en" style="display: none;"
									href="javascript:translate.changeLanguage('chinese_simplified');javascript:void(0);">
									<img src="images/translateCn.png" alt="">
								</a>
							</li>
							<li class="h_qiandao">
								<a href="#">微信签到</a>
							</li>
							<li class="prompt">
								<a href="#">
									<span>0</span>
								</a>
							</li>
							<li class="help">帮助与文档</li>
							<li class="tel">18621589099</li>
						</ul>
					</div>
				</div>
			</div>

			<!-- content -->
			<div id="content" class="clearfix">
				<!-- 左边导航 -->
				<div class="c_left">
					<ul class="c_left_nav f_left">
						<li class="current" title="活动">
							<img src="images/icon6.png" alt="">
						</li>
						<li title="应用">
							<img src="images/icon7.png" alt="">
						</li>
						<li title="联系人">
							<img src="images/icon8.png" alt="">
						</li>
						<li title="供应商">
							<img src="images/icon9.png" alt="">
						</li>
						<li title="账户">
							<img src="images/icon10.png" alt="">
						</li>
					</ul>

				</div>

				<!-- 主要模块 -->
				<div class="c_right">
					<div class="main">

						<div class="new-content-top">
							<div class="content-top-btn">
								<button class="layui-btn" type="button" onclick="">返回设置分区</button>
								<button class="layui-btn" type="button" onclick="viewTask('light1')">批量导入座位</button>
								<button class="layui-btn" type="button">进入场地视图设计</button>
							</div>
						</div>

						<div class="main-content">
							<div class="main-left">
								<h2>座位分配</h2>
								<!-- 座位遮罩层 -->
								<!-- <div class="seatFixed"></div> -->

								<div class="zoomBox">
									<i class="layui-icon layui-icon-reduce-circle" onclick="zoomFn('in')"></i>
									<i class="layui-icon layui-icon-add-circle" onclick="zoomFn('out')"></i>
								</div>

								<div class="seatBoxContent" id="seatBoxContent">
									<div class="seatFixed"></div>
									<div class="seatBox" id="seatBox" style="width: auto;" zoom="1">

									</div>
								</div>
							</div>
							<div class="main-right">
								<div class="container">

									<div class="layui-tab" lay-filter="tab-hash">
										<ul class="layui-tab-title">
											<li lay-id="11">指定分配</li>
											<li class="layui-this" lay-id="22">批量分配</li>
											<li lay-id="33">区域规则</li>
										</ul>
										<div class="layui-tab-content">

											<div class="layui-tab-item">
												<h4 class="text-danger text-left">*点击对应色块，进行座位分配</h4>
												<!-- 当前点击的分组情况 -->
												<div class="groupDetail">
													<!-- <ul class="groupDetailUl">
														<li>
															<span class="colorSpan"></span>
															<div class="groupText">A区：100</div>
															<div class="groupText2">已分配：100</div>
															<div class="groupAllocation">分配座位</div>
														</li>
													</ul> -->
													<div id="selected">
														<ul class="selected-ul">
															<!-- <li>
																<i class="layui-icon layui-icon-clear selected-delete"></i>
																<div class="groupDetailBox">
																	<span class="colorSpan"></span>
																	<div class="groupText">A区：260号座位</div>
																	<div class="groupText2">已分配</div>
																</div>
																<div class="personDetail">
																	<div class="personDetailList">
																		<b>姓名：</b>
																		<span>孙悟空</span>
																	</div>
																	<div class="personDetailList">
																		<b>手机号码：</b>
																		<span>17611111111</span>
																	</div>
																	<div class="personCancelBtn">
																		<button type="button" class="layui-btn layui-btn-sm layui-bg-red">取消座位</button>
																	</div>
																</div>
															</li>
															<li>
																<i class="layui-icon layui-icon-clear selected-delete"></i>
																<div class="groupDetailBox">
																	<span class="colorSpan"></span>
																	<div class="groupText">A区：260号座位</div>
																	<div class="groupText2">未分配</div>
																</div>
																<div class="codeDetail">
																	<div class="codeDetailList">
																		<div class="codeDetailTitle">请输入姓名:</div>
																		<div class="codeDetailInput">
																			<input type="text" class="layui-input" />
																			<button type="button" class="layui-btn layui-btn-sm ">搜索</button>
																		</div>
																	</div>
																</div>
															</li> -->
														</ul>
													</div>
												</div>
												<!-- <div class="content-input flex-column">
													<div class="content-input-title">填入参会人手机号码/确认码（每行一人)：</div>
													<div class="content-input-box layui-form">
														<textarea name="" placeholder="" class="layui-textarea"></textarea>
													</div>
												</div> -->
												<div class="bottom-btn">
													<button id="specifyAllocation" class="layui-btn" type="button">一键分配</button>
												</div>
											</div>
											<div class="layui-tab-item layui-show">
												<div class="content-input flex-column">
													<div class="content-input-title">请选择要分配的人员：</div>
													<div class="content-input-box layui-form">
														<select class="channelSelect" lay-filter="channelSelect">
															<option value="0">请选择</option>
															<option value="50">通道a(50人)</option>
															<option value="150">通道b(150人)</option>
															<option value="300">通道c(300人)</option>
														</select>
													</div>
												</div>
												<div class="content-input flex-column">
													<div class="content-input-title">请选择要分配的区域：</div>
													<div class="content-input-box layui-form">
														<!-- 分组情况 -->
														<div class="groupBox">
															<ul class="groupUlBox">
																<!-- <li>
																	<span class="colorSpan"></span>
																	<div class="groupText">A区：100</div>
																	<div class="groupText2">已分配：100</div>
																	<div class="groupAllocation">分配座位</div>
																</li> -->
															</ul>
														</div>
													</div>
												</div>
												<!-- 计算可用分配数量 -->
												<div class="canUsePerson">
													分配人数：<span id="canUsePersonNumber">0</span>
												</div>
												<div class="canUseBox">
													分配座位数：<span id="canUseBoxNumber">0</span>
												</div>

												<div class="bottom-btn">
													<button id="batchAllocation" class="layui-btn" type="button">一键分配</button>
													<!-- <button id="" class="layui-btn layui-bg-blue" type="button"
														onclick="viewSeat()">预览我的座位</button> -->
												</div>
											</div>

											<!-- 区域规则 -->
											<div class="layui-tab-item">

												<div class="content-input flex-column">
													<!-- <div class="content-input-title">请选择要分配的区域：</div> -->
													<div class="content-input-box layui-form">
														<!-- 分组情况 -->
														<div class="groupBox">
															<ul class="groupRulesUlBox">
																<li>
																	<span class="colorSpan"></span>
																	<div class="groupText">A区</div>
																	<div class="ckBox">
																		<input type="radio" name="AAA" value="1" title="展示区域号">
																	</div>
																	<div class="ckBox">
																		<input type="radio" name="AAA" value="2" title="展示区域号+座位号">
																	</div>
																</li>
															</ul>
														</div>
													</div>
												</div>

											</div>

											<!-- end -->
										</div>
									</div>


								</div>
							</div>
						</div>


					</div>
				</div>
			</div>
		</div>
		<!-- 灰色背景 -->
		<!-- 灰色背景 -->
		<div id="f-fade" class="black_overlay" style="display: block;"></div>

		<!-- 导入 -->
		<div class="white-contentAll" id="light1" style="display:none;width: 500px;">
			<div class="white-header">
				<span class="white-headerTitle">
					导入
				</span>
				<span class="white-delete" onclick="cancel('light1')">
				</span>
			</div>
			<div class="white-body" style="overflow: initial;">
				<div class="edit-module-box">
					<div class="edit-module-flex">
						<span>1.座位分区：</span>
						<div class="layui-form select-box">
							<select name="" id="">
								<option value="请选择">请选择</option>
								<option value="座位分区1">座位分区1</option>
								<option value="座位分区2">座位分区2</option>
							</select>
						</div>
						<button class="layui-btn">下载空模版</button>
					</div>
					<div class="edit-module-flex">
						<span>2.导入文件：</span>
						<button class="layui-btn">选择文件</button>
					</div>
					<!-- 文件回显 -->
					<ul class="file-list">
						<li>
							<div class="file-list-text">这是一个文件名字</div>
							<i class="layui-icon layui-icon-clear" onclick="deleteFile(this)"></i>
						</li>
					</ul>
					<div class="module-tips">
						<div class="module-tips-text">*1.排、座、座位编号不可修改，否则无法导入。</div>
						<div class="module-tips-text">2.姓名和移动电话为必填项目。每个参会者的移动电话不能重复。移动电话需按已有参会者填写。</div>
					</div>
				</div>
			</div>
			<div class="white-footer">
				<!-- <button class="white-close" onclick="cancel('light1')">
					取消
				</button> -->
				<button class="white-sure white-sureAdd" onclick="cancel('light1')">
					完成
				</button>
			</div>
		</div>
		<!-- 加载弹窗 -->
		<div class="white-contentAll" id="light2" style="display:none;width: 500px;">
			<div class="white-header">
				<span class="white-headerTitle">
					导入
				</span>
				<!-- <span class="white-delete" onclick="cancel('light2')">
				</span> -->
			</div>
			<div class="white-body" style="overflow: initial;">
				<div class="loading-module-box">
					<div class="loading-module-content">
						<div class="loadingSix">
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
							<span></span>
						</div>
						<div class="loading-module-text">导入中请耐心等待</div>
					</div>
				</div>
			</div>
			<div class="white-footer">
				<!-- <button class="white-close" onclick="cancel('light1')">
					取消
				</button> -->
				<!-- <button class="white-sure white-sureAdd" onclick="cancel('light1')">
					完成
				</button> -->
			</div>
		</div>
		<!-- 导入成功 -->
		<div class="white-contentAll" id="light3" style="display:none;width: 500px;">
			<div class="white-header">
				<span class="white-headerTitle">
					导入
				</span>
				<!-- <span class="white-delete" onclick="cancel('light3')">
				</span> -->
			</div>
			<div class="white-body" style="overflow: initial;">
				<div class="loading-module-box">
					<div class="loading-module-content">
						<img src="images/u1022.png" class="loading-img" alt="">
						<div class="loading-module-text">导入成功！成功分配 <span>100</span> 个座位</div>
					</div>
				</div>
			</div>
			<div class="white-footer">
				<!-- <button class="white-close" onclick="cancel('light1')">
					取消
				</button> -->
				<!-- <button class="white-sure white-sureAdd" onclick="cancel('light1')">
					完成
				</button> -->
			</div>
		</div>

		<!-- 提示 -->
		<div class="white-contentAll" id="light20241216" style="display:block;width: 400px;">
			<div class="white-header">
				<span class="white-headerTitle">
					提示
				</span>
				<span class="white-delete" onclick="cancel('light20241216')">
				</span>
			</div>
			<div class="white-body" style="overflow: initial;">
				<div style="width: 100%;text-align: center;margin: 30px 0;">保存成功</div>
			</div>
			<div class="white-footer">
				<!-- <button class="white-close">
					取消
				</button> -->
				<button class="white-sure white-sureAdd" onclick="cancel('light20241216')">
					确定
				</button>
			</div>
		</div>



		<!-- <script src="js/jquery-1.7.2.min.js"></script> -->
		<script src="js/jquery-3.6.3.min.js"></script>
		<script src="js/index.js"></script>

		<script src="translateJs/translate.js"></script>
		<script src="translateJs/translateCommon.js"></script>

		<script src="plugin/jdialog/JDialog.min.js"></script>
		<script src="js/seat.js"></script>
		<script src="layui/layui.js"></script>
		<script>
			// 遮罩层
			function viewTask(id) {
				$("div[id^=light]").hide();
				$("#" + id).show();
				$("#f-fade").show();
			}

			function cancel(id) {
				$("#" + id).hide();
				$("#f-fade").hide();
			}
		</script>
		<script>
			var layer = "";
			var noGroupNumber = 0;
			var noGroupHasAssign = 0; // 未分组已分配数量
			var form = "";
			var channelNumber = 0; // 批量通道人数
			var canUseNumber = 0; // 可用分配数量
			var canUseGroup = []; // 可用分配数组
			var channelSelectArray = JSON.parse(localStorage.getItem("channelSelectArrayStorage")) || []; // 已经分配过的通道

			layui.use(function() {
				form = layui.form;
				var element = layui.element;
				layer = layui.layer;
				element.on('tab(tab-hash)', function(obj) {
					let thisIndex = obj.index
					if (thisIndex == 0) {
						noGroupNumber = 0
						$(".seatFixed").hide()
					} else if (thisIndex == 1) {
						// 统计座位总数
						// $("#seatDatas").find("span").text(Object.keys(datas).length)
						// 更新分组数据
						getGroupObject()
						$(".seatFixed").show()
						// $(".canUsePersonNumber")


					} else if (thisIndex == 2) {
						getGroupRules()
						$(".seatFixed").show()
					}
				});
				// 关联通道选择人数
				form.on('select(channelSelect)', function(data) {
					// console.log(data.value)
					channelNumber = Number(data.value)
					$("#canUsePersonNumber").text(data.value)
				})
				// 分配区域选择
				form.on('checkbox(allocationArea)', function(data) {
					var elem = data.elem; // 获得 checkbox 原始 DOM 对象
					var checked = elem.checked; // 获得 checkbox 选中状态
					var value = elem.value; // 获得 checkbox 值
					var othis = data.othis; // 获得 checkbox 元素被替换后的 jQuery 对象
					// 获取内容
					let groupName = othis.parent().attr("groupName"); // 分组名称
					let allocationNumber = othis.parent().attr("allocationNumber"); // 已分配数
					let noAllocationNumber = othis.parent().attr("noAllocationNumber"); //未分配数
					if (elem.checked) {
						canUseNumber += Number(noAllocationNumber)
						canUseGroup.push(groupName)
					} else {
						canUseNumber -= Number(noAllocationNumber)
						canUseGroup = canUseGroup.filter(item => item !== groupName);
					}
					$("#canUseBoxNumber").text(canUseNumber)

					console.log(canUseNumber)
					console.log(canUseGroup)

					// layer.msg('checked 状态: ' + elem.checked);
				});

				// 分配区域规则选择
				form.on('checkbox(allocationType)', function(data) {
					var elem = data.elem; // 获得 checkbox 原始 DOM 对象
					var checked = elem.checked; // 获得 checkbox 选中状态
					var value = elem.value; // 获得 checkbox 值
					var othis = data.othis; // 获得 checkbox 元素被替换后的 jQuery 对象
					console.log(value)
					// layer.msg('checked 状态: ' + elem.checked);
				});
			});

			// 复制可选通道内容
			$(function() {
				// canUseGroup = [50, 150]
				console.log(channelSelectArray)
				for (let i = 0; i < channelSelectArray.length; i++) {
					if (Number(channelSelectArray[i]) == 50) {
						// console.log(1111111)
						$(".channelSelect").find("option").eq(1).attr("disabled", "disabled")
					}
					if (Number(channelSelectArray[i]) == 150) {
						$(".channelSelect").find("option").eq(2).attr("disabled", "disabled")
					}
					if (Number(channelSelectArray[i]) == 300) {
						$(".channelSelect").find("option").eq(3).attr("disabled", "disabled")
					}
				}

			})
		</script>
		<script>
			let seatRows = localStorage.getItem("seatRows");
			let seatCols = localStorage.getItem("seatCols");

			// var groupObject = JSON.parse(localStorage.getItem('groupArrayObject')) || []
			try {
				var datas = JSON.parse(localStorage.getItem("seats"));
				var selected = JSON.parse(localStorage.getItem("selected"));
			} catch (e) {}
			var seats = $.seats({
				box: "#seatBox",
				rows: seatRows,
				cols: seatCols,
				size: 20, //座位的尺寸，单位：像素	
				hall_id: 10, //厅，如：5号厅
				thre_id: 20, //剧场，如：大地电影院中天广场沃尔玛店
				movie_id: 30, //场次ID
				step: 4,
				maxTicketNumber: 1000, //最多购票张数
				datas: datas,
				selected: selected || {},
				onSelected: function(seat) {

					/*************/
					var id = "selected_" + seat.row + "_" + seat.col;
					// 获取分区名称
					console.log(seat.color)
					let getGroupName = seat.groupName
					let getGroupLength = $(`span[name='${getGroupName}']`).length
					// console.log(getGroupLength)

					// 判断是否已经分配的座位
					let hasDisabled = $(`#${seat.row}_${seat.col}`).find("span").hasClass("disabledCancel")
					console.log(hasDisabled)
					// var selectedHtml = ''
					// 获取真实的座位号（去除空格的)
					let realSeat = $(`#${seat.row}_${seat.col}`).attr("seat").split("-")
					console.log(realSeat)

					if (hasDisabled) {
						if (seat.color == undefined) {
							selectedHtml = `<li id="${id}">
																<i class="layui-icon layui-icon-clear selected-delete" onclick="hasDisabledSelectedDelete('${id}',${seat.row},${seat.col})"></i>
																<div class="groupDetailBox">
																	<span class="colorSpan" style="background:#B9DEA0"></span>
																	<div class="groupText">未分区：${realSeat[0]} 排 ${realSeat[1]} 座</div>
																	<div class="groupText2">已分配</div>
																</div>
																<div class="personDetail">
																	<div class="personDetailList">
																		<b>姓名：</b>
																		<span>孙悟空</span>
																	</div>
																	<div class="personDetailList">
																		<b>手机号码：</b>
																		<span>17611111111</span>
																	</div>
																	<div class="personCancelBtn">
																		<button type="button" class="layui-btn layui-btn-sm layui-bg-red" onclick="hasDisabledSelectedCancel('${id}',${seat.row},${seat.col})">取消座位</button>
																	</div>
																</div>
															</li>`
						} else {
							selectedHtml = `<li id="${id}">
																<i class="layui-icon layui-icon-clear selected-delete" onclick="hasDisabledSelectedDelete('${id}',${seat.row},${seat.col})"></i>
																<div class="groupDetailBox">
																	<span class="colorSpan" style="background:${seat.color}"></span>
																	<div class="groupText">${seat.groupName}区：${realSeat[0]} 排 ${realSeat[1]} 座</div>
																	<div class="groupText2">已分配</div>
																</div>
																<div class="personDetail">
																	<div class="personDetailList">
																		<b>姓名：</b>
																		<span>孙悟空</span>
																	</div>
																	<div class="personDetailList">
																		<b>手机号码：</b>
																		<span>17611111111</span>
																	</div>
																	<div class="personCancelBtn">
																		<button type="button" class="layui-btn layui-btn-sm layui-bg-red" onclick="hasDisabledSelectedCancel('${id}',${seat.row},${seat.col})">取消座位</button>
																	</div>
																</div>
															</li>`
						}

					} else {
						if (seat.color == undefined) {
							selectedHtml = `<li id="${id}">
																<i class="layui-icon layui-icon-clear selected-delete" onclick="noDisabledSelectedDelete('${id}',${seat.row},${seat.col})"></i>
																<div class="groupDetailBox">
																	<span class="colorSpan" style="background:#B9DEA0"></span>
																	<div class="groupText">未分区：${realSeat[0]} 排 ${realSeat[1]} 座</div>
																	<div class="groupText2">未分配</div>
																</div>
																<div class="codeDetail">
																	<div class="codeDetailList">
																		<div class="codeDetailTitle">请输入姓名:</div>
																		<div class="codeDetailInput">
																			<input type="text" class="layui-input" />
																			<button type="button" class="layui-btn layui-btn-sm" onclick="codeDetailSearch(this)">搜索</button>
																		</div>
																	</div>
																</div>
																<div class="personDetailSearch" style="display:none;">
																	<div class="personDetailSearchLeft">
																			<div class="personDetailList">
																				<b>姓名：</b>
																				<span>孙悟空</span>
																			</div>
																			<div class="personDetailList">
																				<b>手机号码：</b>
																				<span>17611111111</span>
																			</div>
																	</div>
																	<div class="personDetailSearchRight">
																		<button type="button" class="layui-btn layui-btn-sm" onclick="noDisabledSelected('${id}',${seat.row},${seat.col})">分配</button>
																	</div>
																</div>
															</li>`
						} else {
							selectedHtml = `<li id="${id}">
																<i class="layui-icon layui-icon-clear selected-delete" onclick="noDisabledSelectedDelete('${id}',${seat.row},${seat.col})"></i>
																<div class="groupDetailBox">
																	<span class="colorSpan" style="background:${seat.color}"></span>
																	<div class="groupText">${seat.groupName}区：${realSeat[0]} 排 ${realSeat[1]} 座</div>
																	<div class="groupText2">未分配</div>
																</div>
																<div class="codeDetail">
																	<div class="codeDetailList">
																		<div class="codeDetailTitle">请输入姓名:</div>
																		<div class="codeDetailInput">
																			<input type="text" class="layui-input" />
																			<button type="button" class="layui-btn layui-btn-sm" onclick="codeDetailSearch(this)">搜索</button>
																		</div>
																	</div>
																</div>
																<div class="personDetailSearch" style="display:none;">
																	<div class="personDetailSearchLeft">
																			<div class="personDetailList">
																				<b>姓名：</b>
																				<span>孙悟空</span>
																			</div>
																			<div class="personDetailList">
																				<b>手机号码：</b>
																				<span>17611111111</span>
																			</div>
																	</div>
																	<div class="personDetailSearchRight">
																		<button type="button" class="layui-btn layui-btn-sm" onclick="noDisabledSelected('${id}',${seat.row},${seat.col})">分配</button>
																	</div>
																</div>
															</li>`
						}
					}

					$("#selected").find("ul").append(selectedHtml);
					/*********************************/
					console.log(seat);

					return true;
				},
				onUnselected: function(seat) {
					var id = "selected_" + seat.row + "_" + seat.col;
					$("#" + id).remove();
				},
				onerror: function(msg) {
					// JDialog.msg({
					// 	type: 'error',
					// 	content: msg,
					// 	container: "#seatBox"
					// });
					layer.msg(msg, {
						icon: 2,
						time: 1000 // 设置 2 秒后自动关闭
					});
				}
			});
			/************已经预定座位*****************/
			// 已经预定删除按钮
			function hasDisabledSelectedDelete(liId, row, col) {
				console.log(row, col)
				// 已经约定，单击右上角取消按钮
				$(`#${row}_${col}`).find("span").removeClass("disabledCancel").addClass("disabled")
				$(`#${liId}`).remove()
			}
			// 已预定，取消座位按钮
			function hasDisabledSelectedCancel(liId, row, col) {
				console.log(row, col)
				// 已经约定，单击取消预定按钮
				$(`#${row}_${col}`).find("span").removeClass("disabledCancel");
				$(`#${liId}`).remove()
				seats.unselect(row, col);
				console.log(seats.getSelectedSeats());
				localStorage.setItem("selected", JSON.stringify(seats.getSelectedSeats()));
				layer.msg("取消成功", {
					icon: 2,
					time: 1000 // 设置 2 秒后自动关闭
				});
			}
			/************已经预定座位end*****************/

			/************未预定座位*****************/
			// 未占位 取消选中
			function noDisabledSelectedDelete(liId, row, col) {
				console.log(row, col)
				$(`#${row}_${col}`).find("span").removeClass("selected")
				$(`#${liId}`).remove()
			}
			// 未占位，选中单独预定
			function noDisabledSelected(liId, row, col) {
				console.log(row, col)
				$(`#${liId}`).remove()
				console.log(seats.getSelectedSeats());
				localStorage.setItem("selected", JSON.stringify(seats.getSelectedSeats()));
				// 保存数据后，页面回显占位
				$(`#${row}_${col}`).find("span").removeClass("selected").addClass("disabled")
				layer.msg("分配成功", {
					icon: 1,
					time: 1000 // 设置 2 秒后自动关闭
				});
			}
			// 分配搜索
			function codeDetailSearch(obj) {
				$(obj).parent().parent().parent().parent().find(".personDetailSearch").show()
			}

			// 指定分配
			$("#specifyAllocation").on("click", function() {
				console.log(seats.getSelectedSeats());
				localStorage.setItem("selected", JSON.stringify(seats.getSelectedSeats()));
				// JDialog.msg({
				// 	type: 'ok',
				// 	content: "购票成功.",
				// 	container: "#seatBox"
				// })
				layer.msg("分配成功", {
					icon: 1,
					time: 1000 // 设置 2 秒后自动关闭
				});

				setTimeout(function() {
					location.reload();
				}, 1000);
			});

			/************未预定座位end*****************/

			// 批量分配
			$("#batchAllocation").on("click", function() {

				if (channelNumber == 0) {
					layer.msg("请选择关联通道", {
						icon: 2,
						time: 2000 // 设置 2 秒后自动关闭
					});
					// JDialog.msg({
					// 	type: 'error',
					// 	content: "请选择关联通道",
					// 	// container: "#seatBox"
					// })
				} else {
					if (channelNumber > canUseNumber) { // 可分配人数少于关联人数
						layer.msg("可分配人数少于关联人数", {
							icon: 2,
							time: 2000 // 设置 2 秒后自动关闭
						});
					} else {
						// 保存可分配数组
						let canAssignArray = []
						for (let i = 0; i < canUseGroup.length; i++) {
							console.log(canUseGroup[i])
							if (canUseGroup[i] == "未分区") {
								$(`span[name='noGroup']:not(.disabled)`).each(function() {
									// console.log($(this).parent().attr("id"))
									let seatId = $(this).parent().attr("id")
									canAssignArray.push(seatId)
								})
							} else {
								$(`span[name='${canUseGroup[i]}']:not(.disabled)`).each(function() {
									// console.log($(this).parent().attr("id"))
									let seatId = $(this).parent().attr("id")
									canAssignArray.push(seatId)
								})
							}
							// console.log(canAssignArray)
						}
						// 截取关联需要分配人数的数组
						channelNumber = canAssignArray.splice(0, channelNumber)
						console.log(channelNumber)
						// 让截取分配的座位选中
						channelNumber.forEach((item) => {
							// console.log(item)
							$(`#${item}`).find("span").click()
						})

						// 保存选座数据
						// console.log(seats.getSelectedSeats());
						localStorage.setItem("selected", JSON.stringify(seats.getSelectedSeats()));
						layer.msg("分配成功", {
							icon: 1,
							time: 1000 // 设置 2 秒后自动关闭
						});
						channelSelectArray.push(channelNumber.length)
						// let nowChannelSelectArray = channelSelectArray
						console.log(channelSelectArray)
						// 保存已分配通道
						localStorage.setItem("channelSelectArrayStorage", JSON.stringify(channelSelectArray));

						setTimeout(function() {
							location.reload();
						}, 500);

					}
				}


			});
			// setTimeout(function() {
			// 	getGroupObject()
			// }, 100)

			getGroupObject()

			// 获取分组
			function getGroupObject() {
				// 获取已经保存的组别
				var liHtml = ""
				var groupObject = JSON.parse(localStorage.getItem('groupArrayObject')) || []

				if (groupObject.length > 0) {
					for (let item of groupObject) {
						console.log(item)
						// 获取已经分配的数量
						let hasAssign = $(`span.disabled[name='${item.groupName}']`).length
						noGroupHasAssign += hasAssign
						// console.log(hasAssign)
						noGroupNumber += Number(item.groupNumber)
						liHtml += `<li groupName="${item.groupName}" allocationNumber="${hasAssign}" noAllocationNumber="${item.groupNumber-hasAssign}">
													<span class="colorSpan" style="background:${item.color}"></span>
													<div class="groupText">${item.groupName}区：${item.groupNumber}</div>
													<div class="groupText2">可分配：${item.groupNumber-hasAssign}</div>
													<div class="groupText2">已分配：${hasAssign}</div>
													<input type="checkbox" name="${item.groupName}" title="" lay-filter="allocationArea">
												</li>`
					}
					noGroupNumber = Object.keys(datas).length - noGroupNumber
					noGroupHasAssign = $(`span.disabled`).length - noGroupHasAssign
					liHtml += `<li groupName="未分区" allocationNumber="${noGroupHasAssign}" noAllocationNumber="${noGroupNumber-noGroupHasAssign}">
													<span class="colorSpan" style="background:#B9DEA0;"></span>
													<div class="groupText">
														未分区：<span>${noGroupNumber}</span>
													</div>
													<div class="groupText2">
															可分配：<span>${noGroupNumber-noGroupHasAssign}</span>	
													</div>
													<div class="groupText2">
														已分配：<span>${noGroupHasAssign}</span>
													</div>
													<input type="checkbox" name="未分区" title="" lay-filter="allocationArea">
												</li>`
					$(".groupUlBox").html(liHtml)
					// form.render()
				}
			}

			// 区域规则 
			function getGroupRules() {
				// 获取已经保存的组别
				var liHtml = ""
				var groupObject = JSON.parse(localStorage.getItem('groupArrayObject')) || []

				if (groupObject.length > 0) {
					for (let item of groupObject) {
						console.log(item)
						// 获取已经分配的数量
						let hasAssign = $(`span.disabled[name='${item.groupName}']`).length
						noGroupHasAssign += hasAssign
						// console.log(hasAssign)
						noGroupNumber += Number(item.groupNumber)
						liHtml += `<li groupName="${item.groupName}" allocationNumber="${hasAssign}" noAllocationNumber="${item.groupNumber-hasAssign}">
													<span class="colorSpan" style="background:${item.color}"></span>
													<div class="groupText">${item.groupName}区</div>
													<div class="ckBox">
														<input type="radio" name="${item.groupName}" value="1" title="展示区域号" checked  lay-filter="allocationArea">
													</div>
													<div class="ckBox">
														<input type="radio" name="${item.groupName}"  value="2" title="展示区域号+座位号"  lay-filter="allocationArea">
													</div>
												</li>`
					}
					noGroupNumber = Object.keys(datas).length - noGroupNumber
					noGroupHasAssign = $(`span.disabled`).length - noGroupHasAssign
					liHtml += `<li groupName="未分区" allocationNumber="${noGroupHasAssign}" noAllocationNumber="${noGroupNumber-noGroupHasAssign}">
													<span class="colorSpan" style="background:#B9DEA0;"></span>
													<div class="groupText">
														未分区
													</div>
													<div class="ckBox">
														<input type="radio" name="未分区" value="1" title="展示区域号" checked  lay-filter="allocationArea">
													</div>
													<div class="ckBox">
														<input type="radio" name="未分区"  value="2" title="展示区域号+座位号"  lay-filter="allocationArea">
													</div>
												</li>`
					$(".groupRulesUlBox").html(liHtml)

					form.render()
				}
			}

			function viewSeat() {
				window.location.href = "user.html"
			}

			// 放大缩小
			function zoomFn(obj) {
				let zoomVal = Number($("#seatBox").attr("zoom"))
				if (obj == "in") {
					if (zoomVal >= 0.2) {
						zoomVal = zoomVal - 0.1
						$("#seatBox").css({
							transform: "scale(" + zoomVal + ")"
						})
					}

				} else if (obj == "out") {

					if (zoomVal < 1) {
						zoomVal = zoomVal + 0.1
						$("#seatBox").css({
							transform: "scale(" + zoomVal + ")"
						})
					}

				}
				$("#seatBox").attr("zoom", zoomVal)
			}


			// 滚动条滚到到正中间
			function scrollCenter() {
				var div = document.getElementById("seatBox");
				var computedStyle = getComputedStyle(div, null);
				var paddingTop = Number(computedStyle.paddingTop.replace("px", ""))
				var paddingLeft = Number(computedStyle.paddingLeft.replace("px", ""))
				var paddingRight = Number(computedStyle.paddingRight.replace("px", ""))
				var paddingBottom = Number(computedStyle.paddingBottom.replace("px", ""))
				// let tableWidth = $(".dragContent").width()
				// let tableHeight = $(".dragContent").height()

				let tableWidth = $("#seatBox").width() + Number(computedStyle.paddingLeft.replace("px", "")) + Number(
					computedStyle.paddingRight.replace("px", ""))
				let tableHeight = $("#seatBox").height() + Number(computedStyle.paddingTop.replace("px", "")) + Number(
					computedStyle.paddingBottom.replace("px", ""))

				let scrollWidth = $(".seatBoxContent").width()
				let scrollHeight = $(".seatBoxContent").height()

				let srollLeft = (tableWidth - scrollWidth) / 2
				let srollTop = (tableHeight - scrollHeight) / 2
				console.log("left:" + srollLeft)
				console.log("top:" + srollTop)
				if (srollLeft > 0) {
					$(".seatBoxContent").scrollLeft(srollLeft)
				}
				if (srollTop > 0) {
					$(".seatBoxContent").scrollTop(srollTop)
				}
				console.log(tableWidth, tableHeight)
				console.log($(".seatBox").attr("paading-left"))
			}


			$(function() {
				scrollCenter()
			})
		</script>

		<script>
			// 删除列表
			function deleteFile(obj) {
				$(obj).parent().remove()
			}
		</script>



	</body>
</html>