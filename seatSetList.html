<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<title>场地设置列表</title>
		<!-- 设置 viewport -->
		<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
		<!-- IE -->
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<!-- 兼容国产浏览器的高速模式 -->
		<meta name="renderer" content="webkit">
		<meta name="Author" content="<EMAIL>" />
		<meta name="Keywords" content="" />
		<meta name="Description" content="" />
		<meta charset="UTF-8">
		<link rel="stylesheet" type="text/css" href="css/reset.css">
		<link rel="stylesheet" href="css/translateCommon.css">
		<link rel="stylesheet" href="layui/css/layui.css" />
		<link rel="stylesheet" href="css/modalbox.css" />
		<link rel="stylesheet" href="css/seatSetList.css" />
	</head>
	<body>
		<div id="section">
			<!-- header -->
			<div id="header">
				<div class="hui">
					<img src="images/hui.jpg" alt="">
				</div>
				<div class="logo">
					<h1 class="f_left">
						<a href="#">
							<img src="images/logo.png" alt=""></a>
					</h1>
					<div class="f_right logo_r">
						<ul class="logo_r_show">
							<!-- <li class="search">
								<p>
									<span class="f_left">受邀者</span>
									<input class="f_left" type="text"> <i></i>
								</p>
							</li> -->
							<li class="translate-box">
								<a class="translate-box-cn" href="javascript:translate.changeLanguage('english');javascript:void(0);">
									<img src="images/translateEn.png" alt="">
								</a>
								<a class="translate-box-en" style="display: none;"
									href="javascript:translate.changeLanguage('chinese_simplified');javascript:void(0);">
									<img src="images/translateCn.png" alt="">
								</a>
							</li>
							<li class="h_qiandao">
								<a href="#">微信签到</a>
							</li>
							<li class="prompt">
								<a href="#">
									<span>0</span>
								</a>
							</li>
							<li class="help">帮助与文档</li>
							<li class="tel">18621589099</li>
						</ul>
					</div>
				</div>
			</div>

			<!-- content -->
			<div id="content" class="clearfix">
				<!-- 左边导航 -->
				<div class="c_left">
					<ul class="c_left_nav f_left">
						<li class="current" title="活动">
							<img src="images/icon6.png" alt="">
						</li>
						<li title="应用">
							<img src="images/icon7.png" alt="">
						</li>
						<li title="联系人">
							<img src="images/icon8.png" alt="">
						</li>
						<li title="供应商">
							<img src="images/icon9.png" alt="">
						</li>
						<li title="账户">
							<img src="images/icon10.png" alt="">
						</li>
					</ul>
				</div>

				<!-- 主要模块 -->
				<div class="c_right">
					<div class="main">

						<div class="new-content">
							<div class="new-content-top">
								<div class="content-btn-box">
									<span class="content-title">场地设计</span>
									<div class="layui-btn-container new-content-btn layui-form">
										<div class="layui-input-wrap">
											<input type="text" lay-affix="search" lay-filter="search" lay-options="{split: true}"
												placeholder="请输入座位图名称" class="layui-input">
										</div>
										<button type="button" class="layui-btn" onclick="viewTask('light4')">+ 新增</button>
									</div>
								</div>

							</div>

							<div class="content-html">

								<div class="activity_con clearfix">

									<div class="activity_show">
										<div class="activity_list">
											<img src="https://test.huitengsoft.com/upload/microWeb/shareImg/defaultShareImg.png" alt="">
											<img src="https://test.huitengsoft.com/new_edition/jsp/microWeb/images/bg2.png" alt=""
												style="width: 58px;height: 58px;position: absolute;right: 0;top: 0;">
											<div class="ac_text">
												<p>这里是场地图设置</p>
												<ul class="clearfix">
													<li>
														2024-08-01
													</li>
												</ul>
											</div>
										</div>
										<!-- 鼠标移上隐藏 -->
										<div class="hide">
											<!-- 预览 -->
											<div class="hide-top2">
												<ul>
													<li>
														<div class="overviewSeat">效果图预览</div>
													</li>
												</ul>
											</div>
											<div class="hide-bottom">
												<div class="option">
													<div class="ag">
														<ul>
															<li>
																<i class="layui-icon layui-icon-edit" onclick="editMicroWeb(10007586, 6609);"></i>
															</li>
															<li>
																<span>编辑</span>
															</li>
														</ul>
													</div>
													<div class="ag">
														<ul class="copy-btn" data-clipboard-text="复制的内容">
															<li>
																<i class="layui-icon layui-icon-file"></i>
															</li>
															<li>
																<span>复制</span>
															</li>
														</ul>
													</div>
													<div class="ag">
														<ul onclick="setDelete(6609)">
															<li>
																<i class="layui-icon layui-icon-delete"></i>
															</li>
															<li>
																<span>删除</span>
															</li>
														</ul>
													</div>
												</div>
											</div>
										</div>
									</div>

									<div class="activity_show">
										<div class="activity_list">
											<img src="https://test.huitengsoft.com/upload/microWeb/shareImg/defaultShareImg.png" alt="">
											<img src="https://test.huitengsoft.com/new_edition/jsp/microWeb/images/bg2.png" alt=""
												style="width: 58px;height: 58px;position: absolute;right: 0;top: 0;">
											<div class="ac_text">
												<p>这里是场地图设置</p>
												<ul class="clearfix">
													<li>
														2024-08-01
													</li>
												</ul>
											</div>
										</div>
										<!-- 鼠标移上隐藏 -->
										<div class="hide">
											<!-- 预览 -->
											<div class="hide-top2">
												<ul>
													<li>
														<div class="overviewSeat">效果图预览</div>
													</li>
												</ul>
											</div>
											<div class="hide-bottom">
												<div class="option">
													<div class="ag">
														<ul>
															<li>
																<i class="layui-icon layui-icon-edit" onclick="editMicroWeb(10007586, 6609);"></i>
															</li>
															<li>
																<span>编辑</span>
															</li>
														</ul>
													</div>
													<div class="ag">
														<ul class="copy-btn" data-clipboard-text="复制的内容">
															<li>
																<i class="layui-icon layui-icon-file"></i>
															</li>
															<li>
																<span>复制</span>
															</li>
														</ul>
													</div>
													<div class="ag">
														<ul onclick="setDelete(6609)">
															<li>
																<i class="layui-icon layui-icon-delete"></i>
															</li>
															<li>
																<span>删除</span>
															</li>
														</ul>
													</div>
												</div>
											</div>
										</div>
									</div>


								</div>
							</div>




						</div>


					</div>
				</div>
			</div>
		</div>
		<!-- 灰色背景 -->
		<div id="f-fade" class="black_overlay"></div>

		<!-- 删除 -->
		<div class="white-contentAll" id="light2" style="display:none;width: 500px;">
			<div class="white-header">
				<span class="white-headerTitle">
					删除
				</span>
				<span class="white-delete" onclick="cancel('light2')">
				</span>
			</div>
			<div class="white-body">
				<div class="module-text-box">您确定要删除吗？</div>
			</div>
			<div class="white-footer">
				<!-- <button class="white-close" onclick="cancel('light1')">
	        取消
	    </button> -->
				<button class="white-sure white-sureAdd" onclick="deleteUserAjax(this);">
					确定
				</button>
			</div>
		</div>


		<!-- 复制成功提示 -->
		<div class="white-contentAll" id="light3" style="display:none;width: 400px;">
			<div class="white-header">
				<span class="white-headerTitle">
					复制
				</span>
				<span class="white-delete" onclick="cancel('light3')">
				</span>
			</div>
			<div class="white-body">
				<div class="module-text-box" style="padding: 40px 0;">链接复制成功</div>
			</div>
			<!-- <div class="white-footer">
				<button class="white-close" onclick="cancel('light1')">
					取消
				</button>
				<button class="white-sure white-sureAdd" onclick="deleteUserAjax(this);">
					确定
				</button>
			</div> -->
		</div>

		<!-- 新增网页 -->
		<div class="white-contentAll" id="light4" style="display:none;width:500px;overflow: initial;">
			<div class="white-header">
				<span class="white-headerTitle">
					关联图设计
				</span>
				<span class="white-delete" onclick="cancel('light4')">
				</span>
			</div>
			<div class="white-body" style="max-height: 420px;overflow: initial;">
				<div class="data-module">
					<div class="data-list">
						<div class="data-list-title">
							<b class="redSpan">*</b>网页名称
						</div>
						<div class="data-list-input">
							<input type="text" class="layui-input" placeholder="输入最多25个汉字字符" />
						</div>
					</div>

					<div class="data-list">
						<div class="data-list-title">
							<b class="redSpan">*</b>请选择已有场地设计图
						</div>
						<div class="data-list-input layui-form">
							<select name="" id="">
								<option value="请选择">请选择</option>
								<option value="请选择1">请选择1</option>
								<option value="请选择2">请选择2</option>
							</select>
						</div>
					</div>

				</div>
			</div>
			<div class="white-footer">
				<button class="white-close" onclick="cancel('light4')">
					取消
				</button>
				<button class="white-sure white-sureAdd" onclick="saveAllocation();">
					确认生成
				</button>
			</div>
		</div>

		<!-- <script src="js/jquery-1.7.2.min.js"></script> -->
		<script src="js/jquery-3.6.3.min.js"></script>
		<script src="js/index.js"></script>

		<!-- <script src="translateJs/translate.js"></script>
		<script src="translateJs/translateCommon.js"></script> -->

		<script src="layui/layui.js"></script>
		<script>
			var selectLabels = "请选择行为标签"
			var layer = "";
			var form = "";
			layui.use(function() {
				var element = layui.element;
				element.render('nav');
				layer = layui.layer;
				form = layui.form;
				// 输入框点缀事件 - 搜索示例
				form.on('input-affix(search)', function(data) {
					var elem = data.elem; // 输入框
					var value = elem.value; // 输入框的值
					if (!value) {
						layer.msg('请输入搜索内容');
						return elem.focus()
					};
					// 模拟搜索跳转
					// location.href = '?keywords=' + value + '&_' + new Date().getTime() + '#affix-custom';
				});


				// 行为标签单选
				form.on('radio(powerRadio)', function(data) {
					var elem = data.elem; // 获得 checkbox 原始 DOM 对象
					var checked = elem.checked; // 获得 checkbox 选中状态
					var value = elem.value; // 获得 checkbox 值
					var othis = data.othis; // 获得 checkbox 元素被替换后的 jQuery 对象

					console.log(value)
				});

			});
		</script>
		<script>
			// 管理行为标签
			function manageTag() {
				cancel('light1')
				viewTask("light3")
			}

			// 遮罩层
			function viewTask(id) {
				$("div[id^=light]").hide();
				$("#" + id).show();
				$("#f-fade").show();
			}

			function cancel(id) {
				$("#" + id).hide();
				$("#f-fade").hide();
			}

			// 可使用金额双击编辑
			function availableAmountEdit(obj) {
				let thisText = $(obj).text()
				$(obj).hide()
				$(obj).parent().find(".availableAmountInput").show()
				$(obj).parent().find(".availableAmountInput").val(thisText)
			}
			// 编辑确定
			function availableAmountSure(obj) {
				let thisText = $(obj).val()
				$(obj).hide()
				$(obj).parent().find(".availableAmount").show()
				$(obj).parent().find(".availableAmount").text(thisText)
			}

			$(".tab-box").find("span").click(function() {
				let thisIndex = $(this).index()
				$(this).addClass("tab-box-act").siblings().removeClass("tab-box-act")
				$(".content-html").find(".content-list").eq(thisIndex).show().siblings().hide()
			})
		</script>

		<!-- 复制方法 -->
		<script src="js/clipboard.min.js"></script>
		<script>
			var clipboard = new ClipboardJS('.copy-btn');

			clipboard.on('success', function(e) {
				console.log(e);
				viewTask('light3')
			});

			clipboard.on('error', function(e) {
				console.log(e);
			});
		</script>


	</body>
</html>