.uploadBg {
	width: 100%;
	overflow: hidden;
	margin-top: 10px;
}

.uploadTips {
	font-size: 13px;
	color: #1890ff;
	margin-top: 5px;
}

.uploadImgBox,
.bgBox {
	width: 100%;
	overflow: hidden;
	margin: 10px auto;
}

.uploadImgBox img {
	width: 150px;
}

.seatNameBox {
	width: 100%;
	overflow: hidden;
	display: flex;
	align-items: center;
	margin-bottom: 10px;
}

.seatNameBox input {
	flex: 1;
	width: 100px;
}

.rangeBox {
	width: 100%;
	overflow: hidden;
	padding: 15px 10px;
	box-sizing: border-box;
}

.rangeBoxTitle {
	width: 100%;
	overflow: hidden;
	font-size: 14px;
	color: #000;
	margin-bottom: 10px;
}

.bgSize {
	margin: 10px auto;
}

.zoomBox {
	position: absolute;
	right: 15px;
	top: 20px;
}

.zoomBox .layui-icon {
	font-size: 25px;
	cursor: pointer;
	color: #666;
}

.freeContainerModule {
	position: relative;
}

.seatBoxContent {
	width: 100%;
	overflow-x: auto;
	overflow-y: auto;
	height: calc(100vh - 100px);
	box-sizing: border-box;
	margin-top: 20px;
}