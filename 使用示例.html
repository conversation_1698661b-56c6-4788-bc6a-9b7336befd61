<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>座位图组件使用示例</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .example {
            margin-bottom: 30px;
        }
        
        .code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        
        .event-log {
            max-height: 200px;
            overflow-y: auto;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <div class="header">
                <h1>🎯 座位图组件使用示例</h1>
                <p>演示如何在其他项目中使用座位图组件</p>
            </div>
            
            <!-- 组件使用示例 -->
            <div class="example">
                <h3>1. 基础使用</h3>
                <seat-map-viewer 
                    :width="800" 
                    :height="400"
                    :initial-rows="15"
                    :initial-cols="20"
                    :show-controls="true"
                    :show-stats="true"
                    @seat-click="onSeatClick"
                    @seat-hover="onSeatHover"
                    @view-change="onViewChange"
                    @seats-generated="onSeatsGenerated"
                ></seat-map-viewer>
            </div>
            
            <!-- 使用说明 -->
            <div class="example">
                <h3>2. 如何在您的项目中使用</h3>
                
                <h4>步骤1: 复制文件</h4>
                <p>将以下两个文件复制到您的项目根目录：</p>
                <ul>
                    <li><code>SeatMapViewer.vue</code> - Vue组件</li>
                    <li><code>SeatMapEngine.js</code> - 核心引擎</li>
                </ul>
                
                <h4>步骤2: 在组件中引入</h4>
                <div class="code">
&lt;template&gt;
  &lt;div&gt;
    &lt;h2&gt;我的座位图&lt;/h2&gt;
    &lt;SeatMapViewer 
      :width="800" 
      :height="600"
      :initial-rows="20"
      :initial-cols="30"
      @seat-click="handleSeatClick"
    /&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script&gt;
import SeatMapViewer from './SeatMapViewer.vue'

export default {
  components: {
    SeatMapViewer
  },
  methods: {
    handleSeatClick(seat) {
      console.log('点击了座位:', seat)
      // 这里可以添加您的业务逻辑
    }
  }
}
&lt;/script&gt;
                </div>
                
                <h4>步骤3: 就这么简单！</h4>
                <p>组件会自动处理所有的渲染、交互和性能优化。</p>
            </div>
            
            <!-- 事件日志 -->
            <div class="example" v-if="eventLogs.length > 0">
                <h3>3. 事件日志</h3>
                <div class="event-log">
                    <div v-for="(log, index) in eventLogs" :key="index" style="margin-bottom: 5px;">
                        <span style="color: #666;">{{ log.time }}</span> - {{ log.message }}
                    </div>
                </div>
                <button @click="clearLogs" style="margin-top: 10px; padding: 5px 10px;">清空日志</button>
            </div>
            
            <!-- 配置说明 -->
            <div class="example">
                <h3>4. 主要配置选项</h3>
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #f8f9fa;">
                            <th style="padding: 8px; border: 1px solid #ddd;">属性</th>
                            <th style="padding: 8px; border: 1px solid #ddd;">类型</th>
                            <th style="padding: 8px; border: 1px solid #ddd;">默认值</th>
                            <th style="padding: 8px; border: 1px solid #ddd;">说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd;">width</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">Number</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">800</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">组件宽度</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd;">height</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">Number</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">600</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">组件高度</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd;">initialRows</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">Number</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">10</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">初始行数</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd;">initialCols</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">Number</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">10</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">初始列数</td>
                        </tr>
                        <tr>
                            <td style="padding: 8px; border: 1px solid #ddd;">showControls</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">Boolean</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">true</td>
                            <td style="padding: 8px; border: 1px solid #ddd;">显示控制面板</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- 这里需要引入实际的组件文件 -->
    <script type="module">
        import { createApp } from 'https://unpkg.com/vue@3/dist/vue.esm-browser.js'
        
        // 模拟组件（实际使用时会引入真实的组件文件）
        const SeatMapViewer = {
            template: `
                <div style="border: 2px dashed #ccc; display: flex; align-items: center; justify-content: center; background: #f9f9fa; border-radius: 8px;">
                    <div style="text-align: center; color: #666; padding: 40px;">
                        <div style="font-size: 48px; margin-bottom: 10px;">🎯</div>
                        <div style="font-size: 18px; margin-bottom: 10px;">座位图组件</div>
                        <div style="font-size: 14px; color: #999;">{{ width }} × {{ height }}</div>
                        <div style="font-size: 14px; color: #999;">{{ initialRows }} 行 × {{ initialCols }} 列</div>
                        <div style="font-size: 12px; color: #999; margin-top: 10px;">
                            实际使用时这里会显示真实的座位图
                        </div>
                    </div>
                </div>
            `,
            props: {
                width: { type: Number, default: 800 },
                height: { type: Number, default: 600 },
                initialRows: { type: Number, default: 10 },
                initialCols: { type: Number, default: 10 },
                showControls: { type: Boolean, default: true },
                showStats: { type: Boolean, default: true }
            },
            mounted() {
                // 模拟事件触发
                setTimeout(() => {
                    this.$emit('seats-generated', { 
                        total: this.initialRows * this.initialCols 
                    })
                }, 1000)
                
                // 模拟点击事件
                setTimeout(() => {
                    this.$emit('seat-click', { 
                        id: '5-10', 
                        row: 5, 
                        col: 10, 
                        number: '06-11' 
                    })
                }, 2000)
            }
        }
        
        createApp({
            components: {
                SeatMapViewer
            },
            data() {
                return {
                    eventLogs: []
                }
            },
            methods: {
                addLog(message) {
                    this.eventLogs.unshift({
                        time: new Date().toLocaleTimeString(),
                        message: message
                    })
                    if (this.eventLogs.length > 50) {
                        this.eventLogs.pop()
                    }
                },
                clearLogs() {
                    this.eventLogs = []
                },
                onSeatsGenerated(data) {
                    this.addLog(`✅ 座位生成完成: ${data.total} 个座位`)
                },
                onSeatClick(seat) {
                    this.addLog(`🖱️ 点击座位: ${seat.number || seat.id}`)
                },
                onSeatHover(seat) {
                    this.addLog(`👆 悬停座位: ${seat.number || seat.id}`)
                },
                onViewChange(view) {
                    this.addLog(`🔍 视图变化: 缩放 ${Math.round(view.zoom * 100)}%`)
                }
            }
        }).mount('#app')
    </script>
</body>
</html>
