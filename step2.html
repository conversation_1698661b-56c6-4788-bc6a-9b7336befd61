<!DOCTYPE html>
<html lang="ch">
	<head>
		<title>设置座位</title>
		<!-- 设置 viewport -->
		<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
		<!-- IE -->
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<!-- 兼容国产浏览器的高速模式 -->
		<meta name="renderer" content="webkit">
		<meta name="Author" content="<EMAIL>" />
		<meta name="Keywords" content="" />
		<meta name="Description" content="" />
		<meta charset="UTF-8">
		<link rel="stylesheet" type="text/css" href="css/reset.css">
		<link rel="stylesheet" href="css/translateCommon.css">
		<link rel="stylesheet" href="layui/css/layui.css" />
		<link rel="stylesheet" href="css/stepPlug.css" />
		<link rel="stylesheet" href="css/seat3.css" />
		<link rel="stylesheet" href="css/step2only.css" />

		<!-- 左侧工具栏样式 -->
		<style>
			/* 主布局 */
			.main-content {
				display: flex !important;
				height: calc(100vh - 120px) !important;
				overflow: hidden !important;
			}

			/* 左侧工具栏 */
			.sidebar-toolbar {
				width: 200px;
				background: #f8f9fa;
				border-right: 1px solid #e9ecef;
				overflow-y: auto;
				flex-shrink: 0;
				padding: 0;
			}

			/* 工具栏区块 */
			.toolbar-section {
				border-bottom: 1px solid #e9ecef;
				padding: 8px;
			}

			.toolbar-section:last-child {
				border-bottom: none;
			}

			.section-title {
				font-size: 11px;
				font-weight: 600;
				color: #374151;
				margin-bottom: 6px;
				padding-bottom: 4px;
				border-bottom: 1px solid #3b82f6;
			}

			/* 按钮容器 */
			.btn-grid {
				display: grid;
				grid-template-columns: 1fr 1fr;
				gap: 4px;
			}

			.btn-grid-single {
				display: grid;
				grid-template-columns: 1fr;
				gap: 4px;
			}

			/* 侧边栏按钮 */
			.sidebar-btn {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 4px;
				padding: 6px 4px;
				border: 1px solid #d1d5db;
				background: white;
				border-radius: 4px;
				cursor: pointer;
				transition: all 0.2s;
				font-size: 10px;
				text-align: center;
			}

			.sidebar-btn:hover {
				background: #f3f4f6;
				border-color: #9ca3af;
			}

			.sidebar-btn.active {
				background: #3b82f6;
				border-color: #3b82f6;
				color: white;
			}

			.sidebar-btn.primary {
				background: #059669;
				border-color: #059669;
				color: white;
			}

			.sidebar-btn.primary:hover {
				background: #047857;
				border-color: #047857;
			}

			.sidebar-btn i {
				font-size: 12px;
				flex-shrink: 0;
			}

			.sidebar-btn span {
				white-space: nowrap;
				font-size: 9px;
			}

			.sidebar-btn:disabled {
				background: #f3f4f6;
				border-color: #e5e7eb;
				color: #9ca3af;
				cursor: not-allowed;
			}

			.sidebar-btn:disabled:hover {
				background: #f3f4f6;
				border-color: #e5e7eb;
				transform: none;
			}

			/* 模式切换按钮样式 */
			.mode-btn {
				position: relative;
			}

			.mode-btn.active {
				background: #3b82f6;
				border-color: #3b82f6;
				color: white;
				box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
			}

			.mode-btn.active::after {
				content: '';
				position: absolute;
				bottom: -2px;
				left: 50%;
				transform: translateX(-50%);
				width: 0;
				height: 0;
				border-left: 4px solid transparent;
				border-right: 4px solid transparent;
				border-bottom: 4px solid #3b82f6;
			}

			/* 步骤进度 */
			.step-progress {
				display: flex;
				flex-direction: column;
				gap: 4px;
			}

			.step-item {
				display: flex;
				align-items: center;
				gap: 6px;
				padding: 4px;
				border-radius: 3px;
				transition: all 0.2s;
			}

			.step-item.completed {
				background: #d1fae5;
				color: #065f46;
			}

			.step-item.active {
				background: #dbeafe;
				color: #1e40af;
			}

			.step-circle {
				width: 20px;
				height: 20px;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				font-weight: 600;
				font-size: 10px;
				flex-shrink: 0;
			}

			.step-item.completed .step-circle {
				background: #059669;
				color: white;
			}

			.step-item.active .step-circle {
				background: #3b82f6;
				color: white;
			}

			.step-item .step-circle {
				background: #e5e7eb;
				color: #6b7280;
			}

			.step-label {
				font-size: 10px;
				font-weight: 500;
			}

			/* 信息显示 */
			.info-display {
				display: flex;
				flex-direction: column;
				gap: 3px;
			}

			.info-row {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 4px 6px;
				background: white;
				border-radius: 3px;
				border: 1px solid #e5e7eb;
			}

			.info-label {
				font-size: 10px;
				color: #6b7280;
				font-weight: 500;
			}

			.info-value {
				font-size: 11px;
				color: #059669;
				font-weight: 600;
			}

			/* 提示内容 */
			.tips-content {
				background: #fef3c7;
				border: 1px solid #f59e0b;
				border-radius: 3px;
				padding: 6px;
			}

			.tips-content p {
				margin: 0 0 2px 0;
				font-size: 9px;
				color: #92400e;
				line-height: 1.2;
			}

			.tips-content p:last-child {
				margin-bottom: 0;
			}

			/* 右侧画布区域 */
			.canvas-area {
				flex: 1;
				position: relative;
				overflow: hidden;
				height: 100% !important; /* 确保高度适应内容 */
			}

			/* 修复容器高度问题 */
			.seatBoxContentSetp2 {
				height: 100% !important; /* 覆盖CSS文件中的固定高度 */
				max-height: 100% !important;
			}

			.newSeatBox {
				height: 100% !important;
				max-height: 100% !important;
			}

			.seatBoxContent {
				width: 100% !important;
				height: 100% !important;
				overflow: hidden !important;
				border: none !important;
				margin: 0 !important;
				padding: 0 !important;
			}

			.newSeatBox {
				width: 100% !important;
				height: 100% !important;
				border: none !important;
				margin: 0 !important;
				padding: 0 !important;
			}

			.seatBox {
				width: 100% !important;
				height: 100% !important;
				position: relative !important;
				background: white !important;
				overflow: hidden !important;
				border: none !important;
				padding: 0 !important;
			}

			/* 清理可能的遗留元素 */
			.seatBox .seat-table,
			.seatBox table,
			.seatBox tbody,
			.seatBox tr,
			.seatBox td {
				display: none !important;
				visibility: hidden !important;
			}

			/* 确保Canvas是唯一显示的内容 */
			.seatBox > *:not(canvas) {
				display: none !important;
			}

			.seatBox canvas {
				display: block !important;
				background: white !important;
			}

			/* 正常样式 - 移除调试边框 */
			.canvas-area {
				background: transparent;
			}

			.seatBoxContent {
				background: transparent;
			}

			.newSeatBox {
				background: transparent;
			}

			.seatBox {
				background: white !important;
			}

			/* 强制移除所有可能的额外内容 */
			.seatBox::after,
			.seatBox::before,
			.newSeatBox::after,
			.newSeatBox::before,
			.seatBoxContent::after,
			.seatBoxContent::before {
				display: none !important;
				content: none !important;
			}

			/* 分区设置样式 */
			.zone-form {
				margin-top: 8px;
			}

			.form-row {
				display: flex;
				align-items: center;
				margin-bottom: 6px;
				font-size: 10px;
			}

			.form-row label {
				width: 60px;
				color: #6b7280;
				font-weight: 500;
			}

			.zone-input {
				flex: 1;
				padding: 4px 6px;
				border: 1px solid #d1d5db;
				border-radius: 3px;
				font-size: 10px;
			}

			.zone-color {
				width: 40px;
				height: 24px;
				border: 1px solid #d1d5db;
				border-radius: 3px;
				cursor: pointer;
			}

			.zone-list {
				margin-top: 8px;
				max-height: 120px;
				overflow-y: auto;
			}

			.zone-item {
				display: flex;
				align-items: center;
				padding: 4px 6px;
				margin-bottom: 3px;
				background: white;
				border: 1px solid #e5e7eb;
				border-radius: 3px;
				font-size: 9px;
			}

			.zone-color-dot {
				width: 12px;
				height: 12px;
				border-radius: 50%;
				margin-right: 6px;
				flex-shrink: 0;
			}

			.zone-info {
				flex: 1;
				display: flex;
				flex-direction: column;
				gap: 1px;
			}

			.zone-name {
				font-weight: 500;
				color: #374151;
			}

			.zone-count {
				color: #6b7280;
			}

			.zone-actions {
				display: flex;
				gap: 2px;
			}

			.zone-action-btn {
				width: 16px;
				height: 16px;
				border: none;
				border-radius: 2px;
				cursor: pointer;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 10px;
			}

			.zone-edit-btn {
				background: #3b82f6;
				color: white;
			}

			.zone-delete-btn {
				background: #ef4444;
				color: white;
			}

			/* 响应式设计 */
			@media (max-width: 1200px) {
				.sidebar-toolbar {
					width: 240px;
				}

				.sidebar-btn {
					padding: 10px 12px;
					font-size: 13px;
				}

				.section-title {
					font-size: 13px;
				}
			}
		</style>

	</head>
	<body style="margin: 0; padding: 0; overflow: hidden;">
		<div id="section">
			<!-- header -->
			<div id="header">
				<div class="hui">
					<img src="images/hui.jpg" alt="">
				</div>
				<div class="logo">
					<h1 class="f_left">
						<a href="#">
							<img src="images/logo.png" alt=""></a>
					</h1>
					<div class="f_right logo_r">
						<ul class="logo_r_show">
							<!-- <li class="search">
								<p>
									<span class="f_left">受邀者</span>
									<input class="f_left" type="text"> <i></i>
								</p>
							</li> -->
							<!-- <li class="translate-box">
								<a class="translate-box-cn" href="javascript:translate.changeLanguage('english');javascript:void(0);">
									<img src="images/translateEn.png" alt="">
								</a>
								<a class="translate-box-en" style="display: none;"
									href="javascript:translate.changeLanguage('chinese_simplified');javascript:void(0);">
									<img src="images/translateCn.png" alt="">
								</a>
							</li> -->
							<li class="h_qiandao">
								<a href="#">微信签到</a>
							</li>
							<li class="prompt">
								<a href="#">
									<span>0</span>
								</a>
							</li>
							<li class="help">帮助与文档</li>
							<li class="tel">18621589099</li>
						</ul>
					</div>
				</div>
			</div>

			<!-- content -->
			<div id="content" class="clearfix">
				<!-- 左边导航 -->
				<div class="c_left">
					<ul class="c_left_nav f_left">
						<li class="current" title="活动">
							<img src="images/icon6.png" alt="">
						</li>
						<li title="应用">
							<img src="images/icon7.png" alt="">
						</li>
						<li title="联系人">
							<img src="images/icon8.png" alt="">
						</li>
						<li title="供应商">
							<img src="images/icon9.png" alt="">
						</li>
						<li title="账户">
							<img src="images/icon10.png" alt="">
						</li>
					</ul>

				</div>

				<!-- 主要模块 -->
				<div class="c_right">
					<div class="main">

						<div class="new-content-top">
							<span class="layui-breadcrumb" lay-separator=">">
								<a href="">首页</a>
								<a href="">活动列表</a>
								<a href="">xoxcxxxx活动</a>
								<a><cite>活动总览</cite></a>
							</span>
							<div class="content-top-btn" style="visibility: hidden;">
								<button class="layui-btn" type="button" onclick="">占位</button>
							</div>
						</div>

						<div class="main-content" style="overflow: hidden !important; display: flex;">
							<!-- 左侧工具栏 -->
							<div class="sidebar-toolbar" id="sidebarToolbar">
								<!-- 导航 & 进度 -->
								<div class="toolbar-section">
									<div class="section-title">导航 (步骤2/2)</div>
									<div class="btn-grid">
										<button type="button" class="sidebar-btn primary" onclick="location.href='step1.html'">
											<i class="layui-icon layui-icon-left"></i>
											<span>上一步</span>
										</button>
										<button type="button" class="sidebar-btn primary" id="saveTop" onclick="saveLayout()">
											<i class="layui-icon layui-icon-ok"></i>
											<span>保存并进入第三步</span>
										</button>
									</div>
								</div>

								<!-- 座位布局工具 -->
								<div class="toolbar-section" id="layoutTools">
									<div class="section-title">座位布局工具</div>
									<div class="btn-grid">
										<button type="button" class="sidebar-btn tool-btn" id="selectTool" data-tool="select">
											<i class="layui-icon layui-icon-ok-circle"></i>
											<span>单个移除</span>
										</button>
										<button type="button" class="sidebar-btn tool-btn" id="boxSelectAddTool" data-tool="boxselect-add">
											<i class="layui-icon layui-icon-reduce-circle"></i>
											<span>批量移除</span>
										</button>
									</div>
									<div class="btn-grid">
										<button type="button" class="sidebar-btn tool-btn" id="boxSelectRemoveTool" data-tool="boxselect-remove">
											<i class="layui-icon layui-icon-add-1"></i>
											<span>批量恢复</span>
										</button>
										<button type="button" class="sidebar-btn tool-btn active" id="panTool" data-tool="pan">
											<i class="layui-icon layui-icon-cursor"></i>
											<span>拖拽</span>
										</button>
									</div>
								</div>



								<!-- 视图 -->
								<div class="toolbar-section">
									<div class="section-title">视图</div>
									<div class="btn-grid">
										<button type="button" class="sidebar-btn" id="zoomInBtn">
											<i class="layui-icon layui-icon-add-circle"></i>
											<span>放大</span>
										</button>
										<button type="button" class="sidebar-btn" id="zoomOutBtn">
											<i class="layui-icon layui-icon-reduce-circle"></i>
											<span>缩小</span>
										</button>
										<button type="button" class="sidebar-btn" id="fitToScreenBtn">
											<i class="layui-icon layui-icon-screen-full"></i>
											<span>适应</span>
										</button>
										<button type="button" class="sidebar-btn" id="centerViewBtn">
											<i class="layui-icon layui-icon-location"></i>
											<span>居中</span>
										</button>
									</div>
								</div>

								<!-- 操作 -->
								<div class="toolbar-section">
									<div class="section-title">操作</div>
									<div class="btn-grid-single">
										<button type="button" class="sidebar-btn" id="undoBtn" disabled>
											<i class="layui-icon layui-icon-return"></i>
											<span>撤销</span>
										</button>
									</div>
								</div>



								<!-- 信息 -->
								<div class="toolbar-section">
									<div class="section-title">信息</div>
									<div class="info-display">
										<div class="info-row">
											<span class="info-label">已选:</span>
											<span class="info-value" id="selectedCount">0</span>
										</div>
										<div class="info-row">
											<span class="info-label">总计:</span>
											<span class="info-value" id="totalSeats">0</span>
										</div>
										<div class="info-row">
											<span class="info-label">缩放:</span>
											<span class="info-value" id="zoomLevel">100%</span>
										</div>
									</div>
								</div>
							</div>

							<!-- 右侧画布区域 -->
							<div class="canvas-area">
								<div class="seatBoxContent seatBoxContentSetp2" id="seatBoxContent">
									<div class="newSeatBox">
										<div class="seatBox" id="seatBox" zoom="1">
											<!-- Canvas座位图将在这里生成，标尺跟随座位移动 -->
										</div>
									</div>
								</div>
							</div>
						</div>

							<!-- 座位布局右侧面板 -->
							<div class="main-right" id="layoutRightPanel">
								<div class="container">
									<div class="layui-tab" lay-filter="tab-hash">
										<ul class="layui-tab-title">
											<li class="layui-this" lay-id="11">座位布局设置</li>
										</ul>
										<div class="layui-tab-content">
											<div class="layui-tab-item layui-show">
												<h4 class="text-danger text-left">⚠️ 场地默认显示所有座位，点击移除不需要的座位</h4>
												<div class="info-section">
													<p><strong>正确逻辑：</strong></p>
													<ul>
														<li>🟢 <strong>初始状态</strong>：所有位置都显示座位（满座可见）</li>
														<li>🔴 <strong>点击座位</strong>：移除该座位（留出过道、空地）</li>
														<li>🔴 <strong>批量移除</strong>：批量移除多个座位</li>
														<li>🔍 <strong>最终目的</strong>：留出走廊、过道、安全出口等空间</li>
													</ul>
													<p><strong>操作提示：</strong></p>
													<ul>
														<li>使用"切换座位"工具点击移除/恢复单个座位</li>
														<li>使用"批量移除"工具批量移除座位</li>
														<li>使用"批量恢复"工具批量恢复座位</li>
														<li>使用拖拽工具移动视图</li>
													</ul>
												</div>
											</div>
										</div>
									</div>

									<div class="bottom-btn">
										<button class="layui-btn" type="button" id="saveLayoutBtn">保存布局并进入第三步</button>
									</div>
								</div>
							</div>


						</div>



					</div>
				</div>
			</div>
		</div>
		<!-- 灰色背景 -->
		<div id="fade" class="black_overlay"></div>

		<!-- 加载弹窗 -->
		<div class="loading-module">
			<div class="loading-module-bg">
				<div class="loadingSix">
					<span></span>
					<span></span>
					<span></span>
					<span></span>
					<span></span>
					<span></span>
					<span></span>
					<span></span>
				</div>
				<div class="loading-text-box">页面正在加载中，请稍等。。。。</div>
			</div>
		</div>



		<!-- <script src="js/jquery-1.7.2.min.js"></script> -->
		<script src="js/jquery-3.6.3.min.js"></script>
		<script src="js/index.js"></script>

		<!-- 		<script src="translateJs/translate.js"></script>
		<script src="translateJs/translateCommon.js"></script> -->

		<script src="layui/layui.js"></script>
		<script>
			layui.use(function() {
				var element = layui.element;
				element.on('tab(tab-hash)', function(obj) {
					location.hash = hashName + '=' + this.getAttribute('lay-id');
				});

			});
		</script>
		<!-- 先加载SeatMapEngine -->
		<script src="SeatMapEngine.js"></script>

		<!-- 使用新的Canvas座位系统 -->
		<script>
			// 直接使用SeatMapEngine，不需要import

			// 检查localStorage数据
			let seatRows = localStorage.getItem("seatRows");
			let seatCols = localStorage.getItem("seatCols");
			if (!seatRows || !seatCols || seatRows === 'null' || seatCols === 'null') {
				location.href = "step1.html";
			}

			// 加载已有座位数据
			var datas = JSON.parse(localStorage.getItem("seats"));
			if (datas == undefined || datas == '') {
				datas = {};
			}



			let engine;
			let seatsData = {};
			let isSpacePressed = false; // 跟踪空格键状态



			$(document).ready(function() {

				// 创建Canvas元素，完全适应容器
				const canvas = document.createElement('canvas');
				const container = document.querySelector("#seatBox");

				// 设置Canvas样式，确保完全适应容器
				canvas.style.width = '100%';
				canvas.style.height = '100%';
				canvas.style.display = 'block';
				canvas.style.position = 'absolute';
				canvas.style.top = '0';
				canvas.style.left = '0';

				container.appendChild(canvas);

				// 初始化SeatMapEngine
				engine = new SeatMapEngine(canvas, {
					seatSize: 20,
					seatSpacing: 4,
					showSeatNumbers: true,
					enableRoundedCorners: true,
					showHeaders: true, // 启用标尺
					onSeatClick: handleSeatClick,
					onSeatHover: handleSeatHover,
					onViewChange: handleViewChange
				});

				// 确保Canvas完全适应容器（在引擎初始化后）
				fixCanvasSize(canvas, container);

				// 扩展引擎，添加标尺渲染功能
				addHeadersToEngine(engine);

				// 添加标尺点击功能
				addHeaderClickHandlers(engine);

				// 监听窗口大小变化
				window.addEventListener('resize', () => {
					fixCanvasSize(canvas, container);
					centerSeats(engine);
				});

				// 监听键盘事件
				setupKeyboardEvents();

				// 生成座位
				engine.generateSeats(parseInt(seatRows), parseInt(seatCols));

				// 居中显示座位
				centerSeats(engine);

				// 初始化座位状态 - 使用setTimeout确保座位已生成
				setTimeout(() => {
					// 如果有已保存的数据，加载已保存的数据；否则初始化为满座状态
					if (datas && Object.keys(datas).length > 0) {
						loadExistingData(datas);
					} else {
						initializeFullSeats();
					}
				}, 100);

				// 创建兼容性接口
				createCompatibilityInterface();

				// 初始化工具栏
				initToolbar();

				// 隐藏加载遮罩
				$(".loading-module").hide();
			});

			// 初始化为满座状态
			function initializeFullSeats() {
				if (!engine.seats || engine.seats.length === 0) {
					return;
				}

				// 默认所有座位都是可见的（available状态）
				seatsData = {};

				engine.seats.forEach(seat => {
					const seatKey = seat.row + "_" + seat.col;
					const seatInfo = {
						row: seat.row,
						col: seat.col,
						price: 0,
						color: "",
						groupName: "",
						thre_id: 10,
						hall_id: 10,
						movie_id: 10
					};

					// 同时更新seatsData和seats变量
					seatsData[seatKey] = seatInfo;
					seats[seatKey] = seatInfo;  // 关键：同时更新seats变量

					// 设置为available状态（默认可见，未选中）
					seat.status = 'available';
				});

				// 强制渲染
				engine.render();
				updateToolbarInfo();




			}

			// 处理座位点击 - 切换座位状态
			function handleSeatClick(seat) {
				const seatKey = seat.row + "_" + seat.col;
				const engineSeat = engine.seats.find(s => s.row === seat.row && s.col === seat.col);

				// 座位布局：切换座位状态
				saveOperationToHistory('seat-click', `点击座位 ${seat.row + 1}-${seat.col + 1}`);

				if (seatsData[seatKey]) {
					// 座位存在，点击后移除
					delete seatsData[seatKey];
					if (engineSeat) {
						engineSeat.status = 'empty';  // 设为空状态，不可见
					}

				} else {
					// 座位不存在，点击后恢复
					seatsData[seatKey] = {
						row: seat.row,
						col: seat.col,
						price: 0,
						color: "",
						groupName: "",
						thre_id: 10,
						hall_id: 10,
						movie_id: 10
					};
					if (engineSeat) {
						engineSeat.status = 'available';  // 可见状态
					}

				}

				// 强制重新渲染
				engine.render();

				// 更新工具栏信息
				updateToolbarInfo();
			}

			// 处理座位悬停
			function handleSeatHover(seat) {
				// 可以添加悬停效果
			}

			// 处理视图变化
			function handleViewChange(view) {
				// 可以添加视图变化处理
			}

			// 加载已有数据
			function loadExistingData(data) {
				// Step2专注于座位布局，直接加载数据

				seatsData = data;
				// 更新引擎中的座位状态
				Object.keys(data).forEach(seatKey => {
					const [row, col] = seatKey.split('_').map(Number);
					const seat = engine.seats.find(s => s.row === row && s.col === col);
					if (seat && data[seatKey]) {
						const seatData = data[seatKey];
						seat.status = 'selected';

						// 如果有分组颜色信息，设置分组颜色
						if (seatData.color && seatData.groupName) {
							seat.groupColor = seatData.color;
							seat.groupName = seatData.groupName;

						}
					}
				});
				engine.render();

			}



			// 为引擎添加标尺功能
			function addHeadersToEngine(engine) {
				// 保存原始的render方法
				const originalRender = engine.render.bind(engine);

				// 重写render方法，正确处理变换
				engine.render = function() {
					const startTime = performance.now()

					// 清空画布
					this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height)

					// 设置变换
					this.ctx.save()
					this.ctx.translate(this.state.offsetX, this.state.offsetY)
					this.ctx.scale(this.state.zoom, this.state.zoom)

					// 先渲染标尺（在变换内）
					renderHeaders(engine);

					// 然后渲染座位（在变换内，但不包括选择框）
					this.renderSeatsOnly()

					this.ctx.restore()

					// 在变换外绘制选择框
					if (this.state.isSelecting) {
						this.renderSelectionBox()
					}

					// 更新性能统计
					const endTime = performance.now()
					this.stats.renderTime = endTime - startTime
					this.updateFPS()

					// 触发视图变化回调
					this.callbacks.onViewChange({
						zoom: this.state.zoom,
						offsetX: this.state.offsetX,
						offsetY: this.state.offsetY,
						stats: this.stats
					})
				};

				// 创建只渲染座位的方法（不包括选择框）
				engine.renderSeatsOnly = function() {
					// 视口裁剪 - 只绘制可见座位
					const visibleSeats = this.getVisibleSeats()
					this.stats.visibleSeats = visibleSeats.length



					// 根据数量选择绘制模式
					if (visibleSeats.length > 1200 || this.state.isHighPerformanceMode) {
						this.renderSeatsBatch(visibleSeats)
					} else {
						this.renderSeatsIndividual(visibleSeats)
					}

					// 绘制文字
					if (this.shouldShowDetailedView()) {
						this.renderTexts(visibleSeats)
					}
				};

				// 添加行列选择功能（智能模式）
				engine.selectRow = function(rowIndex) {
					const rowSeats = engine.seats.filter(seat => seat.row === rowIndex);
					const selectedCount = rowSeats.filter(seat => {
						const seatKey = seat.row + "_" + seat.col;
						return seatsData[seatKey];
					}).length;

					// 如果所有座位都存在，则移除整行；如果没有座位，则添加整行
					const shouldSelect = selectedCount === 0;
					const action = shouldSelect ? '选择' : '取消选择';



					// 保存操作前状态
					saveOperationToHistory('select-row', `${action}第${rowIndex + 1}行`);

					rowSeats.forEach(seat => {
						const seatKey = seat.row + "_" + seat.col;
						if (shouldSelect) {
							// 选择模式：恢复座位（设为可见状态）
							if (!seatsData[seatKey]) {
								seatsData[seatKey] = {
									row: seat.row,
									col: seat.col,
									price: 0,
									color: "",
									groupName: "",
									thre_id: 10,
									hall_id: 10,
									movie_id: 10
								};
								seat.status = 'available';  // 恢复为可见的灰色状态
							}
						} else {
							// 取消模式：移除座位（设为不可见状态）
							if (seatsData[seatKey]) {
								delete seatsData[seatKey];
								seat.status = 'empty';  // 设为空状态，不可见
							}
						}
					});

					engine.render();
					updateToolbarInfo();
				};

				engine.selectColumn = function(colIndex) {
					const colSeats = engine.seats.filter(seat => seat.col === colIndex);
					const selectedCount = colSeats.filter(seat => {
						const seatKey = seat.row + "_" + seat.col;
						return seatsData[seatKey];
					}).length;

					// 如果所有座位都存在，则移除整列；如果没有座位，则添加整列
					const shouldSelect = selectedCount === 0;
					const action = shouldSelect ? '选择' : '取消选择';

					// 保存操作前状态
					saveOperationToHistory('select-column', `${action}第${colIndex + 1}列`);

					colSeats.forEach(seat => {
						const seatKey = seat.row + "_" + seat.col;
						if (shouldSelect) {
							// 选择模式：恢复座位（设为可见状态）
							if (!seatsData[seatKey]) {
								seatsData[seatKey] = {
									row: seat.row,
									col: seat.col,
									price: 0,
									color: "",
									groupName: "",
									thre_id: 10,
									hall_id: 10,
									movie_id: 10
								};
								seat.status = 'available';  // 恢复为可见的灰色状态
							}
						} else {
							// 取消模式：移除座位（设为不可见状态）
							if (seatsData[seatKey]) {
								delete seatsData[seatKey];
								seat.status = 'empty';  // 设为空状态，不可见
							}
						}
					});

					engine.render();
					updateToolbarInfo();
				};
			}

			// 渲染标尺（在变换内，会跟座位一起缩放）
			function renderHeaders(engine) {
				const ctx = engine.ctx;
				const config = engine.config;

				// 设置标尺样式（注意：现在在变换内，字体大小会被缩放）
				ctx.fillStyle = '#666';
				// 字体大小需要根据缩放调整，保持可读性
				const fontSize = Math.max(8, Math.min(16, 12 / engine.state.zoom));
				ctx.font = `${fontSize}px Arial`;
				ctx.textAlign = 'center';
				ctx.textBaseline = 'middle';

				// 计算标尺位置（在座位坐标系内）
				const headerOffset = 15;
				const seatSize = config.seatSize;
				const spacing = config.seatSpacing;

				// 绘制行标尺（左侧）
				for (let row = 0; row < parseInt(seatRows); row++) {
					const y = row * (seatSize + spacing) + seatSize/2;
					const x = -headerOffset;

					ctx.fillText((row + 1).toString(), x, y);
				}

				// 绘制列标尺（顶部）
				for (let col = 0; col < parseInt(seatCols); col++) {
					const x = col * (seatSize + spacing) + seatSize/2;
					const y = -headerOffset;

					ctx.fillText((col + 1).toString(), x, y);
				}
			}



			// 添加标尺点击处理
			function addHeaderClickHandlers(engine) {
				// 直接在Canvas上添加我们自己的事件监听器，优先级更高
				engine.canvas.addEventListener('mousedown', function(e) {
					const rect = engine.canvas.getBoundingClientRect();
					const x = e.clientX - rect.left;
					const y = e.clientY - rect.top;

					// 检查是否点击了标尺
					const headerClicked = checkHeaderClick(engine, x, y);
					if (headerClicked) {
						e.preventDefault();
						e.stopPropagation();
						return false;
					}
				}, true); // 使用捕获阶段，优先级更高
			}

			// 检查标尺点击（考虑缩放和偏移）
			function checkHeaderClick(engine, x, y) {
				const config = engine.config;
				const headerOffset = 15;
				const seatSize = config.seatSize;
				const spacing = config.seatSpacing;
				const clickTolerance = 10 / engine.state.zoom; // 点击容差也要考虑缩放

				// 将屏幕坐标转换为座位坐标系
				const seatX = (x - engine.state.offsetX) / engine.state.zoom;
				const seatY = (y - engine.state.offsetY) / engine.state.zoom;



				// 检查行标尺点击（左侧）
				if (seatX < 0 && seatX > -headerOffset - clickTolerance) {
					for (let row = 0; row < parseInt(seatRows); row++) {
						const headerY = row * (seatSize + spacing) + seatSize/2;
						if (Math.abs(seatY - headerY) < clickTolerance) {

							engine.selectRow(row);
							return true;
						}
					}
				}

				// 检查列标尺点击（顶部）
				if (seatY < 0 && seatY > -headerOffset - clickTolerance) {
					for (let col = 0; col < parseInt(seatCols); col++) {
						const headerX = col * (seatSize + spacing) + seatSize/2;
						if (Math.abs(seatX - headerX) < clickTolerance) {

							engine.selectColumn(col);
							return true;
						}
					}
				}

				return false;
			}



			// 从引擎更新座位数据
			function updateSeatsDataFromEngine(engine) {
				// 清空当前数据
				seatsData = {};

				// 从引擎获取选中的座位
				engine.seats.forEach(seat => {
					if (seat.status === 'selected') {
						const seatKey = seat.row + "_" + seat.col;
						seatsData[seatKey] = {
							row: seat.row,
							col: seat.col,
							price: 0,
							color: "",
							groupName: "",
							thre_id: 10,
							hall_id: 10,
							movie_id: 10
						};
					}
				});


			}

			// 修复Canvas尺寸，确保完全适应容器
			function fixCanvasSize(canvas, container) {
				// 详细调试信息
				const seatBoxElement = document.getElementById('seatBox');
				const seatBoxRect = seatBoxElement.getBoundingClientRect();
				const windowHeight = window.innerHeight;
				const seatBoxTop = seatBoxRect.top;
				const availableHeight = windowHeight - seatBoxTop;

				// 设置容器高度为剩余的全部空间
				seatBoxElement.style.height = availableHeight + 'px';

				// 获取更新后的容器尺寸
				const rect = container.getBoundingClientRect();
				const dpr = window.devicePixelRatio || 1;

				// 设置Canvas的实际尺寸（考虑设备像素比）
				canvas.width = rect.width * dpr;
				canvas.height = rect.height * dpr;

				// 设置Canvas的显示尺寸
				canvas.style.width = rect.width + 'px';
				canvas.style.height = rect.height + 'px';

				// 缩放绘图上下文以适应设备像素比
				const ctx = canvas.getContext('2d');
				ctx.scale(dpr, dpr);


			}

			// 居中显示座位
			function centerSeats(engine) {
				const config = engine.config;
				const container = document.querySelector("#seatBox");
				const rect = container.getBoundingClientRect();

				// 计算座位区域的总尺寸
				const totalSeatsWidth = parseInt(seatCols) * (config.seatSize + config.seatSpacing) - config.seatSpacing;
				const totalSeatsHeight = parseInt(seatRows) * (config.seatSize + config.seatSpacing) - config.seatSpacing;

				// 计算居中偏移量，留出标尺空间
				const headerSpace = 30;
				engine.state.offsetX = Math.max(headerSpace, (rect.width - totalSeatsWidth) / 2);
				engine.state.offsetY = Math.max(headerSpace, (rect.height - totalSeatsHeight) / 2);

				engine.render();
			}

			// 设置键盘事件
			function setupKeyboardEvents() {
				// 监听键盘按下
				document.addEventListener('keydown', (e) => {
					if (e.code === 'Space') {
						e.preventDefault(); // 防止页面滚动
						isSpacePressed = true;
						updateCursor();
					}
				});

				// 监听键盘抬起
				document.addEventListener('keyup', (e) => {
					if (e.code === 'Space') {
						isSpacePressed = false;
						updateCursor();
					}
				});
			}

			// 更新鼠标光标
			function updateCursor() {
				const canvas = engine?.canvas;
				if (canvas) {
					if (isSpacePressed) {
						canvas.style.cursor = 'grab';
					} else {
						canvas.style.cursor = 'crosshair';
					}
				}
			}

			// 创建兼容性接口
			function createCompatibilityInterface() {
				// 创建seats对象，兼容原有API
				window.seats = {
					getSeats: function() {
						return seatsData;
					}
				};

				// 兼容原有的$.seats()调用
				$.seats = function(options) {
					return window.seats;
				};
			}

			// 全局状态管理
			let currentTool = 'pan';
			// 移除模式切换，Step2专注于座位布局

			// 操作历史记录
			let operationHistory = [];
			const MAX_HISTORY = 10; // 最多保存10步操作

			// 双数据管理
			let seats = JSON.parse(localStorage.getItem("seats") || "{}"); // 座位基础数据（第二步）
			let seatColors = {}; // 当前选中的座位（第三步）

			// 分区管理
			let zones = JSON.parse(localStorage.getItem('zones')) || [];
			let zoneCounter = zones.length;

			// 工具栏功能
			function initToolbar() {
				// 移除模式切换，Step2专注于座位布局

				// 工具按钮点击事件
				document.querySelectorAll('.tool-btn[data-tool]').forEach(btn => {
					btn.addEventListener('click', function() {
						const tool = this.dataset.tool;
						setActiveTool(tool);
					});
				});

				// 添加Canvas鼠标事件处理（通过工具栏控制）
				setupCanvasEvents();

				// 视图控制按钮
				document.getElementById('zoomInBtn').addEventListener('click', () => {
					engine.zoomIn();
					updateZoomDisplay();
				});

				document.getElementById('zoomOutBtn').addEventListener('click', () => {
					engine.zoomOut();
					updateZoomDisplay();
				});

				document.getElementById('fitToScreenBtn').addEventListener('click', () => {
					fitToScreen();
					updateZoomDisplay();
				});

				document.getElementById('centerViewBtn').addEventListener('click', () => {
					centerSeats(engine);
					updateZoomDisplay();
				});

				// 撤销按钮
				document.getElementById('undoBtn').addEventListener('click', undoLastOperation);


				// 工具栏按钮已通过onclick直接绑定saveLayout函数

				// 初始化信息显示
				updateToolbarInfo();

				// 设置默认工具状态
				setActiveTool(currentTool);

				// 显示右侧面板（移除模式切换）
				document.getElementById('layoutRightPanel').style.display = 'block';
			}

			// 移除模式切换函数，Step2专注于座位布局

			// 设置活动工具
			function setActiveTool(tool) {
				currentTool = tool;

				// 更新按钮状态
				document.querySelectorAll('.tool-btn[data-tool]').forEach(btn => {
					btn.classList.remove('active');
				});
				document.querySelector(`[data-tool="${tool}"]`).classList.add('active');

				// 更新鼠标光标和行为
				const canvas = engine?.canvas;
				if (canvas) {
					switch(tool) {
						case 'select':
							canvas.style.cursor = 'crosshair';
							break;
						case 'boxselect-add':
							canvas.style.cursor = 'crosshair';
							break;
						case 'boxselect-remove':
							canvas.style.cursor = 'crosshair';
							break;
						case 'zone-select':
							canvas.style.cursor = 'crosshair';
							break;
						case 'pan':
							canvas.style.cursor = 'grab';
							break;
					}
				}
			}





			// 适应屏幕
			function fitToScreen() {
				if (!engine || !engine.seats.length) return;

				const canvas = engine.canvas;
				const padding = 50;

				// 计算座位区域
				const seatArea = {
					minX: 0,
					minY: 0,
					maxX: parseInt(seatCols) * (engine.config.seatSize + engine.config.seatSpacing),
					maxY: parseInt(seatRows) * (engine.config.seatSize + engine.config.seatSpacing)
				};

				// 计算缩放比例
				const scaleX = (canvas.width - padding * 2) / seatArea.maxX;
				const scaleY = (canvas.height - padding * 2) / seatArea.maxY;
				const scale = Math.min(scaleX, scaleY, 1); // 不超过100%

				// 设置缩放和居中
				engine.state.zoom = scale;
				engine.state.offsetX = (canvas.width - seatArea.maxX * scale) / 2;
				engine.state.offsetY = (canvas.height - seatArea.maxY * scale) / 2;

				engine.render();
			}

			// 更新缩放显示
			function updateZoomDisplay() {
				const zoomPercent = Math.round(engine.state.zoom * 100);
				document.getElementById('zoomLevel').textContent = zoomPercent + '%';
			}

			// 数据管理函数
			function clearZoneSelection() {
				seatColors = {};
				// 清除座位的选中状态，但保持分区颜色
				engine.seats.forEach(seat => {
					if (seat.status === 'selected') {
						seat.status = 'available';
					}
				});
			}



			function loadLayoutData() {
				// 加载座位布局数据到seatsData
				Object.keys(seats).forEach(seatKey => {
					const [row, col] = seatKey.split('_').map(Number);
					const seat = engine.seats.find(s => s.row === row && s.col === col);
					if (seat && seats[seatKey]) {
						const seatData = seats[seatKey];
						// 如果有分区信息，设置分区颜色
						if (seatData.color && seatData.groupName) {
							seat.groupColor = seatData.color;
							seat.groupName = seatData.groupName;
							seat.status = 'available';
						}
					}
				});
			}

			function loadZoneData() {
				// 加载已有座位数据，准备分区选择
				// 显示所有已创建的座位，但不选中任何座位
				Object.keys(seats).forEach(seatKey => {
					const [row, col] = seatKey.split('_').map(Number);
					const seat = engine.seats.find(s => s.row === row && s.col === col);
					if (seat && seats[seatKey]) {
						const seatData = seats[seatKey];
						if (seatData.color && seatData.groupName) {
							seat.groupColor = seatData.color;
							seat.groupName = seatData.groupName;
						}
						seat.status = 'available';
					}
				});
			}

			// 更新工具栏信息
			function updateToolbarInfo() {
				// 只显示座位布局信息
				const selectedCount = Object.keys(seatsData).length;
				const totalSeats = parseInt(seatRows) * parseInt(seatCols);

				document.getElementById('selectedCount').textContent = selectedCount;
				document.getElementById('totalSeats').textContent = totalSeats;

				if (engine) {
					updateZoomDisplay();
				}

				// 更新撤销按钮状态
				updateUndoButton();
			}

			// 保存操作到历史记录
			function saveOperationToHistory(operationType, description) {
				const operation = {
					type: operationType,
					description: description,
					timestamp: Date.now(),
					seatsDataSnapshot: JSON.parse(JSON.stringify(seatsData)) // 深拷贝
				};

				operationHistory.push(operation);

				// 限制历史记录数量
				if (operationHistory.length > MAX_HISTORY) {
					operationHistory.shift(); // 移除最旧的记录
				}

				updateUndoButton();

			}

			// 撤销上一步操作
			function undoLastOperation() {
				if (operationHistory.length === 0) {

					return;
				}

				// 获取上一步操作
				const lastOperation = operationHistory.pop();

				// 恢复座位数据
				seatsData = JSON.parse(JSON.stringify(lastOperation.seatsDataSnapshot));

				// 更新引擎中的座位状态
				engine.seats.forEach(seat => {
					const seatKey = seat.row + "_" + seat.col;
					if (seatsData[seatKey]) {
						seat.status = 'selected';
					} else {
						seat.status = 'available';
					}
				});

				// 重新渲染
				engine.render();
				updateToolbarInfo();


			}

			// 更新撤销按钮状态
			function updateUndoButton() {
				const undoBtn = document.getElementById('undoBtn');
				if (operationHistory.length > 0) {
					undoBtn.disabled = false;
					undoBtn.title = `撤销: ${operationHistory[operationHistory.length - 1].description}`;
				} else {
					undoBtn.disabled = true;
					undoBtn.title = '没有可撤销的操作';
				}
			}

			// 设置Canvas事件处理（通过工具栏控制）
			function setupCanvasEvents() {
				const canvas = engine.canvas;
				let isDragging = false;
				let isSelecting = false;
				let lastMouseX = 0;
				let lastMouseY = 0;
				let selectionStart = { x: 0, y: 0 };

				// 鼠标按下
				canvas.addEventListener('mousedown', function(e) {
					// 检查是否点击了标尺（保留原有功能）
					const rect = canvas.getBoundingClientRect();
					const x = e.clientX - rect.left;
					const y = e.clientY - rect.top;

					const headerClicked = checkHeaderClick(engine, x, y);
					if (headerClicked) {
						e.preventDefault();
						e.stopPropagation();
						return false;
					}

					// 根据当前工具执行不同操作
					switch(currentTool) {
						case 'select':
							handleSelectTool(e);
							break;
						case 'boxselect-add':
							handleBoxSelectStart(e, 'remove'); // 对调：框选-功能（移除）
							break;
						case 'boxselect-remove':
							handleBoxSelectStart(e, 'add'); // 对调：框选+功能（添加）
							break;
						case 'zone-select':
							handleZoneSelectStart(e);
							break;
						case 'pan':
							handlePanStart(e);
							break;
					}
				});

				// 鼠标移动
				canvas.addEventListener('mousemove', function(e) {
					if (!isDragging) return;

					switch(currentTool) {
						case 'boxselect-add':
						case 'boxselect-remove':
							if (isSelecting) {
								handleBoxSelectMove(e);
							}
							break;
						case 'zone-select':
							if (isSelecting) {
								handleZoneSelectMove(e);
							}
							break;
						case 'pan':
							handlePanMove(e);
							break;
					}
				});

				// 鼠标抬起
				canvas.addEventListener('mouseup', function(e) {
					if ((currentTool === 'boxselect-add' || currentTool === 'boxselect-remove') && isSelecting) {
						handleBoxSelectEnd(e);
					} else if (currentTool === 'zone-select' && isSelecting) {
						handleZoneSelectEnd(e);
					}

					isDragging = false;
					isSelecting = false;
				});

				// 选择工具处理
				function handleSelectTool(e) {
					const rect = canvas.getBoundingClientRect();
					const x = e.clientX - rect.left;
					const y = e.clientY - rect.top;

					// 转换为座位坐标系
					const seatX = (x - engine.state.offsetX) / engine.state.zoom;
					const seatY = (y - engine.state.offsetY) / engine.state.zoom;

					// 查找点击的座位
					const clickedSeat = engine.seats.find(seat =>
						seatX >= seat.x && seatX <= seat.x + seat.width &&
						seatY >= seat.y && seatY <= seat.y + seat.height
					);

					if (clickedSeat) {
						handleSeatClick(clickedSeat);
					}
				}

				// 框选开始
				function handleBoxSelectStart(e, mode) {
					const rect = canvas.getBoundingClientRect();
					selectionStart.x = e.clientX - rect.left;
					selectionStart.y = e.clientY - rect.top;
					selectionStart.mode = mode; // 'add' 或 'remove'

					isSelecting = true;
					isDragging = true;

					engine.state.isSelecting = true;
					engine.state.selectionStartX = selectionStart.x;
					engine.state.selectionStartY = selectionStart.y;
					engine.state.selectionEndX = selectionStart.x;
					engine.state.selectionEndY = selectionStart.y;
					engine.state.selectionMode = mode;
				}

				// 框选移动
				function handleBoxSelectMove(e) {
					const rect = canvas.getBoundingClientRect();
					const x = e.clientX - rect.left;
					const y = e.clientY - rect.top;

					engine.state.selectionEndX = x;
					engine.state.selectionEndY = y;
					engine.render();
				}

				// 框选结束
				function handleBoxSelectEnd(e) {
					// 获取选择框内的座位
					const selectedSeats = getSeatsInSelection();
					const mode = selectionStart.mode;

					// 保存操作前状态（功能已对调）
					const modeText = mode === 'add' ? '框选+' : '框选-';
					saveOperationToHistory('box-select', `${modeText} ${selectedSeats.length}个座位`);

					selectedSeats.forEach(seat => {
						const seatKey = seat.row + "_" + seat.col;

						if (mode === 'add') {
							// 添加选择：恢复座位（设为可见状态）
							if (!seatsData[seatKey]) {
								seatsData[seatKey] = {
									row: seat.row,
									col: seat.col,
									price: 0,
									color: "",
									groupName: "",
									thre_id: 10,
									hall_id: 10,
									movie_id: 10
								};
								seat.status = 'available';  // 恢复为可见的灰色状态
							}
						} else if (mode === 'remove') {
							// 移除选择：移除座位（设为不可见状态）
							if (seatsData[seatKey]) {
								delete seatsData[seatKey];
								seat.status = 'empty';  // 设为空状态，不可见
							}
						}
					});

					engine.state.isSelecting = false;
					engine.render();
					updateToolbarInfo();
				}

				// 拖拽开始
				function handlePanStart(e) {
					const rect = canvas.getBoundingClientRect();
					lastMouseX = e.clientX - rect.left;
					lastMouseY = e.clientY - rect.top;
					isDragging = true;
				}

				// 拖拽移动
				function handlePanMove(e) {
					const rect = canvas.getBoundingClientRect();
					const x = e.clientX - rect.left;
					const y = e.clientY - rect.top;

					const deltaX = x - lastMouseX;
					const deltaY = y - lastMouseY;

					engine.state.offsetX += deltaX;
					engine.state.offsetY += deltaY;

					lastMouseX = x;
					lastMouseY = y;

					engine.render();
				}

				// 获取选择框内的座位
				function getSeatsInSelection() {
					const startX = Math.min(engine.state.selectionStartX, engine.state.selectionEndX);
					const startY = Math.min(engine.state.selectionStartY, engine.state.selectionEndY);
					const endX = Math.max(engine.state.selectionStartX, engine.state.selectionEndX);
					const endY = Math.max(engine.state.selectionStartY, engine.state.selectionEndY);

					// 转换为座位坐标系
					const seatStartX = (startX - engine.state.offsetX) / engine.state.zoom;
					const seatStartY = (startY - engine.state.offsetY) / engine.state.zoom;
					const seatEndX = (endX - engine.state.offsetX) / engine.state.zoom;
					const seatEndY = (endY - engine.state.offsetY) / engine.state.zoom;

					return engine.seats.filter(seat => {
						const seatCenterX = seat.x + seat.width / 2;
						const seatCenterY = seat.y + seat.height / 2;

						return seatCenterX >= seatStartX && seatCenterX <= seatEndX &&
							   seatCenterY >= seatStartY && seatCenterY <= seatEndY;
					});
				}

				// 分区选择工具处理函数
				function handleZoneSelectStart(e) {
					const rect = canvas.getBoundingClientRect();
					selectionStart.x = e.clientX - rect.left;
					selectionStart.y = e.clientY - rect.top;
					selectionStart.mode = 'zone'; // 分区选择模式

					isSelecting = true;
					isDragging = true;

					engine.state.isSelecting = true;
					engine.state.selectionStartX = selectionStart.x;
					engine.state.selectionStartY = selectionStart.y;
					engine.state.selectionEndX = selectionStart.x;
					engine.state.selectionEndY = selectionStart.y;
					engine.state.selectionMode = 'zone';
				}

				function handleZoneSelectMove(e) {
					const rect = canvas.getBoundingClientRect();
					const x = e.clientX - rect.left;
					const y = e.clientY - rect.top;

					engine.state.selectionEndX = x;
					engine.state.selectionEndY = y;
					engine.render();
				}

				function handleZoneSelectEnd(e) {
					// 获取选择框内的所有座位
					const allSeatsInSelection = getSeatsInSelection();

					// 过滤出已存在的座位（从seats数据判断）
					const existingSeats = allSeatsInSelection.filter(seat => {
						const seatKey = seat.row + "_" + seat.col;
						return seats[seatKey]; // 只选择seats中已存在的座位
					});

					if (existingSeats.length === 0) {
						engine.state.isSelecting = false;
						engine.render();
						alert('框选区域内没有已存在的座位');
						return;
					}

					// 保存操作前状态
					saveOperationToHistory('zone-select', `选择 ${existingSeats.length}个座位用于分区`);

					// 将已存在的座位添加到分区选择中（使用seatColors）
					existingSeats.forEach(seat => {
						const seatKey = seat.row + "_" + seat.col;
						if (!seatColors[seatKey]) {
							seatColors[seatKey] = 1; // 标记为选中
							seat.status = 'selected'; // 显示为蓝色选中状态
						}
					});

					// 清除选择框，但保持座位的选中状态
					engine.state.isSelecting = false;
					engine.render();
					updateToolbarInfo();


				}
			}

			// 右侧面板功能实现
			function saveLayout() {
				// 保存座位布局到localStorage
				const layoutData = {};

				// 收集所有已创建的座位
				Object.keys(seatsData).forEach(seatKey => {
					const seatData = seatsData[seatKey];
					layoutData[seatKey] = {
						row: seatData.row,
						col: seatData.col,
						price: seatData.price || 0,
						color: seatData.color || "",
						groupName: seatData.groupName || "",
						thre_id: 10,
						hall_id: 10,
						movie_id: 10
					};
				});

				// 保存到localStorage
				localStorage.setItem("seats", JSON.stringify(layoutData));

				// 更新全局seats数据
				seats = layoutData;



				// 显示保存成功信息并询问是否进入下一步
				const seatCount = Object.keys(layoutData).length;
				const totalPositions = parseInt(seatRows) * parseInt(seatCols);
				const removedCount = totalPositions - seatCount;

				const message = `✅ 座位布局已保存！\n\n📊 统计信息：\n• 总位置：${totalPositions} 个\n• 保留座位：${seatCount} 个\n• 移除位置：${removedCount} 个\n• 座位利用率：${Math.round(seatCount/totalPositions*100)}%\n\n是否进入第三步进行分区设置？`;

				if (confirm(message)) {
					// 跳转到Step3
					window.location.href = 'step3.html';
				}
			}

			function saveZoneSettings() {
				// 保存所有分区设置
				const allZoneData = {};

				// 合并seats基础数据和当前分区数据
				Object.keys(seats).forEach(seatKey => {
					allZoneData[seatKey] = { ...seats[seatKey] };
				});

				// 应用分区信息到对应座位
				zones.forEach(zone => {
					zone.seats.forEach(seatKey => {
						if (allZoneData[seatKey]) {
							allZoneData[seatKey].color = zone.color;
							allZoneData[seatKey].groupName = zone.name;
							allZoneData[seatKey].price = zone.price || 0;
						}
					});
				});

				// 保存到localStorage
				localStorage.setItem("seats", JSON.stringify(allZoneData));
				localStorage.setItem("zones", JSON.stringify(zones));

				alert(`分区设置已保存！共保存 ${zones.length} 个分区。`);

			}

			// 分区功能实现
			function showZoneForm() {
				// 统计当前选中的座位数量（蓝色选中状态的座位）
				const selectedSeats = engine.seats.filter(seat => seat.status === 'selected');
				const selectedCount = selectedSeats.length;

				if (selectedCount === 0) {
					alert('请先选择座位再创建分区');
					return;
				}

				document.getElementById('zoneForm').style.display = 'block';
				document.getElementById('createZoneBtn').style.display = 'none';
				document.getElementById('zoneName').value = `a${zones.length + 1}`;
				document.getElementById('zoneSelectedCount').textContent = selectedCount;
			}

			function hideZoneForm() {
				document.getElementById('zoneForm').style.display = 'none';
				document.getElementById('createZoneBtn').style.display = 'block';
			}

			function saveZone() {
				const zoneName = document.getElementById('zoneName').value.trim();
				const zoneColor = document.getElementById('zoneColor').value;
				const selectedSeats = Object.keys(seatsData);

				if (!zoneName) {
					alert('请输入分区名称');
					return;
				}

				if (selectedSeats.length === 0) {
					alert('请选择座位');
					return;
				}

				// 保存操作前状态
				saveOperationToHistory('create-zone', `创建分区: ${zoneName}`);

				// 创建新分区
				const newZone = {
					id: Date.now(),
					name: zoneName,
					color: zoneColor,
					seats: [...selectedSeats],
					seatCount: selectedSeats.length
				};

				zones.push(newZone);
				zoneCounter++;

				// 更新座位颜色
				selectedSeats.forEach(seatKey => {
					const [row, col] = seatKey.split('_').map(Number);
					const seat = engine.seats.find(s => s.row === row && s.col === col);
					if (seat) {
						seat.groupColor = zoneColor;
						seatsData[seatKey].color = zoneColor;
						seatsData[seatKey].groupName = zoneName;
					}
				});

				// 保存到localStorage
				localStorage.setItem('zones', JSON.stringify(zones));
				localStorage.setItem("seats", JSON.stringify(seatsData));

				// 重新渲染
				engine.render();
				updateZoneList();
				hideZoneForm();
				updateToolbarInfo();


			}

			function updateZoneList() {
				const zoneList = document.getElementById('zoneList');
				zoneList.innerHTML = '';

				zones.forEach(zone => {
					const zoneItem = document.createElement('div');
					zoneItem.className = 'zone-item';
					zoneItem.innerHTML = `
						<div class="zone-color-dot" style="background-color: ${zone.color}"></div>
						<div class="zone-info">
							<div class="zone-name">${zone.name}</div>
							<div class="zone-count">${zone.seatCount}个座位</div>
						</div>
						<div class="zone-actions">
							<button class="zone-action-btn zone-edit-btn" onclick="editZone(${zone.id})" title="编辑">
								<i class="layui-icon layui-icon-edit"></i>
							</button>
							<button class="zone-action-btn zone-delete-btn" onclick="deleteZone(${zone.id})" title="删除">
								<i class="layui-icon layui-icon-delete"></i>
							</button>
						</div>
					`;
					zoneList.appendChild(zoneItem);
				});
			}

			function editZone(zoneId) {
				const zone = zones.find(z => z.id === zoneId);
				if (!zone) return;

				const newName = prompt('请输入新的分区名称:', zone.name);
				if (newName && newName.trim()) {
					// 保存操作前状态
					saveOperationToHistory('edit-zone', `编辑分区: ${zone.name} -> ${newName.trim()}`);

					zone.name = newName.trim();

					// 更新座位数据
					zone.seats.forEach(seatKey => {
						if (seatsData[seatKey]) {
							seatsData[seatKey].groupName = zone.name;
						}
					});

					localStorage.setItem('zones', JSON.stringify(zones));
					localStorage.setItem("seats", JSON.stringify(seatsData));
					updateZoneList();
				}
			}

			function deleteZone(zoneId) {
				if (!confirm('确定要删除这个分区吗？')) return;

				const zoneIndex = zones.findIndex(z => z.id === zoneId);
				if (zoneIndex === -1) return;

				const zone = zones[zoneIndex];

				// 保存操作前状态
				saveOperationToHistory('delete-zone', `删除分区: ${zone.name}`);

				// 清除座位的分区信息
				zone.seats.forEach(seatKey => {
					const [row, col] = seatKey.split('_').map(Number);
					const seat = engine.seats.find(s => s.row === row && s.col === col);
					if (seat) {
						seat.groupColor = null;
					}
					if (seatsData[seatKey]) {
						seatsData[seatKey].color = "";
						seatsData[seatKey].groupName = "";
					}
				});

				// 删除分区
				zones.splice(zoneIndex, 1);

				localStorage.setItem('zones', JSON.stringify(zones));
				localStorage.setItem("seats", JSON.stringify(seatsData));

				engine.render();
				updateZoneList();
				updateToolbarInfo();
			}

			// 保存按钮事件（现在包含分区数据）
			$('#saveTop').on('click', function() {

				localStorage.setItem("seats", JSON.stringify(seatsData));
				localStorage.setItem("zones", JSON.stringify(zones));
				localStorage.removeItem("groupArrayObject")
				localStorage.removeItem("selected")
				localStorage.removeItem("channelSelectArrayStorage")

				// 由于已经合并了第三步功能，直接跳转到第四步
				alert('座位布局和分区设置已保存！');
				// location.href = "step4.html"; // 如果有第四步的话
			});

		</script>


	</body>
</html>