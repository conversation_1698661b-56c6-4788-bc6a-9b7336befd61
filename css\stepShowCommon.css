	#mySeat {
		display: none;
	}

	.zoomBox {
		/* display: none; */
	}

	.seatBoxContent {
		margin: 0 !important;
		height: auto !important;
	}

	.main-left {
		padding: 0 !important;
	}

	body .seatBox {
		border: 0 !important;
	}

	body {
		overflow-y: auto;
	}

	.main-content {
		height: auto !important;
	}

	.backBox {
		position: fixed;
		width: 40px;
		height: 40px;
		background: #4078cb;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #fff;
		font-size: 13px;
		cursor: pointer;
		top: 10px;
		left: 10px;
		z-index: 111;
	}