<!DOCTYPE html>
<html lang="zh-CN">
	<head>
		<meta charset="UTF-8">
		<title>第三步：设置分区</title>
		<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="renderer" content="webkit">
		<link rel="stylesheet" type="text/css" href="css/reset.css">
		<link rel="stylesheet" href="css/translateCommon.css">
		<link rel="stylesheet" href="layui/css/layui.css" />
		<link rel="stylesheet" href="css/modalbox.css" />
		<link rel="stylesheet" href="css/stepPlug.css" />
		<link rel="stylesheet" href="css/seat.css" />

		<!-- 左侧工具栏样式 -->
		<style>
			/* 主布局 */
			.main-content {
				display: flex !important;
				height: calc(100vh - 120px) !important;
				overflow: hidden !important;
			}

			/* 左侧工具栏 */
			.sidebar-toolbar {
				width: 250px;
				background: white;
				border-right: 1px solid #e6e6e6;
				overflow-y: auto;
				padding: 20px 15px;
				box-sizing: border-box;
			}

			.toolbar-section {
				margin-bottom: 25px;
				background: #f8f9fa;
				border-radius: 8px;
				padding: 15px;
			}

			.section-title {
				font-size: 14px;
				font-weight: bold;
				color: #333;
				margin-bottom: 12px;
				padding-bottom: 8px;
				border-bottom: 1px solid #e6e6e6;
			}

			.btn-grid {
				display: grid;
				grid-template-columns: 1fr 1fr;
				gap: 8px;
			}

			.btn-grid-single {
				display: grid;
				grid-template-columns: 1fr;
				gap: 8px;
			}

			.sidebar-btn {
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 6px;
				padding: 8px 12px;
				border: 1px solid #d9d9d9;
				background: white;
				border-radius: 4px;
				cursor: pointer;
				transition: all 0.2s;
				font-size: 12px;
				min-height: 36px;
				text-decoration: none;
				color: #333;
			}

			.sidebar-btn:hover {
				border-color: #40a9ff;
				color: #40a9ff;
			}

			.sidebar-btn.active {
				background: #1890ff;
				border-color: #1890ff;
				color: white;
			}

			.sidebar-btn.primary {
				background: #52c41a;
				border-color: #52c41a;
				color: white;
			}

			.sidebar-btn.primary:hover {
				background: #73d13d;
				border-color: #73d13d;
			}

			.sidebar-btn:disabled {
				opacity: 0.5;
				cursor: not-allowed;
			}

			.sidebar-btn i {
				font-size: 14px;
			}

			.tool-btn.active {
				background: #1890ff;
				border-color: #1890ff;
				color: white;
			}

			/* 座位画布区域 */
			.seat-canvas-container {
				flex: 1;
				position: relative;
				background: #fafafa;
				overflow: hidden;
			}

			#seatBox {
				width: 100%;
				height: 100%;
				position: relative;
				overflow: hidden;
			}

			#seatCanvas {
				display: block;
				cursor: grab;
			}

			#seatCanvas:active {
				cursor: grabbing;
			}

			/* 右侧面板 */
			.main-right {
				width: 300px;
				background: white;
				border-left: 1px solid #e6e6e6;
				overflow-y: auto;
			}

			.container {
				padding: 20px;
			}

			.layui-tab {
				margin: 0;
			}

			.layui-tab-title {
				border-bottom: 1px solid #e6e6e6;
				margin-bottom: 20px;
			}

			.layui-tab-title li {
				padding: 10px 20px;
			}

			.info-section {
				margin-bottom: 20px;
			}

			.info-section h4 {
				margin-bottom: 10px;
				color: #333;
			}

			.info-section ul {
				padding-left: 20px;
			}

			.info-section li {
				margin-bottom: 5px;
				line-height: 1.5;
			}

			.bottom-btn {
				margin-top: 20px;
				text-align: center;
			}

			/* 分区相关样式 */
			.zone-form {
				background: #f8f9fa;
				padding: 15px;
				border-radius: 6px;
				margin-bottom: 15px;
			}

			.form-group {
				margin-bottom: 12px;
			}

			.form-group label {
				display: block;
				margin-bottom: 5px;
				font-weight: bold;
				color: #333;
			}

			.form-group input, .form-group select {
				width: 100%;
				padding: 8px 12px;
				border: 1px solid #d9d9d9;
				border-radius: 4px;
				font-size: 14px;
			}

			.zone-list {
				max-height: 300px;
				overflow-y: auto;
			}

			.zone-item {
				background: white;
				border: 1px solid #e6e6e6;
				border-radius: 6px;
				padding: 12px;
				margin-bottom: 10px;
			}

			.zone-item h5 {
				margin: 0 0 8px 0;
				font-weight: bold;
			}

			.zone-info {
				font-size: 12px;
				color: #666;
				display: flex;
				justify-content: space-between;
				align-items: center;
			}

			.zone-color {
				width: 20px;
				height: 20px;
				border-radius: 3px;
				border: 1px solid #ddd;
			}
		</style>
	</head>
	<body>
		<div id="section">
			<!-- header -->
			<div id="header" style="background: #001529; color: white; padding: 20px; text-align: center;">
				<h1 style="margin: 0; color: white;">Step3 - 座位分区设置</h1>
			</div>

			<!-- 主要模块 -->
			<div class="c_right">
				<div class="main">
					<div class="new-content-top">
						<span class="layui-breadcrumb" lay-separator=">">
							<a href="">首页</a>
							<a href="">活动列表</a>
							<a href="">座位规划</a>
							<a><cite>设置分区</cite></a>
						</span>
						<div class="content-top-btn">
							<button class="layui-btn" type="button" onclick="goToStep2()">上一步</button>
							<button class="layui-btn layui-btn-normal" type="button" onclick="saveZones()">保存分区</button>
							<button class="layui-btn layui-btn-disabled" type="button" onclick="">进入座位分配</button>
						</div>

					<div class="main-content" style="overflow: hidden !important; display: flex;">
						<!-- 左侧工具栏 -->
						<div class="sidebar-toolbar" id="sidebarToolbar">
							<!-- 导航 & 进度 -->
							<div class="toolbar-section">
								<div class="section-title">导航 (步骤3/3)</div>
								<div class="btn-grid">
									<button type="button" class="sidebar-btn primary" onclick="goToStep2()">
										<i class="layui-icon layui-icon-left"></i>
										<span>上一步</span>
									</button>
									<button type="button" class="sidebar-btn primary" id="saveTop" onclick="saveZones()">
										<i class="layui-icon layui-icon-ok"></i>
										<span>保存分区设置</span>
									</button>
								</div>
							</div>

							<!-- 工具 -->
							<div class="toolbar-section">
								<div class="section-title">工具</div>
								<div class="btn-grid">
									<button type="button" class="sidebar-btn tool-btn active" id="selectTool" data-tool="select">
										<i class="layui-icon layui-icon-ok-circle"></i>
										<span>选择座位</span>
									</button>
									<button type="button" class="sidebar-btn tool-btn" id="boxSelectTool" data-tool="boxselect">
										<i class="layui-icon layui-icon-screen-full"></i>
										<span>框选座位</span>
									</button>
								</div>
								<div class="btn-grid">
									<button type="button" class="sidebar-btn tool-btn" id="panTool" data-tool="pan">
										<i class="layui-icon layui-icon-screen-restore"></i>
										<span>拖拽</span>
									</button>
									<button type="button" class="sidebar-btn" id="createZoneBtn">
										<i class="layui-icon layui-icon-add-1"></i>
										<span>创建分区</span>
									</button>
								</div>
							</div>

							<!-- 操作 -->
							<div class="toolbar-section">
								<div class="section-title">操作</div>
								<div class="btn-grid">
									<button type="button" class="sidebar-btn" id="selectAllBtn">
										<i class="layui-icon layui-icon-ok"></i>
										<span>全选</span>
									</button>
									<button type="button" class="sidebar-btn" id="clearSelectionBtn">
										<i class="layui-icon layui-icon-close"></i>
										<span>清除选择</span>
									</button>
								</div>
								<div class="btn-grid">
									<button type="button" class="sidebar-btn" id="invertSelectionBtn">
										<i class="layui-icon layui-icon-refresh"></i>
										<span>反选</span>
									</button>
									<button type="button" class="sidebar-btn" id="undoBtn" disabled>
										<i class="layui-icon layui-icon-return"></i>
										<span>撤销</span>
									</button>
								</div>
							</div>
						</div>

						<!-- 座位画布区域 -->
						<div class="seat-canvas-container">
							<div id="seatBox">
								<canvas id="seatCanvas"></canvas>
							</div>
						</div>

						<div class="main-right" id="layoutRightPanel">
							<div class="container">
								<div class="layui-tab" lay-filter="tab-hash">
									<ul class="layui-tab-title">
										<li class="layui-this" lay-id="11">分区设置</li>
									</ul>

									<div class="layui-tab-content">
										<div class="layui-tab-item layui-show">
											<!-- 使用说明 -->
											<div class="info-section">
												<h4>📋 操作说明</h4>
												<ul>
													<li>🎯 <strong>选择座位</strong>：点击或框选要分区的座位</li>
													<li>🎨 <strong>创建分区</strong>：选择座位后填写分区信息</li>
													<li>💾 <strong>保存设置</strong>：完成所有分区后保存</li>
												</ul>
											</div>

											<!-- 当前选择信息 -->
											<div class="info-section">
												<h4>📊 当前选择</h4>
												<div id="selectionInfo">
													<p>已选择座位：<span id="selectedCount">0</span> 个</p>
													<p>总座位数：<span id="totalSeats">0</span> 个</p>
												</div>
											</div>

											<!-- 分区表单 -->
											<div class="zone-form" id="zoneForm" style="display: none;">
												<h4>🎨 创建新分区</h4>
												<div class="form-group">
													<label>分区名称</label>
													<input type="text" id="zoneName" placeholder="例如：VIP区" />
												</div>
												<div class="form-group">
													<label>分区价格</label>
													<input type="number" id="zonePrice" placeholder="例如：100" />
												</div>
												<div class="form-group">
													<label>分区颜色</label>
													<input type="color" id="zoneColor" value="#1890ff" />
												</div>
												<div class="form-group">
													<button class="layui-btn layui-btn-sm" onclick="createZone()">创建分区</button>
													<button class="layui-btn layui-btn-primary layui-btn-sm" onclick="cancelZone()">取消</button>
												</div>
											</div>

											<!-- 分区列表 -->
											<div class="info-section">
												<h4>📝 已创建分区</h4>
												<div class="zone-list" id="zoneList">
													<p style="color: #999; text-align: center;">暂无分区</p>
												</div>
											</div>

											<!-- 底部按钮 -->
											<div class="bottom-btn">
												<button class="layui-btn layui-btn-normal" type="button" id="createZoneBtn">
													🎨 创建新分区
												</button>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- JavaScript -->
	<script src="layui/layui.js"></script>
	<script src="js/jquery-3.6.3.min.js"></script>
	<script src="SeatMapEngine.js"></script>
	<script>
		// 全局变量
		let engine;
		let seatsData = {};
		let selectedSeats = new Set();
		let zones = [];
		let currentTool = 'select';
		let operationHistory = [];

		// 从localStorage获取基础数据
		const seatRows = localStorage.getItem("seatRows");
		const seatCols = localStorage.getItem("seatCols");

		// 检查基础数据
		if (!seatRows || !seatCols || seatRows === 'null' || seatCols === 'null') {
			// 如果没有数据，设置测试数据
			console.log("没有找到基础数据，使用测试数据");
			localStorage.setItem("seatRows", "10");
			localStorage.setItem("seatCols", "10");

			// 创建测试座位数据
			const testSeats = {};
			for (let row = 0; row < 10; row++) {
				for (let col = 0; col < 10; col++) {
					testSeats[row + "_" + col] = {
						row: row,
						col: col,
						price: 0
					};
				}
			}
			localStorage.setItem("seats", JSON.stringify(testSeats));

			// 重新加载页面
			window.location.reload();
		}

		// 加载座位数据
		const savedSeats = localStorage.getItem("seats");
		if (!savedSeats) {
			alert("没有找到座位布局数据，请先完成第二步！");
			// location.href = "step2.html";

			// 临时：创建测试数据
			const testSeats = {};
			for (let row = 0; row < parseInt(seatRows); row++) {
				for (let col = 0; col < parseInt(seatCols); col++) {
					testSeats[row + "_" + col] = {
						row: row,
						col: col,
						price: 0
					};
				}
			}
			seatsData = testSeats;
		} else {
			seatsData = JSON.parse(savedSeats);
		}

		$(document).ready(function() {
			// 初始化Canvas座位系统
			const canvas = document.getElementById('seatCanvas');
			const container = document.getElementById('seatBox');

			// 设置Canvas样式，确保完全适应容器
			canvas.style.width = '100%';
			canvas.style.height = '100%';
			canvas.style.display = 'block';
			canvas.style.position = 'absolute';
			canvas.style.top = '0';
			canvas.style.left = '0';

			engine = new SeatMapEngine(canvas, {
				seatSize: 20,
				seatSpacing: 4,
				showSeatNumbers: true,
				enableRoundedCorners: true,
				showHeaders: true,
				onSeatClick: handleSeatClick,
				onSeatHover: handleSeatHover,
				onViewChange: handleViewChange
			});

			// 确保Canvas完全适应容器
			fixCanvasSize(canvas, container);

			// 监听窗口大小变化
			window.addEventListener('resize', () => {
				fixCanvasSize(canvas, container);
				centerSeats(engine);
			});

			// 生成座位
			engine.generateSeats(parseInt(seatRows), parseInt(seatCols));

			// 居中显示座位
			centerSeats(engine);

			// 设置座位数据和已有分区
			loadExistingZones();
			engine.seats.forEach(seat => {
				const seatKey = seat.row + "_" + seat.col;
				if (seatsData[seatKey]) {
					seat.status = 'available';
					// 如果座位已有分区信息，设置颜色
					if (seatsData[seatKey].color) {
						seat.groupColor = seatsData[seatKey].color;
						seat.groupName = seatsData[seatKey].zoneName;
					}
				} else {
					seat.status = 'empty';
				}
			});

			// 设置点击处理
			engine.onSeatClick = handleSeatClick;
			engine.onBoxSelect = handleBoxSelect;

			// 初始化界面
			updateSelectionInfo();
			updateZoneList();
			setupEventListeners();

			// 渲染
			engine.render();
		});

		// 高度自适应函数
		function adjustCanvasHeight() {
			const seatBoxElement = document.getElementById('seatBox');
			if (!seatBoxElement) return;

			// 简单设置固定高度，确保画布可见
			seatBoxElement.style.height = '600px';
			seatBoxElement.style.minHeight = '400px';
		}

		// 加载已有分区数据
		function loadExistingZones() {
			const savedZones = localStorage.getItem('zones');
			if (savedZones) {
				zones = JSON.parse(savedZones);
			}
		}

		// 座位点击处理
		function handleSeatClick(seat) {
			if (currentTool !== 'select') return;

			const seatKey = seat.row + "_" + seat.col;
			if (!seatsData[seatKey]) return; // 只能选择存在的座位

			// 保存操作历史
			saveOperationToHistory('seat-click', `点击座位 ${seat.row + 1}-${seat.col + 1}`);

			if (selectedSeats.has(seatKey)) {
				selectedSeats.delete(seatKey);
				// 恢复原有颜色或设为available
				if (seatsData[seatKey].color) {
					seat.groupColor = seatsData[seatKey].color;
					seat.status = 'available';
				} else {
					seat.groupColor = null;
					seat.status = 'available';
				}
			} else {
				selectedSeats.add(seatKey);
				seat.status = 'selected';
			}

			engine.render();
			updateSelectionInfo();
		}

		// 框选处理
		function handleBoxSelect(seats) {
			if (currentTool !== 'boxselect') return;

			// 保存操作历史
			saveOperationToHistory('box-select', `框选 ${seats.length} 个座位`);

			seats.forEach(seat => {
				const seatKey = seat.row + "_" + seat.col;
				if (!seatsData[seatKey]) return; // 只能选择存在的座位

				if (selectedSeats.has(seatKey)) {
					selectedSeats.delete(seatKey);
					// 恢复原有颜色或设为available
					if (seatsData[seatKey].color) {
						seat.groupColor = seatsData[seatKey].color;
						seat.status = 'available';
					} else {
						seat.groupColor = null;
						seat.status = 'available';
					}
				} else {
					selectedSeats.add(seatKey);
					seat.status = 'selected';
				}
			});

			engine.render();
			updateSelectionInfo();
		}

		// 更新选择信息
		function updateSelectionInfo() {
			const totalSeats = Object.keys(seatsData).length;
			const selectedCount = selectedSeats.size;

			document.getElementById('selectedCount').textContent = selectedCount;
			document.getElementById('totalSeats').textContent = totalSeats;
		}

		// 设置事件监听器
		function setupEventListeners() {
			// 工具切换
			document.querySelectorAll('.tool-btn').forEach(btn => {
				btn.addEventListener('click', function() {
					// 移除所有active状态
					document.querySelectorAll('.tool-btn').forEach(b => b.classList.remove('active'));
					// 添加当前按钮的active状态
					this.classList.add('active');

					// 设置当前工具
					currentTool = this.dataset.tool;

					// 根据工具类型设置引擎状态
					switch(currentTool) {
						case 'select':
							engine.disablePan();
							engine.setTool('select');
							break;
						case 'boxselect':
							engine.disablePan();
							engine.setTool('boxselect');
							break;
						case 'pan':
							engine.enablePan();
							break;
					}
				});
			});

			// 操作按钮
			document.getElementById('selectAllBtn').addEventListener('click', selectAllSeats);
			document.getElementById('clearSelectionBtn').addEventListener('click', clearSelection);
			document.getElementById('invertSelectionBtn').addEventListener('click', invertSelection);
			document.getElementById('createZoneBtn').addEventListener('click', showZoneForm);
			document.getElementById('undoBtn').addEventListener('click', undoLastOperation);

			// 更新撤销按钮状态
			updateUndoButton();
		}

		// 更新撤销按钮状态
		function updateUndoButton() {
			const undoBtn = document.getElementById('undoBtn');
			if (operationHistory.length > 0) {
				undoBtn.disabled = false;
				undoBtn.classList.remove('disabled');
			} else {
				undoBtn.disabled = true;
				undoBtn.classList.add('disabled');
			}
		}

		// 全选座位
		function selectAllSeats() {
			// 保存操作历史
			saveOperationToHistory('select-all', '全选座位');

			selectedSeats.clear();
			engine.seats.forEach(seat => {
				const seatKey = seat.row + "_" + seat.col;
				if (seatsData[seatKey]) {
					selectedSeats.add(seatKey);
					seat.status = 'selected';
				}
			});
			engine.render();
			updateSelectionInfo();
		}

		// 清除选择
		function clearSelection() {
			// 保存操作历史
			saveOperationToHistory('clear-selection', '清除选择');

			selectedSeats.clear();
			engine.seats.forEach(seat => {
				const seatKey = seat.row + "_" + seat.col;
				if (seatsData[seatKey]) {
					// 恢复原有颜色或设为available
					if (seatsData[seatKey].color) {
						seat.groupColor = seatsData[seatKey].color;
						seat.groupName = seatsData[seatKey].zoneName;
						seat.status = 'available';
					} else {
						seat.groupColor = null;
						seat.groupName = null;
						seat.status = 'available';
					}
				}
			});
			engine.render();
			updateSelectionInfo();
		}

		// 反选
		function invertSelection() {
			// 保存操作历史
			saveOperationToHistory('invert-selection', '反选座位');

			const newSelection = new Set();
			engine.seats.forEach(seat => {
				const seatKey = seat.row + "_" + seat.col;
				if (seatsData[seatKey]) {
					if (selectedSeats.has(seatKey)) {
						// 恢复原有颜色或设为available
						if (seatsData[seatKey].color) {
							seat.groupColor = seatsData[seatKey].color;
							seat.groupName = seatsData[seatKey].zoneName;
							seat.status = 'available';
						} else {
							seat.groupColor = null;
							seat.groupName = null;
							seat.status = 'available';
						}
					} else {
						newSelection.add(seatKey);
						seat.status = 'selected';
					}
				}
			});
			selectedSeats = newSelection;
			engine.render();
			updateSelectionInfo();
		}

		// 显示分区表单
		function showZoneForm() {
			if (selectedSeats.size === 0) {
				alert('请先选择要分区的座位！');
				return;
			}
			document.getElementById('zoneForm').style.display = 'block';
			document.getElementById('createZoneBtn').style.display = 'none';
		}

		// 取消分区
		function cancelZone() {
			document.getElementById('zoneForm').style.display = 'none';
			document.getElementById('createZoneBtn').style.display = 'block';
			// 清空表单
			document.getElementById('zoneName').value = '';
			document.getElementById('zonePrice').value = '';
			document.getElementById('zoneColor').value = '#1890ff';
		}

		// 创建分区
		function createZone() {
			const name = document.getElementById('zoneName').value.trim();
			const price = document.getElementById('zonePrice').value.trim();
			const color = document.getElementById('zoneColor').value;

			if (!name) {
				alert('请输入分区名称！');
				document.getElementById('zoneName').focus();
				return;
			}

			if (!price || isNaN(price) || parseFloat(price) < 0) {
				alert('请输入有效的价格！');
				document.getElementById('zonePrice').focus();
				return;
			}

			// 检查分区名称是否重复
			if (zones.some(zone => zone.name === name)) {
				alert('分区名称已存在，请使用其他名称！');
				document.getElementById('zoneName').focus();
				return;
			}

			// 保存操作历史
			saveOperationToHistory('create-zone', `创建分区"${name}"`);

			// 创建新分区
			const zone = {
				id: Date.now().toString(),
				name: name,
				price: parseFloat(price),
				color: color,
				seats: Array.from(selectedSeats)
			};

			zones.push(zone);

			// 更新座位数据
			selectedSeats.forEach(seatKey => {
				if (seatsData[seatKey]) {
					seatsData[seatKey].zoneName = name;
					seatsData[seatKey].price = parseFloat(price);
					seatsData[seatKey].color = color;
				}
			});

			// 更新座位颜色
			engine.seats.forEach(seat => {
				const seatKey = seat.row + "_" + seat.col;
				if (selectedSeats.has(seatKey)) {
					seat.groupColor = color;
					seat.groupName = name;
					seat.status = 'available';
				}
			});

			// 清除选择
			selectedSeats.clear();

			// 更新界面
			updateZoneList();
			updateSelectionInfo();
			cancelZone();
			engine.render();

			// 显示成功消息
			layui.use('layer', function(){
				const layer = layui.layer;
				layer.msg(`分区"${name}"创建成功！`, {icon: 1});
			});
		}

		// 更新分区列表
		function updateZoneList() {
			const zoneList = document.getElementById('zoneList');

			if (zones.length === 0) {
				zoneList.innerHTML = '<p style="color: #999; text-align: center;">暂无分区</p>';
				return;
			}

			let html = '';
			zones.forEach((zone, index) => {
				html += `
					<div class="zone-item" data-zone-id="${zone.id}">
						<div style="display: flex; justify-content: space-between; align-items: flex-start;">
							<h5 style="margin: 0; flex: 1;">${zone.name}</h5>
							<button class="layui-btn layui-btn-danger layui-btn-xs" onclick="deleteZone('${zone.id}')" title="删除分区">
								<i class="layui-icon layui-icon-delete"></i>
							</button>
						</div>
						<div class="zone-info" style="margin-top: 8px;">
							<span>价格：¥${zone.price}</span>
							<span>座位：${zone.seats.length}个</span>
							<div class="zone-color" style="background-color: ${zone.color}" title="分区颜色: ${zone.color}"></div>
						</div>
						<div style="margin-top: 8px; font-size: 11px; color: #999;">
							点击座位可预览此分区
						</div>
					</div>
				`;
			});

			zoneList.innerHTML = html;
		}

		// 删除分区
		function deleteZone(zoneId) {
			const zone = zones.find(z => z.id === zoneId);
			if (!zone) return;

			if (!confirm(`确定要删除分区"${zone.name}"吗？`)) {
				return;
			}

			// 保存操作历史
			saveOperationToHistory('delete-zone', `删除分区"${zone.name}"`);

			// 从分区列表中移除
			zones = zones.filter(z => z.id !== zoneId);

			// 清除座位的分区信息
			zone.seats.forEach(seatKey => {
				if (seatsData[seatKey]) {
					delete seatsData[seatKey].zoneName;
					delete seatsData[seatKey].color;
					seatsData[seatKey].price = 0;
				}
			});

			// 更新座位显示
			engine.seats.forEach(seat => {
				const seatKey = seat.row + "_" + seat.col;
				if (zone.seats.includes(seatKey)) {
					seat.groupColor = null;
					seat.groupName = null;
				}
			});

			// 更新界面
			updateZoneList();
			engine.render();

			// 显示成功消息
			layui.use('layer', function(){
				const layer = layui.layer;
				layer.msg(`分区"${zone.name}"已删除`, {icon: 1});
			});
		}

		// 保存分区设置
		function saveZones() {
			if (zones.length === 0) {
				layui.use('layer', function(){
					const layer = layui.layer;
					layer.msg('请至少创建一个分区！', {icon: 2});
				});
				return;
			}

			// 检查是否所有座位都已分区
			const totalSeats = Object.keys(seatsData).length;
			const assignedSeats = zones.reduce((total, zone) => total + zone.seats.length, 0);

			if (assignedSeats < totalSeats) {
				layui.use('layer', function(){
					const layer = layui.layer;
					layer.confirm(`还有 ${totalSeats - assignedSeats} 个座位未分区，确定要保存吗？`, {
						btn: ['确定保存', '继续编辑']
					}, function(index){
						layer.close(index);
						doSaveZones();
					});
				});
			} else {
				doSaveZones();
			}
		}

		// 执行保存操作
		function doSaveZones() {
			try {
				// 保存到localStorage
				localStorage.setItem('seats', JSON.stringify(seatsData));
				localStorage.setItem('zones', JSON.stringify(zones));

				// 保存分区统计信息
				const zoneStats = {
					totalZones: zones.length,
					totalSeats: Object.keys(seatsData).length,
					assignedSeats: zones.reduce((total, zone) => total + zone.seats.length, 0),
					saveTime: new Date().toISOString()
				};
				localStorage.setItem('zoneStats', JSON.stringify(zoneStats));

				// 显示成功消息
				layui.use('layer', function(){
					const layer = layui.layer;
					layer.msg('分区设置已保存！', {icon: 1}, function(){
						// 可以跳转到下一步或其他页面
						// location.href = 'step4.html';
					});
				});

			} catch (error) {
				layui.use('layer', function(){
					const layer = layui.layer;
					layer.msg('保存失败，请重试！', {icon: 2});
				});
			}
		}

		// 返回第二步
		function goToStep2() {
			layui.use('layer', function(){
				const layer = layui.layer;
				layer.confirm('确定要返回第二步吗？当前的分区设置将会丢失！', {
					btn: ['确定返回', '取消']
				}, function(index){
					layer.close(index);
					location.href = 'step2.html';
				});
			});
		}

		// 键盘快捷键
		document.addEventListener('keydown', function(e) {
			// Ctrl+A 全选
			if (e.ctrlKey && e.key === 'a') {
				e.preventDefault();
				selectAllSeats();
			}
			// Ctrl+D 清除选择
			else if (e.ctrlKey && e.key === 'd') {
				e.preventDefault();
				clearSelection();
			}
			// Ctrl+I 反选
			else if (e.ctrlKey && e.key === 'i') {
				e.preventDefault();
				invertSelection();
			}
			// Ctrl+Z 撤销
			else if (e.ctrlKey && e.key === 'z') {
				e.preventDefault();
				undoLastOperation();
			}
			// Ctrl+S 保存
			else if (e.ctrlKey && e.key === 's') {
				e.preventDefault();
				saveZones();
			}
			// Escape 取消分区表单
			else if (e.key === 'Escape') {
				const zoneForm = document.getElementById('zoneForm');
				if (zoneForm && zoneForm.style.display !== 'none') {
					cancelZone();
				}
			}
		});

		// 分区颜色预设
		const presetColors = [
			'#1890ff', '#52c41a', '#faad14', '#f5222d',
			'#722ed1', '#13c2c2', '#eb2f96', '#fa541c'
		];

		// 随机分配分区颜色
		function getRandomZoneColor() {
			const usedColors = zones.map(zone => zone.color);
			const availableColors = presetColors.filter(color => !usedColors.includes(color));

			if (availableColors.length > 0) {
				return availableColors[Math.floor(Math.random() * availableColors.length)];
			} else {
				// 如果预设颜色都用完了，生成随机颜色
				return '#' + Math.floor(Math.random()*16777215).toString(16);
			}
		}

		// 显示分区表单时自动设置随机颜色
		function showZoneForm() {
			if (selectedSeats.size === 0) {
				layui.use('layer', function(){
					const layer = layui.layer;
					layer.msg('请先选择要分区的座位！', {icon: 2});
				});
				return;
			}

			document.getElementById('zoneForm').style.display = 'block';
			document.getElementById('createZoneBtn').style.display = 'none';

			// 自动设置随机颜色
			document.getElementById('zoneColor').value = getRandomZoneColor();

			// 自动聚焦到名称输入框
			document.getElementById('zoneName').focus();
		}

		// 保存操作到历史记录
		function saveOperationToHistory(type, description) {
			const operation = {
				type: type,
				description: description,
				timestamp: Date.now(),
				selectedSeats: new Set(selectedSeats),
				seatsData: JSON.parse(JSON.stringify(seatsData)),
				zones: JSON.parse(JSON.stringify(zones))
			};

			operationHistory.push(operation);

			// 限制历史记录数量
			if (operationHistory.length > 50) {
				operationHistory.shift();
			}

			updateUndoButton();
		}

		// 撤销操作
		function undoLastOperation() {
			if (operationHistory.length === 0) {
				return;
			}

			const lastOperation = operationHistory.pop();

			// 恢复状态
			selectedSeats = lastOperation.selectedSeats;
			seatsData = lastOperation.seatsData;
			zones = lastOperation.zones;

			// 更新座位状态
			engine.seats.forEach(seat => {
				const seatKey = seat.row + "_" + seat.col;
				if (seatsData[seatKey]) {
					if (selectedSeats.has(seatKey)) {
						seat.status = 'selected';
					} else {
						seat.status = 'available';
						if (seatsData[seatKey].color) {
							seat.groupColor = seatsData[seatKey].color;
							seat.groupName = seatsData[seatKey].zoneName;
						} else {
							seat.groupColor = null;
							seat.groupName = null;
						}
					}
				} else {
					seat.status = 'empty';
					seat.groupColor = null;
					seat.groupName = null;
				}
			});

			// 更新界面
			updateSelectionInfo();
			updateZoneList();
			updateUndoButton();
			engine.render();
		}
	</script>
</body>
</html>
