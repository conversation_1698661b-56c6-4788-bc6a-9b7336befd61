<!DOCTYPE html>
<html lang="ch">
	<head>
		<title>设置座位</title>
		<!-- 设置 viewport -->
		<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
		<!-- IE -->
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<!-- 兼容国产浏览器的高速模式 -->
		<meta name="renderer" content="webkit">
		<meta name="Author" content="<EMAIL>" />
		<meta name="Keywords" content="" />
		<meta name="Description" content="" />
		<meta charset="UTF-8">
		<link rel="stylesheet" type="text/css" href="css/reset.css">
		<link rel="stylesheet" href="css/translateCommon.css">
		<link rel="stylesheet" href="layui/css/layui.css" />
		<link rel="stylesheet" href="css/stepPlug.css" />
		<link rel="stylesheet" href="css/seat3.css" />
		<link rel="stylesheet" href="css/step2only.css" />

	</head>
	<body>
		<div id="section">
			<!-- header -->
			<div id="header">
				<div class="hui">
					<img src="images/hui.jpg" alt="">
				</div>
				<div class="logo">
					<h1 class="f_left">
						<a href="#">
							<img src="images/logo.png" alt=""></a>
					</h1>
					<div class="f_right logo_r">
						<ul class="logo_r_show">
							<!-- 保持原有的header内容 -->
						</ul>
					</div>
				</div>
			</div>
			<!-- content -->
			<div id="content" class="clearfix">
				<!-- 左边导航 -->
				<div class="c_left">
					<div class="c_left_content">
						<div class="c_left_content_title">
							<h3>座位规划</h3>
						</div>
						<div class="c_left_content_list">
							<ul>
								<li class="active">
									<a href="step1.html">
										<i class="layui-icon layui-icon-set"></i>
										<span>设置场地排列</span>
									</a>
								</li>
								<li class="active">
									<a href="step2.html">
										<i class="layui-icon layui-icon-template-1"></i>
										<span>设置场地布局</span>
									</a>
								</li>
								<li>
									<a href="step3.html">
										<i class="layui-icon layui-icon-cols"></i>
										<span>设置分区</span>
									</a>
								</li>
								<li>
									<a href="step4.html">
										<i class="layui-icon layui-icon-picture"></i>
										<span>效果图设置</span>
									</a>
								</li>
								<li>
									<a href="step5.html">
										<i class="layui-icon layui-icon-picture"></i>
										<span>分区引导图</span>
									</a>
								</li>
								<li>
									<a href="step6.html">
										<i class="layui-icon layui-icon-picture"></i>
										<span>嘉宾视图</span>
									</a>
								</li>
								<li>
									<a href="assignSeats.html">
										<i class="layui-icon layui-icon-username"></i>
										<span>座位分配</span>
									</a>
								</li>
							</ul>
						</div>
					</div>
				</div>
				<!-- 主要模块 -->
				<div class="c_right">
					<div class="main">
						<div class="main-content">
							<div class="main-html">
								<!-- 步骤条 -->
								<div class="step-html">
									<div class="step-box">
										<div class="step-item step-item-active">
											<div class="step-item-tail"></div>
											<div class="step-item-icon">
												<i class="layui-icon layui-icon-ok"></i>
											</div>
											<div class="step-item-content">
												<div class="step-item-title">设置场地排列</div>
												<div class="step-item-description">设置场地的行数和列数</div>
											</div>
										</div>
										<div class="step-item step-item-active">
											<div class="step-item-tail"></div>
											<div class="step-item-icon">
												<span class="step-item-icon-index">2</span>
											</div>
											<div class="step-item-content">
												<div class="step-item-title">设置场地布局</div>
												<div class="step-item-description">选择实际有座位的位置</div>
											</div>
										</div>
										<div class="step-item">
											<div class="step-item-tail"></div>
											<div class="step-item-icon">
												<span class="step-item-icon-index">3</span>
											</div>
											<div class="step-item-content">
												<div class="step-item-title">设置分区</div>
												<div class="step-item-description">对座位进行分组和颜色设置</div>
											</div>
										</div>
									</div>
								</div>
								<!-- 座位图提示 -->
								<div class="seat-setting-tips">
									*请选择实际有座位的位置，取消没有座位的位置
									<span id="seatTotal">当前座位总数：0个</span>
								</div>
								<!-- 座位图容器 - 保持原有的结构和样式 -->
								<div class="seatBoxContent seatBoxContentSetp2" id="seatBoxContent">
									<div class="newSeatBox">
										<div class="seatBox" id="seatBox" style="width: auto;" zoom="1">
											<!-- Canvas将在这里创建 -->
										</div>
									</div>
									<!-- 缩放控制 - 保持原有位置和样式 -->
									<div class="zoomBox">
										<i class="layui-icon layui-icon-reduce-circle" onclick="zoomFn('in')"></i>
										<i class="layui-icon layui-icon-add-circle" onclick="zoomFn('out')"></i>
									</div>
								</div>
								<!-- 底部按钮 -->
								<div class="bottom-btn">
									<button type="button" class="layui-btn layui-btn-primary" onclick="goBack()">上一步</button>
									<button type="button" class="layui-btn" id="save">下一步</button>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- 保持原有的脚本引用 -->
		<script src="layui/layui.js"></script>
		<script src="js/jquery-3.3.1.min.js"></script>
		
		<!-- 新的座位引擎 -->
		<script type="module">
			// 导入新的座位系统
			import { CompatibleSeatWrapper } from './CompatibleSeatWrapper.js'
			
			// 确保全局可用
			window.CompatibleSeatWrapper = CompatibleSeatWrapper
			
			// 保持原有的全局函数
			window.zoomFn = function(direction) {
				const canvas = document.querySelector('#seatBox canvas')
				if (canvas && canvas.seatEngine) {
					if (direction === 'in') {
						canvas.seatEngine.zoomOut()
					} else if (direction === 'out') {
						canvas.seatEngine.zoomIn()
					}
				}
			}
			
			window.goBack = function() {
				location.href = "step1.html"
			}
			
			// 页面加载完成后初始化
			document.addEventListener('DOMContentLoaded', function() {
				// 获取原有数据
				let seatRows = localStorage.getItem("seatRows") || 10
				let seatCols = localStorage.getItem("seatCols") || 10
				let datas = {}
				
				try {
					const savedSeats = localStorage.getItem("seats")
					if (savedSeats) {
						datas = JSON.parse(savedSeats)
					}
				} catch (e) {
					console.log("没有找到已保存的座位数据")
				}
				
				// 创建座位系统 - 使用完全相同的参数
				const seats = new CompatibleSeatWrapper({
					rows: parseInt(seatRows),
					cols: parseInt(seatCols),
					size: 20,
					box: "#seatBox",
					step: 101,
					datas: datas,
					thre_id: 20,
					hall_id: 10,
					movie_id: 30
				})
				
				// 保存到全局变量，保持原有的访问方式
				window.seats = seats
				
				// 绑定保存按钮 - 保持原有逻辑
				document.getElementById('save').addEventListener('click', function() {
					console.log(seats.getSeats())
					localStorage.setItem("seats", JSON.stringify(seats.getSeats()))
					localStorage.removeItem("groupArrayObject")
					localStorage.removeItem("selected")
					localStorage.removeItem("channelSelectArrayStorage")
					location.href = "step3.html"
				})
			})
		</script>
	</body>
</html>
