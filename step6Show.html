<!DOCTYPE html>
<html lang="ch">
	<head>
		<title>区域视图设置</title>
		<!-- 设置 viewport -->
		<!-- <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" /> -->
		<!-- IE -->
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<!-- 兼容国产浏览器的高速模式 -->
		<meta name="renderer" content="webkit">
		<meta name="Author" content="<EMAIL>" />
		<meta name="Keywords" content="" />
		<meta name="Description" content="" />
		<meta charset="UTF-8">
		<link rel="stylesheet" type="text/css" href="css/reset.css">
		<link rel="stylesheet" href="css/translateCommon.css">
		<link rel="stylesheet" href="layui/css/layui.css" />
		<link rel="stylesheet" href="css/stepPlug.css" />
		<link rel="stylesheet" href="plugin/spectrum/spectrum.css">
		<link rel="stylesheet" href="css/modalbox.css" />
		<link rel="stylesheet" href="css/seat3.css" />
		<link rel="stylesheet" href="css/dragModule.css" />
		<link rel="stylesheet" href="css/step6only.css" />
		<link rel="stylesheet" href="css/step6Show.css" />
		<link rel="stylesheet" href="css/stepShowCommon.css" />

	</head>
	<body>
		<div class="main-content">
			<div class="main-left">
				<!-- <div class="step-html" style="width: 600px;">
					<div class="step-box">
						<div class="step-list step-act">
							<span class="step-number">1</span>
						</div>
						<div class="step-list-html-text step-act">设置场地排列</div>
						<div class="step-line"></div>
						<div class="step-list step-act">
							<span class="step-number step-act">2</span>
						</div>
						<div class="step-list-html-text step-act">设置场地布局</div>
						<div class="step-line"></div>
						<div class="step-list step-act">
							<span class="step-number step-act">3</span>
						</div>
						<div class="step-list-html-text step-act">设置分区</div>
						<div class="step-line"></div>
						<div class="step-list step-act">
							<span class="step-number step-act">4</span>
						</div>
						<div class="step-list-html-text step-act">设置效果图</div>
					</div>
				</div> -->

				<!-- <div class="main-left-tab">
					<span onclick="viewSrc('step4.html')">分区引导图设置</span>
					<span onclick="viewSrc('step5.html')">座位视图设置</span>
					<span class="main-left-tab-act" onclick="viewSrc('step6.html')">区域视图设置</span>
				</div> -->

				<div class="zoomBox">
					<i class="layui-icon layui-icon-reduce-circle" onclick="zoomFn('in')"></i>
					<i class="layui-icon layui-icon-add-circle" onclick="zoomFn('out')"></i>
				</div>
				<div
					style="width: 100%;text-align: center;font-size: 25px;color: #000;font-weight: bold;margin-top: 15px;letter-spacing: 5px;"
					id="mySeat">
				</div>
				<!-- 座位遮罩层 -->
				<!-- <div class="seatFixed"></div> -->
				<div class="seatBoxContent seatBoxContentSetp4" id="seatBoxContent">

					<!--
					 noGroupBgColor:非分区域背景颜色
					 noGroupBorderColor:非分区域边框颜色
					 groupBgColor：分配区域背景颜色
					 groupBorderColor：分区域边框颜色
					 -->
					<div class="placeholderSymbol" style="display: none;" nogroupbgcolor="rgba(0, 0, 255, 1)"
						nogroupbordercolor="rgb(0, 255, 255)" groupbgcolor="rgba(231, 70, 70, 1)" groupbordercolor="rgb(0, 255, 0)">
					</div>



					<div class="seatBox" id="seatBox" style="width: 960px; background: rgb(229, 229, 229);" zoom="1">

						<div class="dragContent">
							<div class="dragContent freeContainerModule">

								<!-- <div class="ui-draggable freeDragItem " id="timestamp" onclick="freeDragli(this,event)">
																	<div name="paper">
																		<div style="position: relative;" class="freeBtbBox">
																			<div class="freeweb_btn"><a src="#">组件</a></div>
																			<div class="coor3 "></div>
																			<img class="del_img" src="images/delete_hover.png" onclick="freeDeleteImg(this)" />
																		</div>
																	</div>
																</div> -->

								<div class="ui-draggable freeDragItem ui-draggable-handle" name="a1区"
									style="top: 4px; left: 7px; opacity: 1;display: none;" id="m1725244490576"
									onclick="freeDragli(this,event)">
									<div name="paper">
										<div style="position: relative; width: 969px; height: 292px; left: 0px;" class="freeBtbBox">
											<div class="freeweb_btn"><a src="#"
													style="border-radius: 0px; font-size: 14px; background: rgb(198, 60, 180);">a1区</a>
											</div>
											<div class="coor3"></div>
											<img class="del_img" src="images/delete_hover.png" onclick="freeDeleteImg(this)">
										</div>
									</div>
								</div>
								<div class="ui-draggable freeDragItem ui-draggable-handle" name="a2区"
									style="top: 301px; left: 57px; opacity: 1;" id="m1725244503855" onclick="freeDragli(this,event)">
									<div name="paper">
										<div style="position: relative; left: 0px; width: 912px; height: 53px;" class="freeBtbBox">
											<div class="freeweb_btn"><a src="#"
													style="border-radius: 0px; font-size: 14px; background: rgb(0, 255, 255); color: rgb(10, 0, 0);">a2区</a>
											</div>
											<div class="coor3"></div>
											<img class="del_img" src="images/delete_hover.png" onclick="freeDeleteImg(this)">
										</div>
									</div>
								</div>
								<div class="ui-draggable freeDragItem ui-draggable-handle" name="a3区"
									style="top: 364px; left: 8px; opacity: 1;" id="m1725244536087" onclick="freeDragli(this,event)">
									<div name="paper">
										<div style="position: relative; left: 0px; width: 886px; height: 280px;" class="freeBtbBox">
											<div class="freeweb_btn"><a src="#"
													style="border-radius: 0px; font-size: 14px; background: rgb(255, 255, 0); color: rgb(7, 0, 0);">a3区</a>
											</div>
											<div class="coor3"></div>
											<img class="del_img" src="images/delete_hover.png" onclick="freeDeleteImg(this)">
										</div>
									</div>
								</div>
								<div class="ui-draggable freeDragItem ui-draggable-handle dragItemClicked" name="a4区"
									style="top: 365px; left: 895px; opacity: 1;" id="m1725244563643" onclick="freeDragli(this,event)">
									<div name="paper">
										<div style="position: relative; left: 0px; width: 77px; height: 194px;" class="freeBtbBox">
											<div class="freeweb_btn"><a src="#" style="border-radius: 0px; font-size: 14px;">a4区</a>
											</div>
											<div class="coor3 coorClick">
											</div>
											<img class="del_img" src="images/delete_hover.png" onclick="freeDeleteImg(this)">
										</div>
									</div>
								</div>
							</div>
						</div>


					</div>

				</div>
				<!-- <div class="bottom-btn">
					<button class="layui-btn" type="button" onclick="editSeat()">修改区域</button>
					<button class="layui-btn" type="button" onclick="">保存</button>
					<button class="layui-btn" type="button" onclick="viewMySeat()">预览</button>
				</div> -->
			</div>
			<div class="main-right" style="display: none;">

			</div>
		</div>


		<!-- 灰色背景 -->
		<div id="f-fade" class="black_overlay" style="display: none;"></div>

		<!-- 新增网页 -->
		<div class="white-contentAll" id="light1" style="display:none;width:400px;overflow: initial;">
			<div class="white-header">
				<span class="white-headerTitle">
					模块关联座位区域
				</span>
				<span class="white-delete" onclick="cancel('light1')">
				</span>
			</div>
			<div class="white-body" style="max-height: 420px;overflow: initial;">
				<div class="data-module">

					<div class="data-list">
						<div class="data-list-title">
							<b class="redSpan">*</b>请选择模块关联座位区域
						</div>
						<div class="data-list-input layui-form">
							<select name="" id="" lay-filter="groupSelectFn">
								<option value="请选择">请选择</option>
								<option value="a1区">a1区</option>
								<option value="a2区">a2区</option>
								<option value="a3区">a3区</option>
								<option value="a4区">a4区</option>
							</select>
						</div>
					</div>

				</div>
			</div>
			<div class="white-footer">
				<button class="white-close" onclick="cancel('light1')">
					取消
				</button>
				<button class="white-sure white-sureAdd" onclick="groupSelectSure()">
					确定
				</button>
			</div>
		</div>

		<div class="white-contentAll" id="light2" style="display:none;width:400px;overflow: initial;">
			<div class="white-header">
				<span class="white-headerTitle">
					模块关联座位区域
				</span>
				<span class="white-delete" onclick="cancel('light2')">
				</span>
			</div>
			<div class="white-body" style="max-height: 420px;overflow: initial;">
				<div class="data-module">

					<div class="data-list">
						<div class="data-list-title">
							<b class="redSpan">*</b>请选择模块关联座位区域
						</div>
						<div class="data-list-input layui-form">
							<select name="" id="" lay-filter="groupCopySelectFn">
								<option value="请选择">请选择</option>
								<option value="a1区">a1区</option>
								<option value="a2区">a2区</option>
								<option value="a3区">a3区</option>
								<option value="a4区">a4区</option>
							</select>
						</div>
					</div>

				</div>
			</div>
			<div class="white-footer">
				<button class="white-close" onclick="cancel('light2')">
					取消
				</button>
				<button class="white-sure white-sureAdd" onclick="groupCopySelectSure()">
					确定
				</button>
			</div>
		</div>



		<!-- <script src="js/jquery-1.7.2.min.js"></script> -->
		<script src="js/jquery-3.6.3.min.js"></script>
		<script src="js/index.js"></script>
		<script src="js/index.js"></script>
		<!-- <script src="js/jquery-ui-1.9.2.custom.min.js"></script> -->
		<script src="js/moveZoom.js"></script>
		<!-- <script src="js/jquery-ui.js"></script> -->
		<!-- <script src="js/dragModule6.js"></script> -->


		<!-- <script>
			$(function() {
				// 动态加载可放大功能
				$.getScript('js/dragModule6.js', function() {});
				// 动态加载可放大功能
				$.getScript('js/moveZoom.js', function() {
					all();
				});
			})
		</script> -->

		<script src="translateJs/translate.js"></script>
		<script src="translateJs/translateCommon.js"></script>
		<script src="plugin/spectrum/spectrum.js"></script>
		<script src="plugin/jdialog/JDialog.min.js"></script>
		<script src="js/seat.js"></script>
		<script src="layui/layui.js"></script>
		<script>
			var layer = "";
			var slider = "";
			var colorpickerRender = ""; //颜色选择器
			// var setWidthSlider = ""; // 定义宽度滑块
			var setBorderWidthSlider = ""; // 边框宽度
			var setRadiusSlider = ""; // 定义圆角滑块
			var setFontSizeSlider = ""; // 定义字体大小滑块
			var noGroupNumber = 0;
			var noGroupHasAssign = 0; // 未分组已分配数量
			var form = "";
			var channelNumber = 0; // 批量通道人数
			var canUseNumber = 0; // 可用分配数量
			var canUseGroup = []; // 可用分配数组
			var channelSelectArray = JSON.parse(localStorage.getItem("channelSelectArrayStorage")) || []; // 已经分配过的通道

			var dragModuleWidth = 100;
			var dragModuleBorderWidth = 0
			// 所选区域
			var groupSelectText = "请选择";
			var groupCopySelectText = "请选择"


			layui.use(function() {
				form = layui.form;
				var element = layui.element;
				layer = layui.layer;

				element.on('tab(tab-hash)', function(obj) {
					let thisIndex = obj.index
					if (thisIndex == 1) {
						$(".dragItemClicked").removeClass("dragItemClicked")
						$(".coorClick").removeClass("coorClick")
					}
				});
				// 所属分组区域选择
				form.on('select(groupSelectFn)', function(data) {
					var elem = data.elem; // 获得 select 原始 DOM 对象
					var value = data.value; // 获得被选中的值
					console.log(value)
					groupSelectText = value
				});
				// 复制所属区域选择
				form.on('select(groupCopySelectFn)', function(data) {
					var elem = data.elem; // 获得 select 原始 DOM 对象
					var value = data.value; // 获得被选中的值
					console.log(value)
					groupCopySelectText = value
				});

				colorpickerRender = layui.colorpicker;

				// 滑块
				slider = layui.slider;


			});



			// 放大缩小
			function zoomFn(obj) {
				let zoomVal = Number((Number($("#seatBox").attr("zoom"))).toFixed(1))
				if (obj == "in") {
					if (zoomVal >= 0.2) {
						zoomVal = zoomVal - 0.1
						$("#seatBox").css({
							transform: "scale(" + zoomVal + ")"
						})
					}

				} else if (obj == "out") {

					if (zoomVal < 1) {
						zoomVal = zoomVal + 0.1
						$("#seatBox").css({
							transform: "scale(" + zoomVal + ")"
						})
					}

				}
				$("#seatBox").attr("zoom", zoomVal)
			}
		</script>
		<script>
			let seatRows = localStorage.getItem("seatRows");
			let seatCols = localStorage.getItem("seatCols");

			var groupObject = JSON.parse(localStorage.getItem('groupArrayObject')) || []
			try {
				var datas = JSON.parse(localStorage.getItem("seats"));
				var selected = JSON.parse(localStorage.getItem("selected"));
			} catch (e) {}
			var seats = $.seats({
				box: "#seatBox",
				rows: seatRows,
				cols: seatCols,
				size: 20, //座位的尺寸，单位：像素	
				hall_id: 10, //厅，如：5号厅
				thre_id: 20, //剧场，如：大地电影院中天广场沃尔玛店
				movie_id: 30, //场次ID
				step: 5,
				maxTicketNumber: 1000, //最多购票张数
				datas: datas,
				selected: selected || {},
				onSelected: function(seat) {

					/*************/
					var id = "selected_" + seat.row + "_" + seat.col;
					// 获取分区名称
					console.log(seat.color)
					let getGroupName = seat.groupName
					let getGroupLength = $(`span[name='${getGroupName}']`).length
					// console.log(getGroupLength)

					// 判断是否已经分配的座位
					let hasDisabled = $(`#${seat.row}_${seat.col}`).find("span").hasClass(
						"disabledCancel")
					console.log(hasDisabled)
					// var selectedHtml = ''
					// 获取真实的座位号（去除空格的)
					let realSeat = $(`#${seat.row}_${seat.col}`).attr("seat").split("-")
					console.log(realSeat)

					/*********************************/
					return true;
				},
				onUnselected: function(seat) {
					var id = "selected_" + seat.row + "_" + seat.col;
					$("#" + id).remove();
				},
				onerror: function(msg) {
					// JDialog.msg({
					// 	type: 'error',
					// 	content: msg,
					// 	container: "#seatBox"
					// });
					layer.msg(msg, {
						icon: 2,
						time: 1000 // 设置 2 秒后自动关闭
					});
				}
			});

			// 所属区域高亮

			$(function() {
				// let hrefSrc = window.location.href.split("?")[1].split("-")
				let row = 4
				let col = 18
				let seat = `${row}-${col}`
				console.log(seat)
				let groupName = $(`td[seat='${seat}']`).find("span").attr("name")
				console.log(groupName)
				// 回显座位到页面
				$("#mySeat").html(`我的座位：${groupName}区 - ${row}排${col}座`)


				// 获取颜色
				let noGroupBgColor = $(".placeholderSymbol").attr("noGroupBgColor") // 非分区域背景颜色
				let noGroupBorderColor = $(".placeholderSymbol").attr("noGroupBorderColor") // 非分区域边框颜色
				let groupBgColor = $(".placeholderSymbol").attr("groupBgColor") // 分配区域背景颜色
				let groupBorderColor = $(".placeholderSymbol").attr("groupBorderColor") // 分区域边框颜色

				// $(`td[seat='${seat}']`).find("span").html(seat).css({
				// 	color: "#fff",
				// 	fontSize: "10px",
				// 	lineHeight: "20px"
				// })
				// console.log($("td").find(`span[name='${groupName}']`).)
				let groupBg = $("td").find(`span[name='${groupName}']`).attr("style").split("background")[1]
					.replace(":",
						"")
				groupBg = groupBg.replace(";", "")
				// 遍历所有可选座位,并给非我的区域座位设置颜色
				console.log(noGroupBorderColor)
				// 如果没有设置非分区背景这进行
				if (noGroupBgColor == "" || noGroupBgColor == undefined) {

				} else {
					$("td").each(function() {
						let backNone = $(this).find("span").attr("style")
						if (!backNone.includes("background: none;")) {
							$(this).find("span").css({
								background: noGroupBgColor,
								"border": "1px solid " + noGroupBorderColor
							})
						}
					})
				}
				$("td").each(function() {
					let backNone = $(this).find("span").attr("style")
					if (!backNone.includes("background: none;")) {
						$(this).find("span").css({
							"border-width": "1px",
							"border-style": "solid",
							"border-color": noGroupBorderColor || "none"
						})
					}
				})

				$("td").find("span").removeClass("disabled")

				// 给我的区域设置颜色	
				console.log(groupName)
				$("td").find(`span[name='${groupName}']`).css({
					background: groupBgColor || groupBg,
					"border-width": "1px",
					"border-style": "solid",
					"border-color": groupBorderColor || "none"
				})
				$(`td[seat='${seat}']`).find("span").addClass("disabled")
				// // 给我的座位设置高亮
				// $(".disabled").css({
				// 	background: mySeatBgColor || groupBgColor || groupBg,
				// 	"boder-width": "1px",
				// 	"boder-style": "solid",
				// 	"border-color": mySeatBorderColor || "none"
				// })

				$("td").find("span").css({
					opacity: 0
				})


			})

			function editSeat() {
				window.location.href = "step3.html"
			}
			// 预览
			function viewMySeat() {
				window.location.href = "mySeat.html"
			}

			// 遮罩层
			function viewTask(id) {
				$("div[id^=light]").hide();
				$("#" + id).show();
				$("#f-fade").show();
			}

			function cancel(id) {
				$("#" + id).hide();
				$("#f-fade").hide();
			}

			function viewSrc(src) {
				window.location.href = src
			}


			// 滚动条滚到到正中间
			function scrollCenter() {
				// console.log(1234)
				var div = document.getElementById("seatBox");
				var computedStyle = getComputedStyle(div, null);
				var paddingTop = Number(computedStyle.paddingTop.replace("px", ""))
				var paddingLeft = Number(computedStyle.paddingLeft.replace("px", ""))
				var paddingRight = Number(computedStyle.paddingRight.replace("px", ""))
				var paddingBottom = Number(computedStyle.paddingBottom.replace("px", ""))
				// let tableWidth = $(".dragContent").width()
				// let tableHeight = $(".dragContent").height()

				let tableWidth = $("#seatBox").width() + Number(computedStyle.paddingLeft.replace("px", "")) + Number(
					computedStyle.paddingRight.replace("px", ""))
				let tableHeight = $("#seatBox").height() + Number(computedStyle.paddingTop.replace("px", "")) + Number(
					computedStyle.paddingBottom.replace("px", ""))

				let scrollWidth = $(".seatBoxContent").width()
				// let scrollHeight = $(".seatBoxContent").height()
				// 可见区域的高度
				let scrollHeight = document.documentElement.clientHeight

				let srollLeft = (tableWidth - scrollWidth) / 2
				let srollTop = (tableHeight - scrollHeight) / 2
				console.log("left:" + srollLeft)
				console.log("top:" + srollTop)
				if (srollLeft > 0) {
					$(".seatBoxContent").scrollLeft(srollLeft)
				}
				if (srollTop > 0) {
					// $("body,html").scrollTop(srollTop)
					setTimeout(function() {
						$("body,html").scrollTop(srollTop)
					}, 100)
				}

				console.log("总高度-" + tableHeight)
				console.log("可见区域高度-" + scrollHeight)
				console.log($(".seatBox").attr("paading-left"))
			}


			$(function() {
				scrollCenter()
			})
		</script>

		<!-- 新增组件设置js -->
		<script src="js/seatModule6.js"></script>



	</body>
</html>