<!DOCTYPE html>
<html lang="ch">
	<head>
		<title>设置座位</title>
		<!-- 设置 viewport -->
		<!-- <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" /> -->
		<!-- IE -->
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<!-- 兼容国产浏览器的高速模式 -->
		<meta name="renderer" content="webkit">
		<meta name="Author" content="<EMAIL>" />
		<meta name="Keywords" content="" />
		<meta name="Description" content="" />
		<meta charset="UTF-8">
		<link rel="stylesheet" type="text/css" href="css/reset.css">
		<link rel="stylesheet" href="css/translateCommon.css">
		<link rel="stylesheet" href="layui/css/layui.css" />
		<link rel="stylesheet" href="css/stepPlug.css" />
		<link rel="stylesheet" href="plugin/spectrum/spectrum.css">
		<link rel="stylesheet" href="css/seat3.css" />
		<link rel="stylesheet" href="css/dragModule.css" />
		<link rel="stylesheet" href="css/mySeat.css" />

	</head>
	<body>
		<div class="main-content">
			<div class="main-left">
				<!-- <div class="step-html" style="width: 600px;">
					<div class="step-box">
						<div class="step-list step-act">
							<span class="step-number">1</span>
						</div>
						<div class="step-list-html-text step-act">设置场地排列</div>
						<div class="step-line"></div>
						<div class="step-list">
							<span class="step-number step-act">2</span>
						</div>
						<div class="step-list-html-text step-act">设置场地布局</div>
						<div class="step-line"></div>
						<div class="step-list">
							<span class="step-number step-act">3</span>
						</div>
						<div class="step-list-html-text step-act">设置分区</div>
						<div class="step-line"></div>
						<div class="step-list">
							<span class="step-number step-act">4</span>
						</div>
						<div class="step-list-html-text step-act">设置效果图</div>
					</div>
				</div> -->
				<div class="zoomBox">
					<i class="layui-icon layui-icon-reduce-circle" onclick="zoomFn('in')"></i>
					<i class="layui-icon layui-icon-add-circle" onclick="zoomFn('out')"></i>
				</div>
				<div
					style="width: 100%;text-align: center;font-size: 25px;color: #000;font-weight: bold;margin-top: 15px;letter-spacing: 5px;"
					id="mySeat">
				</div>
				<!-- 座位遮罩层 -->
				<!-- <div class="seatFixed"></div> -->
				<div class="seatBoxContent" id="seatBoxContent">


					<div class="seatBox" id="seatBox" style="width: auto;background: #e5e5e5;" zoom="1">

						<div class="dragContent">
							<div class="dragContent freeContainerModule">
								<!-- 组件  dragItemClicked coorClick-->
								<div class="ui-draggable freeDragItem " id="timestamp" onclick="freeDragli(this,event)">
									<div name="paper">
										<div style="position: relative;" class="freeBtbBox">
											<div class="freeweb_btn"><a src="#">组件</a></div>
											<div class="coor3 "></div>
											<img class="del_img" src="images/delete_hover.png" onclick="freeDeleteImg(this)" />
										</div>
									</div>
								</div>
							</div>
						</div>


					</div>
				</div>
			</div>
		</div>


		<!-- 灰色背景 -->
		<div id="fade" class="black_overlay"></div>



		<!-- <script src="js/jquery-1.7.2.min.js"></script> -->
		<script src="js/jquery-3.6.3.min.js"></script>
		<!-- <script src="js/jquery-ui-1.9.2.custom.min.js"></script> -->
		<script src="js/moveZoom.js"></script>
		<script src="js/jquery-ui.js"></script>
		<script src="js/dragModule.js"></script>


		<script>
			$(function() {
				// 动态加载可放大功能
				$.getScript('js/dragModule.js', function() {});
				// 动态加载可放大功能
				$.getScript('js/moveZoom.js', function() {
					all();
				});
			})
		</script>

		<script src="translateJs/translate.js"></script>
		<script src="translateJs/translateCommon.js"></script>
		<script src="js/seat.js"></script>
		<script src="layui/layui.js"></script>
		<script>
			var layer = "";
			var slider = "";
			var noGroupNumber = 0;
			var noGroupHasAssign = 0; // 未分组已分配数量
			var form = "";
			var channelNumber = 0; // 批量通道人数
			var canUseNumber = 0; // 可用分配数量
			var canUseGroup = []; // 可用分配数组
			var channelSelectArray = JSON.parse(localStorage.getItem("channelSelectArrayStorage")) || []; // 已经分配过的通道

			var dragModuleWidth = 100;


			layui.use(function() {
				form = layui.form;
				var element = layui.element;
				layer = layui.layer;

				element.on('tab(tab-hash)', function(obj) {
					let thisIndex = obj.index
					if (thisIndex == 1) {
						$(".dragItemClicked").removeClass("dragItemClicked")
						$(".coorClick").removeClass("coorClick")
					}
				});

			});


			// 放大缩小
			function zoomFn(obj) {
				let zoomVal = Number($("#seatBox").attr("zoom"))
				if (obj == "in") {
					if (zoomVal >= 0.2) {
						zoomVal = zoomVal - 0.1
						$("#seatBox").css({
							transform: "scale(" + zoomVal + ")"
						})
					}

				} else if (obj == "out") {

					if (zoomVal < 1) {
						zoomVal = zoomVal + 0.1
						$("#seatBox").css({
							transform: "scale(" + zoomVal + ")"
						})
					}

				}
				$("#seatBox").attr("zoom", zoomVal)
			}
		</script>
		<script>
			let seatRows = localStorage.getItem("seatRows");
			let seatCols = localStorage.getItem("seatCols");

			// var groupObject = JSON.parse(localStorage.getItem('groupArrayObject')) || []
			try {
				var datas = JSON.parse(localStorage.getItem("seats"));
				var selected = JSON.parse(localStorage.getItem("selected"));
			} catch (e) {}
			var seats = $.seats({
				box: "#seatBox",
				rows: seatRows,
				cols: seatCols,
				size: 20, //座位的尺寸，单位：像素	
				hall_id: 10, //厅，如：5号厅
				thre_id: 20, //剧场，如：大地电影院中天广场沃尔玛店
				movie_id: 30, //场次ID
				step: 5,
				maxTicketNumber: 1000, //最多购票张数
				datas: datas,
				selected: selected || {},
				onSelected: function(seat) {

					/*************/
					var id = "selected_" + seat.row + "_" + seat.col;
					// 获取分区名称
					console.log(seat.color)
					let getGroupName = seat.groupName
					let getGroupLength = $(`span[name='${getGroupName}']`).length
					// console.log(getGroupLength)

					// 判断是否已经分配的座位
					let hasDisabled = $(`#${seat.row}_${seat.col}`).find("span").hasClass("disabledCancel")
					console.log(hasDisabled)
					// var selectedHtml = ''
					// 获取真实的座位号（去除空格的)
					let realSeat = $(`#${seat.row}_${seat.col}`).attr("seat").split("-")
					console.log(realSeat)

					/*********************************/
					return true;
				},
				onUnselected: function(seat) {
					var id = "selected_" + seat.row + "_" + seat.col;
					$("#" + id).remove();
				},
				onerror: function(msg) {
					// JDialog.msg({
					// 	type: 'error',
					// 	content: msg,
					// 	container: "#seatBox"
					// });
					layer.msg(msg, {
						icon: 2,
						time: 1000 // 设置 2 秒后自动关闭
					});
				}
			});

			// 所属区域高亮

			$(function() {
				// let hrefSrc = window.location.href.split("?")[1].split("-")
				let row = 4
				let col = 18
				let seat = `${row}-${col}`
				console.log(seat)
				let groupName = $(`td[seat='${seat}']`).find("span").attr("name")
				console.log(groupName)
				// 回显座位到页面
				$("#mySeat").html(`我的座位：${groupName}区 - ${row}排${col}座`)

				// $(`td[seat='${seat}']`).find("span").html(seat).css({
				// 	color: "#fff",
				// 	fontSize: "10px",
				// 	lineHeight: "20px"
				// })
				// console.log($("td").find(`span[name='${groupName}']`).)
				let groupBg = $("td").find(`span[name='${groupName}']`).attr("style").split("background")[1].replace(":", "")
				groupBg = groupBg.replace(";", "")
				// 遍历所有可选座位
				$("td").each(function() {
					let backNone = $(this).find("span").attr("style")
					if (!backNone.includes("background: none;")) {
						$(this).find("span").css({
							background: '#d7d7d7'
						})
					}
				})

				// $("td").find("span").css({
				// 	background: '#d7d7d7'
				// })
				$("td").find("span").removeClass("disabled")


				$("td").find(`span[name='${groupName}']`).css({
					background: groupBg,
					opacity: 0.5
				})
				$(`td[seat='${seat}']`).find("span").addClass("disabled")
				$(`td[seat='${seat}']`).find("span").css({
					opacity: 1
				})
			})
		</script>



	</body>
</html>