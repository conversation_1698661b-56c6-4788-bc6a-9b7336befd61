<!DOCTYPE html>
<html lang="ch">
	<head>
		<title>设置分区</title>
		<meta name="viewport" content="width=device-width, initial-scale=1" />
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<meta name="renderer" content="webkit">
		<meta charset="UTF-8">
		<link rel="stylesheet" type="text/css" href="css/reset.css">
		<link rel="stylesheet" href="css/translateCommon.css">
		<link rel="stylesheet" href="layui/css/layui.css" />
		<link rel="stylesheet" href="css/stepPlug.css" />
		<link rel="stylesheet" href="css/seat3.css" />
		<link rel="stylesheet" href="css/step2only.css" />

		<style>
			/* 隐藏原有的页面结构，只保留Canvas */
			#header, .new-content-top, .sidebar-toolbar {
				display: none !important;
			}

			/* Canvas全屏显示 */
			#seatBox {
				position: fixed !important;
				top: 0 !important;
				left: 0 !important;
				right: 0 !important;
				bottom: 0 !important;
				width: 100vw !important;
				height: 100vh !important;
				z-index: 1 !important;
				background: white !important;
			}

			/* 右上角导航按钮 */
			.top-nav-buttons {
				position: fixed;
				top: 20px;
				right: 20px;
				z-index: 30;
				display: flex;
				gap: 10px;
			}

			.nav-btn {
				padding: 10px 20px;
				border: none;
				border-radius: 6px;
				font-size: 14px;
				cursor: pointer;
				transition: all 0.3s ease;
				box-shadow: 0 2px 8px rgba(0,0,0,0.1);
			}

			.nav-btn-secondary {
				background: #6B7280;
				color: white;
			}

			.nav-btn-secondary:hover {
				background: #4B5563;
			}

			.nav-btn-primary {
				background: #3B82F6;
				color: white;
			}

			.nav-btn-primary:hover {
				background: #2563EB;
			}

			/* 左侧分区工具栏 */
			.zone-toolbar {
				position: fixed;
				top: 20px;
				left: 20px;
				background: rgba(255, 255, 255, 0.95);
				padding: 15px;
				border-radius: 8px;
				box-shadow: 0 4px 12px rgba(0,0,0,0.15);
				z-index: 25;
				min-width: 280px;
				max-height: calc(100vh - 40px);
				overflow-y: auto;
			}

			.zone-toolbar h3 {
				margin: 0 0 15px 0;
				font-size: 16px;
				color: #333;
				border-bottom: 2px solid #3B82F6;
				padding-bottom: 8px;
			}

			.zone-section {
				margin-bottom: 20px;
			}

			.zone-section-title {
				font-size: 14px;
				font-weight: bold;
				color: #555;
				margin-bottom: 10px;
			}

			.zone-btn-group {
				display: flex;
				gap: 8px;
				flex-wrap: wrap;
			}

			.zone-btn {
				padding: 8px 12px;
				border: 1px solid #ddd;
				background: white;
				border-radius: 4px;
				cursor: pointer;
				font-size: 12px;
				transition: all 0.2s ease;
			}

			.zone-btn:hover {
				background: #f0f0f0;
				border-color: #3B82F6;
			}

			.zone-btn.active {
				background: #3B82F6;
				color: white;
				border-color: #3B82F6;
			}

			.zone-form {
				background: #f8f9fa;
				padding: 12px;
				border-radius: 6px;
				margin-top: 10px;
			}

			.zone-form-group {
				margin-bottom: 12px;
			}

			.zone-form-group label {
				display: block;
				font-size: 12px;
				color: #555;
				margin-bottom: 4px;
			}

			.zone-form-group input {
				width: 100%;
				padding: 6px 8px;
				border: 1px solid #ddd;
				border-radius: 4px;
				font-size: 12px;
				box-sizing: border-box;
			}

			.zone-list {
				max-height: 200px;
				overflow-y: auto;
			}

			.zone-item {
				background: white;
				border: 1px solid #e6e6e6;
				border-radius: 4px;
				padding: 8px;
				margin-bottom: 6px;
				font-size: 11px;
			}

			.zone-item-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 4px;
			}

			.zone-item-info {
				color: #666;
				display: flex;
				align-items: center;
				gap: 8px;
			}

			.zone-color-indicator {
				display: inline-block;
				width: 12px;
				height: 12px;
				border-radius: 2px;
				vertical-align: middle;
			}

			.zone-delete-btn {
				background: #ff4d4f;
				color: white;
				border: none;
				border-radius: 2px;
				padding: 2px 6px;
				cursor: pointer;
				font-size: 10px;
			}

			.zone-info-display {
				background: #f0f8ff;
				padding: 10px;
				border-radius: 6px;
				font-size: 12px;
			}

			.zone-info-row {
				display: flex;
				justify-content: space-between;
				margin-bottom: 4px;
			}

			.zone-info-label {
				color: #666;
			}

			.zone-info-value {
				font-weight: bold;
				color: #333;
			}
		</style>
	</head>
	<body style="margin: 0; padding: 0; overflow: hidden;">
		<!-- 右上角导航按钮 -->
		<div class="top-nav-buttons">
			<button type="button" class="nav-btn nav-btn-secondary" onclick="location.href='step2.html'">上一步</button>
			<button type="button" class="nav-btn nav-btn-primary" onclick="saveZones()">保存分区设置</button>
		</div>

		<!-- 左侧分区工具栏 -->
		<div class="zone-toolbar">
			<h3>🎨 分区设置</h3>

			<!-- 工具选择 -->
			<div class="zone-section">
				<div class="zone-section-title">选择工具</div>
				<div class="zone-btn-group">
					<button class="zone-btn active" id="selectTool" data-tool="select">选择座位</button>
					<button class="zone-btn" id="boxSelectTool" data-tool="boxselect">框选座位</button>
				</div>
			</div>

			<!-- 分区操作 -->
			<div class="zone-section">
				<div class="zone-section-title">分区操作</div>
				<div class="zone-btn-group">
					<button class="zone-btn" onclick="showZoneForm()">创建分区</button>
					<button class="zone-btn" onclick="clearAllZones()">清除所有分区</button>
				</div>
			</div>

			<!-- 信息显示 -->
			<div class="zone-section">
				<div class="zone-section-title">当前状态</div>
				<div class="zone-info-display">
					<div class="zone-info-row">
						<span class="zone-info-label">已选座位:</span>
						<span class="zone-info-value" id="selectedCount">0</span>
					</div>
					<div class="zone-info-row">
						<span class="zone-info-label">总座位数:</span>
						<span class="zone-info-value" id="totalSeats">0</span>
					</div>
					<div class="zone-info-row">
						<span class="zone-info-label">分区数量:</span>
						<span class="zone-info-value" id="zoneCount">0</span>
					</div>
				</div>
			</div>

			<!-- 分区表单 -->
			<div class="zone-section" id="zoneFormSection" style="display: none;">
				<div class="zone-section-title">创建新分区</div>
				<div class="zone-form">
					<div class="zone-form-group">
						<label for="zoneName">分区名称</label>
						<input type="text" id="zoneName" placeholder="分区1" />
					</div>
					<div class="zone-form-group">
						<label for="zoneColor">分区颜色</label>
						<input type="color" id="zoneColor" value="#1890ff" />
					</div>
					<div class="zone-btn-group">
						<button class="zone-btn" onclick="createZone()" style="background: #52c41a; color: white;">创建</button>
						<button class="zone-btn" onclick="cancelZone()">取消</button>
					</div>
				</div>
			</div>

			<!-- 分区列表 -->
			<div class="zone-section">
				<div class="zone-section-title">已创建分区</div>
				<div class="zone-list" id="zoneList">
					<p style="color: #999; text-align: center; font-size: 12px;">暂无分区</p>
				</div>
			</div>
		</div>

		<div id="section">
			<!-- header -->
			<div id="header" style="background: #001529; color: white; padding: 20px; text-align: center;">
				<h1 style="margin: 0; color: white;">Step3 - 座位分区设置</h1>
			</div>

			<!-- 主要模块 -->
			<div class="c_right">
				<div class="main">
					<div class="new-content-top">
						<span class="layui-breadcrumb" lay-separator=">">
							<a href="">首页</a>
							<a href="">活动列表</a>
							<a href="">座位规划</a>
							<a><cite>设置分区</cite></a>
						</span>
						<div class="content-top-btn">
							<button class="layui-btn" type="button" onclick="location.href='step2.html'">上一步</button>
							<button class="layui-btn layui-btn-normal" type="button" onclick="saveZones()">保存分区设置</button>
						</div>
					</div>

					<div class="main-content" style="overflow: hidden !important; display: flex;">
						<!-- 左侧工具栏（与Step2完全一致） -->
						<div class="sidebar-toolbar" id="sidebarToolbar">
							<!-- 导航 & 进度 -->
							<div class="toolbar-section">
								<div class="section-title">导航 (步骤3/3)</div>
								<div class="btn-grid">
									<button type="button" class="sidebar-btn primary" onclick="location.href='step2.html'">
										<i class="layui-icon layui-icon-left"></i>
										<span>上一步</span>
									</button>
									<button type="button" class="sidebar-btn primary" onclick="saveZones()">
										<i class="layui-icon layui-icon-ok"></i>
										<span>保存设置</span>
									</button>
								</div>
							</div>

							<!-- 分区工具 -->
							<div class="toolbar-section">
								<div class="section-title">分区工具</div>
								<div class="btn-grid">
									<button type="button" class="sidebar-btn tool-btn active" id="selectTool" data-tool="select">
										<i class="layui-icon layui-icon-ok-circle"></i>
										<span>选择座位</span>
									</button>
									<button type="button" class="sidebar-btn tool-btn" id="boxSelectTool" data-tool="boxselect">
										<i class="layui-icon layui-icon-screen-full"></i>
										<span>框选座位</span>
									</button>
								</div>
								<div class="btn-grid">
									<button type="button" class="sidebar-btn" id="createZoneBtn">
										<i class="layui-icon layui-icon-add-1"></i>
										<span>创建分区</span>
									</button>
									<button type="button" class="sidebar-btn" id="clearZonesBtn">
										<i class="layui-icon layui-icon-delete"></i>
										<span>清除所有分区</span>
									</button>
								</div>
							</div>



							<!-- 信息显示 -->
							<div class="toolbar-section">
								<div class="section-title">信息</div>
								<div class="info-display">
									<div class="info-row">
										<span class="info-label">已选:</span>
										<span class="info-value" id="selectedCount">0</span>
									</div>
									<div class="info-row">
										<span class="info-label">总计:</span>
										<span class="info-value" id="totalSeats">0</span>
									</div>
									<div class="info-row">
										<span class="info-label">分区:</span>
										<span class="info-value" id="zoneCount">0</span>
									</div>
								</div>
							</div>

							<!-- 分区表单（在工具栏中） -->
							<div class="toolbar-section" id="zoneFormSection" style="display: none;">
								<div class="section-title">创建分区</div>
								<div class="form-group">
									<label for="zoneName">分区名称</label>
									<input type="text" id="zoneName" placeholder="分区1" class="layui-input" title="输入分区名称" />
								</div>
								<div class="form-group">
									<label for="zoneColor">分区颜色</label>
									<input type="color" id="zoneColor" value="#1890ff" class="layui-input" title="选择分区颜色" />
								</div>
								<div class="btn-grid">
									<button type="button" class="sidebar-btn primary" onclick="createZone()">
										<i class="layui-icon layui-icon-ok"></i>
										<span>创建</span>
									</button>
									<button type="button" class="sidebar-btn" onclick="cancelZone()">
										<i class="layui-icon layui-icon-close"></i>
										<span>取消</span>
									</button>
								</div>
							</div>

							<!-- 分区列表（长期显示） -->
							<div class="toolbar-section">
								<div class="section-title">已创建分区</div>
								<div id="zoneList" style="max-height: 200px; overflow-y: auto;">
									<p style="color: #999; text-align: center; font-size: 12px;">暂无分区</p>
								</div>
							</div>
						</div>

						<!-- 座位画布区域（占满剩余空间） -->
						<div class="seat-canvas-container" style="flex: 1; position: relative; background: #fafafa; overflow: hidden;">
							<div id="seatBox" style="width: 100%; height: 100%; position: relative; overflow: hidden;">
								<canvas id="seatCanvas"></canvas>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- JavaScript -->
		<script src="layui/layui.js"></script>
		<script src="js/jquery-3.6.3.min.js"></script>
		<script src="SeatMapEngine.js"></script>
		<script>
			// 全局变量
			let engine;
			let seatsData = {};
			let selectedSeats = new Set();
			let zones = [];
			let currentTool = 'select';

			// 预设颜色
			const presetColors = [
				'#1890ff', '#52c41a', '#faad14', '#f5222d', 
				'#722ed1', '#13c2c2', '#eb2f96', '#fa541c'
			];

			// 创建测试数据
			function createTestData() {
				const seatRows = localStorage.getItem("seatRows") || "8";
				const seatCols = localStorage.getItem("seatCols") || "12";
				
				localStorage.setItem("seatRows", seatRows);
				localStorage.setItem("seatCols", seatCols);
				
				// 创建座位数据（总是重新创建以确保完整性）
				const testSeats = {};
				for (let row = 0; row < parseInt(seatRows); row++) {
					for (let col = 0; col < parseInt(seatCols); col++) {
						testSeats[row + "_" + col] = {
							row: row,
							col: col
						};
					}
				}
				seatsData = testSeats;

				// 加载已有分区数据（如果存在）
				const savedSeats = localStorage.getItem("seats");
				if (savedSeats) {
					try {
						const savedData = JSON.parse(savedSeats);
						// 合并已有的分区信息到新创建的座位数据中
						Object.keys(savedData).forEach(seatKey => {
							if (seatsData[seatKey] && savedData[seatKey].zoneName) {
								seatsData[seatKey].zoneName = savedData[seatKey].zoneName;
								seatsData[seatKey].color = savedData[seatKey].color;
							}
						});
					} catch (e) {
						console.log('Failed to load saved seat data:', e);
					}
				}

				// 加载已有分区数据
				const savedZones = localStorage.getItem('zones');
				if (savedZones) {
					zones = JSON.parse(savedZones);
				}

				return { rows: parseInt(seatRows), cols: parseInt(seatCols) };
			}

			$(document).ready(function() {
				// 创建测试数据
				const { rows, cols } = createTestData();

				// 使用已存在的Canvas元素
				const canvas = document.getElementById('seatCanvas');
				const container = document.querySelector("#seatBox");

				// 设置Canvas样式，确保完全适应容器
				canvas.style.width = '100%';
				canvas.style.height = '100%';
				canvas.style.display = 'block';
				canvas.style.position = 'absolute';
				canvas.style.top = '0';
				canvas.style.left = '0';

				// 初始化SeatMapEngine
				engine = new SeatMapEngine(canvas, {
					seatSize: 20,
					seatSpacing: 4,
					showSeatNumbers: true,
					enableRoundedCorners: true,
					showHeaders: true,
					onSeatClick: handleSeatClick,
					onSeatHover: handleSeatHover,
					onViewChange: handleViewChange
				});

				// 确保Canvas完全适应容器
				fixCanvasSize(canvas, container);

				// 监听窗口大小变化
				window.addEventListener('resize', () => {
					fixCanvasSize(canvas, container);
					centerSeats(engine);
				});

				// 生成座位
				engine.generateSeats(rows, cols);

				// 居中显示座位
				centerSeats(engine);

				// 设置座位数据
				engine.seats.forEach(seat => {
					const seatKey = seat.row + "_" + seat.col;
					if (seatsData[seatKey]) {
						seat.status = 'available';
						// 如果座位已有分区信息，设置颜色
						if (seatsData[seatKey].color) {
							seat.groupColor = seatsData[seatKey].color;
							seat.groupName = seatsData[seatKey].zoneName;
						}
					} else {
						seat.status = 'empty';
					}
				});

				// 设置点击处理
				engine.onSeatClick = handleSeatClick;

				// 重新启用鼠标事件（修复点击功能）
				engine.canvas.addEventListener('mousedown', function(e) {
					engine.handleMouseDown.call(engine, e);
				});
				engine.canvas.addEventListener('mousemove', function(e) {
					engine.handleMouseMove.call(engine, e);
				});
				engine.canvas.addEventListener('mouseup', function(e) {
					engine.handleMouseUp.call(engine, e);
				});

				// 初始化界面
				updateInfo();
				updateZoneList();
				setupEventListeners();

				// 渲染
				engine.render();
			});

			// 座位点击处理（统一处理点击和框选）
			function handleSeatClick(seat) {
				// 只在选择模式下处理座位点击（包括select和boxselect）
				if (currentTool !== 'select' && currentTool !== 'boxselect') return;

				const seatKey = seat.row + "_" + seat.col;
				if (!seatsData[seatKey]) return;

				if (currentTool === 'select') {
					// 点击选择模式：切换座位状态
					if (selectedSeats.has(seatKey)) {
						selectedSeats.delete(seatKey);
						// 恢复原有状态
						if (seatsData[seatKey].color) {
							seat.groupColor = seatsData[seatKey].color;
							seat.groupName = seatsData[seatKey].zoneName;
							seat.status = 'available';
						} else {
							seat.groupColor = null;
							seat.groupName = null;
							seat.status = 'available';
						}
					} else {
						selectedSeats.add(seatKey);
						// 设置选中状态，如果有分区颜色则加深显示
						seat.status = 'selected';
						if (seatsData[seatKey].color) {
							// 保持分区信息，但状态为选中
							seat.groupColor = seatsData[seatKey].color;
							seat.groupName = seatsData[seatKey].zoneName;
						}
					}
				} else if (currentTool === 'boxselect') {
					// 框选模式：只添加到选择中，不取消已选中的座位
					if (!selectedSeats.has(seatKey)) {
						selectedSeats.add(seatKey);
						seat.status = 'selected';
						// 如果座位有分区颜色，保持分区信息但状态为选中
						if (seatsData[seatKey].color) {
							seat.groupColor = seatsData[seatKey].color;
							seat.groupName = seatsData[seatKey].zoneName;
						}
					}
					// 如果座位已经选中，保持选中状态，不做任何操作
				}

				engine.render();
				updateInfo();
			}



			// 更新信息显示
			function updateInfo() {
				const totalSeats = Object.keys(seatsData).length;
				const selectedCount = selectedSeats.size;

				document.getElementById('selectedCount').textContent = selectedCount;
				document.getElementById('totalSeats').textContent = totalSeats;
				document.getElementById('zoneCount').textContent = zones.length;
			}

			// 设置事件监听器
			function setupEventListeners() {
				// 工具切换 - 支持新的工具栏按钮
				document.querySelectorAll('.tool-btn, .zone-btn[data-tool]').forEach(btn => {
					btn.addEventListener('click', function() {
						// 移除所有工具按钮的active状态
						document.querySelectorAll('.tool-btn, .zone-btn[data-tool]').forEach(b => b.classList.remove('active'));
						this.classList.add('active');
						currentTool = this.dataset.tool;
					});
				});

				// 操作按钮 - 兼容新旧按钮ID
				const createBtn = document.getElementById('createZoneBtn') || document.querySelector('[onclick="showZoneForm()"]');
				const clearBtn = document.getElementById('clearZonesBtn') || document.querySelector('[onclick="clearAllZones()"]');

				if (createBtn && !createBtn.onclick) {
					createBtn.addEventListener('click', showZoneForm);
				}
				if (clearBtn && !clearBtn.onclick) {
					clearBtn.addEventListener('click', clearAllZones);
				}
			}



			// 显示分区表单
			function showZoneForm() {
				if (selectedSeats.size === 0) {
					layui.use('layer', function(){
						const layer = layui.layer;
						layer.msg('请先选择要分区的座位！', {icon: 2});
					});
					return;
				}

				// 显示分区表单
				document.getElementById('zoneFormSection').style.display = 'block';

				// 自动设置默认分区名称（按顺序）
				const defaultName = `分区${zones.length + 1}`;
				document.getElementById('zoneName').value = defaultName;

				// 自动设置随机颜色
				document.getElementById('zoneColor').value = getRandomZoneColor();

				// 聚焦到名称输入框，方便用户修改
				document.getElementById('zoneName').focus();
				document.getElementById('zoneName').select(); // 选中文本，方便用户直接输入新名称
			}

			// 取消分区
			function cancelZone() {
				document.getElementById('zoneFormSection').style.display = 'none';
				document.getElementById('zoneName').value = '';
				document.getElementById('zoneColor').value = '#1890ff';
			}

			// 创建分区
			function createZone() {
				const name = document.getElementById('zoneName').value.trim();
				const color = document.getElementById('zoneColor').value;

				if (!name) {
					layui.use('layer', function(){
						const layer = layui.layer;
						layer.msg('请输入分区名称！', {icon: 2});
					});
					document.getElementById('zoneName').focus();
					return;
				}

				// 检查分区名称是否重复
				if (zones.some(zone => zone.name === name)) {
					layui.use('layer', function(){
						const layer = layui.layer;
						layer.msg('分区名称已存在，请使用其他名称！', {icon: 2});
					});
					document.getElementById('zoneName').focus();
					return;
				}

				// 处理座位转移：从原分区中移除被选中的座位
				const affectedZones = new Set(); // 记录受影响的分区
				selectedSeats.forEach(seatKey => {
					if (seatsData[seatKey] && seatsData[seatKey].zoneName) {
						// 找到原分区并记录
						const originalZoneName = seatsData[seatKey].zoneName;
						affectedZones.add(originalZoneName);

						// 从原分区的座位列表中移除这个座位
						zones.forEach(zone => {
							if (zone.name === originalZoneName) {
								zone.seats = zone.seats.filter(seat => seat !== seatKey);
							}
						});
					}
				});

				// 创建新分区
				const zone = {
					id: Date.now().toString(),
					name: name,
					color: color,
					seats: Array.from(selectedSeats)
				};

				zones.push(zone);

				// 更新座位数据
				selectedSeats.forEach(seatKey => {
					if (seatsData[seatKey]) {
						seatsData[seatKey].zoneName = name;
						seatsData[seatKey].color = color;
						// 移除价格字段
						delete seatsData[seatKey].price;
					}
				});

				// 自动清理空分区
				const emptyZones = [];
				zones.forEach(zone => {
					if (zone.seats.length === 0) {
						emptyZones.push(zone);
					}
				});

				// 移除空分区
				emptyZones.forEach(emptyZone => {
					zones = zones.filter(zone => zone.id !== emptyZone.id);
					console.log(`自动删除空分区: ${emptyZone.name}`);
				});

				// 更新座位颜色
				engine.seats.forEach(seat => {
					const seatKey = seat.row + "_" + seat.col;
					if (selectedSeats.has(seatKey)) {
						seat.groupColor = color;
						seat.groupName = name;
						seat.status = 'available';
					}
				});

				selectedSeats.clear();
				updateInfo();
				updateZoneList();
				cancelZone();

				// 强制刷新显示 - 重新设置Canvas来触发重绘
				setTimeout(() => {
					// 方法1: 重新设置Canvas尺寸强制刷新
					const canvas = engine.canvas;
					const currentWidth = canvas.width;
					const currentHeight = canvas.height;
					canvas.width = currentWidth;
					canvas.height = currentHeight;

					// 重新设置设备像素比缩放
					const dpr = window.devicePixelRatio || 1;
					engine.ctx.scale(dpr, dpr);

					// 调用渲染
					engine.render();
				}, 50);



				layui.use('layer', function(){
					const layer = layui.layer;
					layer.msg(`分区"${name}"创建成功！`, {icon: 1});
				});
			}



			// 更新分区列表
			function updateZoneList() {
				const zoneList = document.getElementById('zoneList');

				if (zones.length === 0) {
					zoneList.innerHTML = '<p style="color: #999; text-align: center; font-size: 12px;">暂无分区</p>';
					return;
				}

				let html = '';
				zones.forEach(zone => {
					html += `
						<div style="background: white; border: 1px solid #e6e6e6; border-radius: 4px; padding: 8px; margin-bottom: 8px; font-size: 11px;">
							<div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 4px;">
								<strong>${zone.name}</strong>
								<button onclick="deleteZone('${zone.id}')" style="background: #ff4d4f; color: white; border: none; border-radius: 2px; padding: 2px 6px; cursor: pointer; font-size: 10px;">删除</button>
							</div>
							<div style="color: #666;">
								座位：${zone.seats.length}个
								<span style="display: inline-block; width: 12px; height: 12px; background: ${zone.color}; border-radius: 2px; margin-left: 5px; vertical-align: middle;"></span>
							</div>
						</div>
					`;
				});
				zoneList.innerHTML = html;
			}

			// 删除分区
			function deleteZone(zoneId) {
				const zone = zones.find(z => z.id === zoneId);
				if (!zone) return;

				layui.use('layer', function(){
					const layer = layui.layer;
					layer.confirm(`确定要删除分区"${zone.name}"吗？`, {
						btn: ['确定删除', '取消']
					}, function(index){
						layer.close(index);

						// 从分区列表中移除
						zones = zones.filter(z => z.id !== zoneId);

						// 清除座位的分区信息
						zone.seats.forEach(seatKey => {
							if (seatsData[seatKey]) {
								delete seatsData[seatKey].zoneName;
								delete seatsData[seatKey].color;
							}
						});

						// 更新座位显示
						engine.seats.forEach(seat => {
							const seatKey = seat.row + "_" + seat.col;
							if (zone.seats.includes(seatKey)) {
								seat.groupColor = null;
								seat.groupName = null;
							}
						});

						// 更新界面
						updateInfo();
						updateZoneList();
						engine.render();

						layer.msg(`分区"${zone.name}"已删除`, {icon: 1});
					});
				});
			}

			// 清除所有分区
			function clearAllZones() {
				if (zones.length === 0) {
					layui.use('layer', function(){
						const layer = layui.layer;
						layer.msg('没有分区可清除！', {icon: 2});
					});
					return;
				}

				layui.use('layer', function(){
					const layer = layui.layer;
					layer.confirm(`确定要清除所有 ${zones.length} 个分区吗？`, {
						btn: ['确定清除', '取消']
					}, function(index){
						layer.close(index);

						// 清除所有分区
						zones = [];

						// 清除所有座位的分区信息
						Object.keys(seatsData).forEach(seatKey => {
							delete seatsData[seatKey].zoneName;
							delete seatsData[seatKey].color;
						});

						// 更新座位显示
						engine.seats.forEach(seat => {
							seat.groupColor = null;
							seat.groupName = null;
						});

						// 更新界面
						updateInfo();
						updateZoneList();
						engine.render();

						layer.msg('所有分区已清除', {icon: 1});
					});
				});
			}

			// 随机分配分区颜色
			function getRandomZoneColor() {
				const usedColors = zones.map(zone => zone.color);
				const availableColors = presetColors.filter(color => !usedColors.includes(color));

				if (availableColors.length > 0) {
					return availableColors[Math.floor(Math.random() * availableColors.length)];
				} else {
					// 如果预设颜色都用完了，生成随机颜色
					return '#' + Math.floor(Math.random()*16777215).toString(16);
				}
			}

			// 保存分区设置
			function saveZones() {
				if (zones.length === 0) {
					layui.use('layer', function(){
						const layer = layui.layer;
						layer.msg('请至少创建一个分区！', {icon: 2});
					});
					return;
				}

				// 检查是否所有座位都已分区
				const totalSeats = Object.keys(seatsData).length;
				const assignedSeats = zones.reduce((total, zone) => total + zone.seats.length, 0);

				if (assignedSeats < totalSeats) {
					layui.use('layer', function(){
						const layer = layui.layer;
						layer.confirm(`还有 ${totalSeats - assignedSeats} 个座位未分区，确定要保存吗？`, {
							btn: ['确定保存', '继续编辑']
						}, function(index){
							layer.close(index);
							doSaveZones();
						});
					});
				} else {
					doSaveZones();
				}
			}

			// 执行保存操作
			function doSaveZones() {
				try {
					// 保存到localStorage
					localStorage.setItem('seats', JSON.stringify(seatsData));
					localStorage.setItem('zones', JSON.stringify(zones));

					// 保存分区统计信息
					const zoneStats = {
						totalZones: zones.length,
						totalSeats: Object.keys(seatsData).length,
						assignedSeats: zones.reduce((total, zone) => total + zone.seats.length, 0),
						saveTime: new Date().toISOString()
					};
					localStorage.setItem('zoneStats', JSON.stringify(zoneStats));

					// 显示成功消息
					layui.use('layer', function(){
						const layer = layui.layer;
						layer.msg('分区设置已保存！', {icon: 1});
					});

				} catch (error) {
					layui.use('layer', function(){
						const layer = layui.layer;
						layer.msg('保存失败，请重试！', {icon: 2});
					});
				}
			}

			// Canvas尺寸修复函数（与Step2一致）
			function fixCanvasSize(canvas, container) {
				const seatBoxElement = document.getElementById('seatBox');
				const seatBoxRect = seatBoxElement.getBoundingClientRect();
				const windowHeight = window.innerHeight;
				const seatBoxTop = seatBoxRect.top;
				const availableHeight = windowHeight - seatBoxTop;

				// 设置容器高度为剩余的全部空间
				seatBoxElement.style.height = availableHeight + 'px';

				// 获取更新后的容器尺寸
				const rect = container.getBoundingClientRect();
				const dpr = window.devicePixelRatio || 1;

				// 设置Canvas的实际尺寸（考虑设备像素比）
				canvas.width = rect.width * dpr;
				canvas.height = rect.height * dpr;

				// 设置Canvas的显示尺寸
				canvas.style.width = rect.width + 'px';
				canvas.style.height = rect.height + 'px';

				// 缩放绘图上下文以适应设备像素比
				const ctx = canvas.getContext('2d');
				ctx.scale(dpr, dpr);

				// 通知引擎Canvas尺寸已更改
				if (engine && engine.handleResize) {
					engine.handleResize();
				}
			}

			// 居中显示座位（与Step2一致）
			function centerSeats(engine) {
				if (!engine || !engine.seats || engine.seats.length === 0) {
					return;
				}

				// 计算座位区域的边界
				let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;

				engine.seats.forEach(seat => {
					const x = seat.col * (engine.seatSize + engine.seatSpacing);
					const y = seat.row * (engine.seatSize + engine.seatSpacing);

					minX = Math.min(minX, x);
					minY = Math.min(minY, y);
					maxX = Math.max(maxX, x + engine.seatSize);
					maxY = Math.max(maxY, y + engine.seatSize);
				});

				// 计算座位区域的中心
				const seatAreaWidth = maxX - minX;
				const seatAreaHeight = maxY - minY;
				const seatAreaCenterX = minX + seatAreaWidth / 2;
				const seatAreaCenterY = minY + seatAreaHeight / 2;

				// 计算Canvas的中心
				const canvas = engine.canvas;
				const canvasCenterX = canvas.width / (window.devicePixelRatio || 1) / 2;
				const canvasCenterY = canvas.height / (window.devicePixelRatio || 1) / 2;

				// 计算偏移量
				const offsetX = canvasCenterX - seatAreaCenterX;
				const offsetY = canvasCenterY - seatAreaCenterY;

				// 设置视图偏移
				if (engine.setViewOffset) {
					engine.setViewOffset(offsetX, offsetY);
				}

				// 重新渲染
				engine.render();
			}

			// 空函数占位符（与Step2兼容）
			function handleSeatHover(seat) {
				// 座位悬停处理
			}

			function handleViewChange() {
				// 视图变化处理
			}
		</script>
	</body>
</html>
