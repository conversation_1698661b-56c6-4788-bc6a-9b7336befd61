/*弹窗*/
.black_overlay{ 
    display: none; 
    position: fixed; 
    top: 0%; 
    left: 0%; 
    width: 100%; 
    height: 100%; 
    background-color: black; 
    z-index:9998; 
    -moz-opacity: 0.5; 
    opacity:.5; 
    filter: alpha(opacity=88); 
    overflow:hidden;
} 
.pop_xx {
    width: 17px;
    height: 17px;
    background: url(../images/pop_xx.jpg) no-repeat;
    background-size: contain;
    cursor: pointer;
    margin-top: 17px;
}

/*删除弹窗*/
.white_content2{ 
    display: none; 
    position: fixed; 
    top: 50%; 
    left: 50%; 
    width: 500px;
    height: 210px;
    transform: translate(-50%,-50%);
    background-color: white; 
    z-index:9999; 
} 
.white_content2 .title-one {
    height: 49px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #ddd;
    padding: 0 15px;
    line-height: 49px;
    font-size: 14px;
}
.white_content2 .title-two{
  width: 80%;
  height:60px;
  margin-top: 37px;
  margin-left: 10%;
}
.white_content2 .title-two img{
  width: 40px;
  height: 40px;
  float: left;
  margin-left: 90px;
}
.white_content2 .title-two p{
  float: left;
  font-size: 14px;
  margin-left: 20px;
  color: #666;
  margin-top: 5px;
}
.white_content2 .title-three{
  padding: 10px 0;
  text-align: center;
}
.white_content2 .title-three button{
  font-size: 12px;
  line-height: 29px;
}
.white_content2 .title-three .current{
  height: 30px;
  line-height: 30px;
  padding: 0 15px;
  background: #4079CB;
  border: 1px solid #4079CB;
  color: #fff;
}
.pop_btn1, .pop_btn2 {
    height: 30px;
    line-height: 30px;
    padding: 0 15px;
    background: #F7F7F7;
    border: 1px solid #DDDDDD;
    cursor: pointer;
    /*margin-left: 10px;*/
}

.white_content3{ 
    display: none; 
    position: fixed; 
    top: 50%; 
    left: 50%; 
    width: 500px;
    height: 210px;
    transform: translate(-50%,-50%);
    background-color: white; 
    z-index:9999; 
} 
.white_content3 .title-one {
    height: 49px;
    background-color: #f5f5f5;
    border-bottom: 1px solid #ddd;
    padding: 0 15px;
    line-height: 49px;
    font-size: 14px;
}
.white_content3 .title-two{
  width: 80%;
  height:60px;
  margin-top: 37px;
  margin-left: 10%;
}
.white_content3 .title-two img{
  width: 40px;
  height: 40px;
  float: left;
  margin-left: 130px;
}
.white_content3 .title-two p{
  float: left;
  font-size: 14px;
  margin-left: 20px;
  color: #666;
  margin-top: 5px;
}
.white_content3 .title-three{
  padding: 10px 0;
  text-align: center;
}
.white_content3 .title-three button{
  font-size: 12px;
  line-height: 29px;
}
.white_content3 .title-three .current{
  height: 30px;
  line-height: 30px;
  padding: 0 15px;
  background: #4079CB;
  border: 1px solid #4079CB;
  color: #fff;
}



.main{
    padding: 0;
}
.c_right {
    width: 100%;
    background: #fff;
}
.main .Body .top{
    width: 98%;
    height:40px;
    margin-left: 1%;
    background: #EAEDF2;
    margin-top: 50px;
    border-top:1px solid #ccc;
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
    box-sizing: border-box;
}
.main .Body .top ul li{
    height: 40px;
    float: left;
    font-weight: bold;
    text-align: center;
    line-height: 40px;
    border-right: 1px solid #ccc;
    box-sizing: border-box;
}
.main .Body .top ul li:nth-child(1){
    width: 15%;
}
.main .Body .top ul li:nth-child(2){
    width: 65%;
}
.main .Body .top ul li:nth-child(3){
    width: 10%;
}
.main .Body .top ul li:nth-child(4){
    width: 10%;
    border-right: 0px;
}

.main .Body-a .top-a{
    width: 98%;
    height:40px;
    margin-left: 1%;
    border-bottom:1px solid #ccc;
    border-left: 1px solid #ccc;
    border-right: 1px solid #ccc;
    box-sizing: border-box;
}
.main .Body-a .top-a ul li{
    height: 40px;
    float: left;
    text-align: center;
    line-height: 40px;
    border-right: 1px solid #ccc;
    box-sizing: border-box;
}
.main .Body-a .top-a ul li:nth-child(1){
    width: 15%;
}
.main .Body-a .top-a ul li:nth-child(2){
    width: 65%;
    text-align: left;
    padding-left: 5PX;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.main .Body-a .top-a ul li:nth-child(3){
    width: 10%;
}
.main .Body-a .top-a ul li:nth-child(4){
    width: 10%;
    border-right: 0px;
    text-decoration: underline;
    color: #0000FF;
    cursor: pointer;
}