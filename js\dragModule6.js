// 自由容器内部拖动
// 拖拽
$(function() {
	$(".freeContainerModuleOther .freeDragItem").draggable({
		containment: "parent",
		// connectToSortable: ".form_component,.web_frameUl",
		opacity: 1,
		start: function(event, ui) {
			// alert(123)
			$(".selection-box").remove()
			// console.log($(this).html())
			// console.log(event.target)
			// determineZone(event.target)
			// console.log("-- --")
			// console.log()
			// console.log("-- --")
			// $(this).find("a").text(determineZone(event.target))
		},
		stop: function(event, ui) {
			// console.log(456);
			// 拖动结束回显内容
			$(this).find("a").text(determineZone(event.target))
		}
	});


})

// 点击组件选中状态
function freeDragli(obj, event) {
	event.stopPropagation(event);
	$(".dragItemClicked").removeClass("dragItemClicked");
	$(".coorClick").removeClass("coorClick")
	// $(".form_component").find(".utility").removeClass('clicked');
	// $(obj).parent().parent().parent().parent().addClass('clicked');
	$(obj).addClass("dragItemClicked");
	// let hasCoor = $(obj).find(".coor3").hasClass("dragItemClicked")
	$(obj).find(".coor3").addClass("coorClick");
	// 动态加载可放大功能
	$.getScript('js/moveZoom.js', function() {
		// console.log(1123)
		all();
	});

	// 回显按钮文本
	let btnText = $(obj).find(".freeweb_btn a").text()
	$("#buttonModule").val(btnText)

	// 选中组件 显示设置页面
	$(".layui-tab-title").find("li").eq(0).addClass("layui-this").siblings().removeClass("layui-this");
	$(".layui-tab-content").find(".layui-tab-item").eq(0).addClass("layui-show").siblings().removeClass("layui-show");

	// 回显宽度
	let thisWidth = $(obj).find(".freeBtbBox").width();
	let thisHeight = $(obj).find(".freeBtbBox").height();
	// 动态回显slider组件
	// setWidthSlider.setValue(thisWidth)

	// step6 回显宽度高
	$(".moduleWidth").val(thisWidth)
	$(".moduleHeight").val(thisHeight)

	// 回显圆角
	let thisRaidus = $(obj).find(".freeBtbBox").find(".freeweb_btn>a").css('border-radius').split("px")[0];
	setRadiusSlider.setValue(thisRaidus)

	// 回显字体大小
	let thisFontSizes = $(obj).find(".freeBtbBox").find(".freeweb_btn>a").css('font-size').split("px")[0];
	setFontSizeSlider.setValue(thisFontSizes)

	// 回显边框宽度
	// let thisBorderWidth = $(obj).find(".freeBtbBox").find(".freeweb_btn>a").css('border-width').split("px")[0];
	// setBorderWidthSlider.setValue(thisBorderWidth)

	// 字体回显
	// let thisFontFamily = $(obj).find(".freeBtbBox").find(".freeweb_btn>a").css('font-family');
	// // console.log(thisFontFamily)
	// if (thisFontFamily == "SimHei" || thisFontFamily == "SimSun" || thisFontFamily == "KaiTi") {
	// 	$(".selectTextFontFamily").val(thisFontFamily)
	// } else {
	// 	$(".selectTextFontFamily").val("Microsoft YaHei")
	// }
	// 边框类型回显回显
	let thisBorderStyle = $(obj).find(".freeBtbBox").find(".freeweb_btn>a").css('border-style');
	// console.log(thisBorderStyle)
	$(".selectTextBorderStyle").val(thisBorderStyle)
	// //更新全部
	form.render();

	// 模块背景颜色回显
	let thisModuleBgColor = $(obj).find(".freeBtbBox").find(".freeweb_btn>a").css('background-color');
	colorpickerRender.render({ //
		elem: '#moduleBgColor',
		color: thisModuleBgColor, // hex
		alpha: true, // 开启透明度
		format: 'rgb',
		predefine: true, // 开启预定义颜色
		colors: ["rgb(152, 0, 0)", "rgb(255, 0, 0)", "rgb(255, 153, 0)", "rgb(255, 255, 0)", "rgb(0, 255, 0)",
			"rgb(0, 255, 255)", "rgb(74, 134, 232)", "rgb(0, 0, 255)", "rgb(255, 0, 255)"
		], //自定义预定义颜色项
		done: function(value) {
			console.log(value); // 当前选中的颜色值
			$(".dragItemClicked").find(".freeweb_btn>a").css({
				background: value
			})
		}
	});

	// 模块字体颜色
	let thisFontColor = $(obj).find(".freeBtbBox").find(".freeweb_btn>a").css('color');
	colorpickerRender.render({ //
		elem: '#btnTextColor',
		color: thisFontColor, // hex
		// alpha: true, // 开启透明度
		format: 'rgb',
		predefine: true, // 开启预定义颜色
		colors: ["rgb(152, 0, 0)", "rgb(255, 0, 0)", "rgb(255, 153, 0)", "rgb(255, 255, 0)", "rgb(0, 255, 0)",
			"rgb(0, 255, 255)", "rgb(74, 134, 232)", "rgb(0, 0, 255)", "rgb(255, 0, 255)"
		], //自定义预定义颜色项
		done: function(value) {
			// console.log(value); // 当前选中的颜色值
			$(".dragItemClicked").find(".freeweb_btn>a").css({
				color: value
			})
		}
	});
	// 模块边框回显
	let thisModuleBorderColor = $(obj).find(".freeBtbBox").find(".freeweb_btn>a").css('border-color');
	colorpickerRender.render({ //
		elem: '#moduleBorderColor',
		color: thisModuleBorderColor, // hex
		// alpha: true, // 开启透明度
		format: 'rgb',
		predefine: true, // 开启预定义颜色
		colors: ["rgb(152, 0, 0)", "rgb(255, 0, 0)", "rgb(255, 153, 0)", "rgb(255, 255, 0)", "rgb(0, 255, 0)",
			"rgb(0, 255, 255)", "rgb(74, 134, 232)", "rgb(0, 0, 255)", "rgb(255, 0, 255)"
		], //自定义预定义颜色项
		done: function(value) {
			console.log(value); // 当前选中的颜色值
			// 非我的分区边框
			$(".dragItemClicked").find(".freeweb_btn>a").css({
				"border-color": value
			})
		}
	});


}

// 中间部分删除
function freeDeleteImg(obj) {
	var thisDir = $(obj).parent().parent().parent().attr("id");
	$(obj).parent().parent().parent().remove();
	$(".settingFree_list").find(".settingF_listli[dir=" + thisDir + "]").remove();
}

// 按钮组件值回显
function btnValue(obj, selector1) {
	var inputV = $(obj).val();
	$(selector1).html(inputV);
}

// 新增组件
function addModuleItem() {

	var timestamp = (new Date()).getTime();
	$(".coorClick").removeClass("coorClick")
	$(".dragItemClicked").removeClass("dragItemClicked")

	$(".freeContainerModuleOther").append(`<div class="ui-draggable freeDragItem dragItemClicked" style="top:0;left:0;" id="m${timestamp}" onclick="freeDragli(this,event)">
													<div name="paper">
														<div style="position: relative;" class="freeBtbBox">
															<div class="freeweb_btn"><a src="#">组件</a></div>
															<div class="coor3 coorClick"></div>
															<img class="del_img" src="images/delete_hover.png" onclick="freeDeleteImg(this)" />
														</div>
													</div>
												</div>`)
	$.getScript('js/dragModule6.js', function() {});
	// 动态加载可放大功能
	$.getScript('js/moveZoom.js', function() {
		all();
	});
	$("#buttonModule").val("组件")
}

// 新建组件2
// var containerContent = document.getElementsByTagName('table');
// var createBoxButton = document.getElementById('createBoxButton');
// var isDragging = false;
// var isResizing = false;
// var offsetX, offsetY;
// // let currentBox;
// var currentBox = $(".dragItemClicked")

// var container = document.getElementById('freeContainerModuleOther');
var seatTable = document.getElementById("seatBox")
// var createBoxButton = document.getElementById('createBoxButton');
// var isDragging = false;
// var isResizing = false;
// var offsetX, offsetY;
// var currentBox;

// createBoxButton.addEventListener('click', () => {
// 	debugger
// 	let selectionBox = document.createElement('div');
// 	selectionBox.classList.add('zone-box');
// 	selectionBox.style.left = '50px';
// 	selectionBox.style.top = '50px';
// 	container.appendChild(selectionBox);

// 	let label = document.createElement('div');
// 	label.textContent = determineZone(selectionBox);
// 	label.style.pointerEvents = 'none'; // 禁止选择文字
// 	selectionBox.appendChild(label);

// 	let resizer = document.createElement('div');
// 	resizer.classList.add('resizer');
// 	selectionBox.appendChild(resizer);

// 	selectionBox.addEventListener('mousedown', (e) => {
// 		if (e.target === resizer) {
// 			isResizing = true;
// 		} else {
// 			isDragging = true;
// 			currentBox = selectionBox;
// 			offsetX = e.clientX - selectionBox.getBoundingClientRect().left;
// 			offsetY = e.clientY - selectionBox.getBoundingClientRect().top;
// 		}
// 	});

// 	resizer.addEventListener('mousedown', (e) => {
// 		e.stopPropagation();
// 		isResizing = true;
// 		currentBox = selectionBox;
// 	});
// });

// document.addEventListener('mousemove', (e) => {
// 	$(".selection-box").remove()
// 	if (isDragging && currentBox) {
// 		let currentX = e.clientX - container.getBoundingClientRect().left - offsetX;
// 		let currentY = e.clientY - container.getBoundingClientRect().top - offsetY;

// 		currentBox.style.left = currentX + 'px';
// 		currentBox.style.top = currentY + 'px';

// 		let label = currentBox.querySelector('div');
// 		label.textContent = determineZone(currentBox);
// 	} else if (isResizing && currentBox) {
// 		let newWidth = e.clientX - currentBox.getBoundingClientRect().left;
// 		let newHeight = e.clientY - currentBox.getBoundingClientRect().top;

// 		currentBox.style.width = newWidth + 'px';
// 		currentBox.style.height = newHeight + 'px';

// 		let label = currentBox.querySelector('div');
// 		label.textContent = determineZone(currentBox);
// 	}
// });

// document.addEventListener('mouseup', () => {
// 	isDragging = false;
// 	isResizing = false;
// 	currentBox = null;
// });

function determineZone(selectionBox) {
	let boxRect = selectionBox.getBoundingClientRect();
	let cells = seatTable.querySelectorAll('td');
	let maxOverlapArea = 0;
	let dominantZone = '未知区域';

	for (let cell of cells) {
		let cellRect = cell.getBoundingClientRect();
		let overlapWidth = Math.max(0, Math.min(boxRect.right, cellRect.right) - Math.max(boxRect.left, cellRect.left));
		let overlapHeight = Math.max(0, Math.min(boxRect.bottom, cellRect.bottom) - Math.max(boxRect.top, cellRect.top));
		let overlapArea = overlapWidth * overlapHeight;
		// console.warn(cell)
		if (overlapArea > maxOverlapArea) {
			maxOverlapArea = overlapArea;
			// dominantZone = cell.getAttribute('data-row');
			dominantZone = cell.querySelector("span").getAttribute('name')
		}
	}
	return dominantZone;
}



/************step6 区域选择确定*************/

function addNewModuleSelect() {
	$("#f-fade,#light1").show()
}

function groupSelectSure() {
	// console.log(groupSelectText)

	if (groupSelectText == "请选择") {
		alert("请选择模块关联座位区域")
	} else {
		var timestamp = (new Date()).getTime();
		$(".coorClick").removeClass("coorClick")
		$(".dragItemClicked").removeClass("dragItemClicked")

		$(".freeContainerModuleOther").append(`<div class="ui-draggable freeDragItem dragItemClicked" name="${groupSelectText}" style="top:0;left:0;" id="m${timestamp}" onclick="freeDragli(this,event)">
															<div name="paper">
																<div style="position: relative;" class="freeBtbBox">
																	<div class="freeweb_btn"><a src="#">${groupSelectText}</a></div>
																	<div class="coor3 coorClick"></div>
																	<img class="del_img" src="images/delete_hover.png" onclick="freeDeleteImg(this)" />
																</div>
															</div>
														</div>`)
		$.getScript('js/dragModule6.js', function() {});
		// 动态加载可放大功能
		$.getScript('js/moveZoom.js', function() {
			all();
		});
		$("#buttonModule").val(groupSelectText)
		// 弹窗隐藏
		$("#f-fade,#light1").hide()
	}
}

// 复制模块
function copyNewModuleSelect() {
	$("#f-fade,#light2").show()
}

function groupCopySelectSure() {
	if (groupCopySelectText == "请选择") {
		// alert("请选择模块关联座位区域")
		layer.msg('请选择模块关联座位区域');
	} else {
		let copyHtml = $(".dragItemClicked").html()
		console.log(copyHtml)
		if (copyHtml == "" || copyHtml == undefined) {
			layer.msg('请选择要复制的模块');
		} else {
			var timestamp = (new Date()).getTime();
			$(".coorClick").removeClass("coorClick")
			$(".dragItemClicked").removeClass("dragItemClicked")

			$(".freeContainerModuleOther").append(`<div class="ui-draggable freeDragItem dragItemClicked" name="${groupCopySelectText}" style="top:0;left:0;" id="m${timestamp}" onclick="freeDragli(this,event)">
																${copyHtml}
															</div>`)
			$.getScript('js/dragModule6.js', function() {});
			// 动态加载可放大功能
			$.getScript('js/moveZoom.js', function() {
				all();
			});
			$("#buttonModule").val(groupCopySelectText)
			$(".dragItemClicked").find(".freeweb_btn>a").html(groupCopySelectText)
			// 弹窗隐藏
			$("#f-fade,#light2").hide()
		}
	}
}

/*******************step6 end***************************/