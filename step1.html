<!DOCTYPE html>
<html lang="ch">
	<head>
		<title>设置座位</title>
		<!-- 设置 viewport -->
		<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1" />
		<!-- IE -->
		<meta http-equiv="X-UA-Compatible" content="IE=edge">
		<!-- 兼容国产浏览器的高速模式 -->
		<meta name="renderer" content="webkit">
		<meta name="Author" content="<EMAIL>" />
		<meta name="Keywords" content="" />
		<meta name="Description" content="" />
		<meta charset="UTF-8">
		<link rel="stylesheet" type="text/css" href="css/reset.css">
		<link rel="stylesheet" href="css/translateCommon.css">
		<link rel="stylesheet" href="layui/css/layui.css" />
		<!-- 步骤条 -->
		<link rel="stylesheet" href="css/stepPlug.css" />
		<link rel="stylesheet" href="css/step1.css" />

	</head>
	<body>
		<div id="section">
			<!-- header -->
			<div id="header">
				<div class="hui">
					<img src="images/hui.jpg" alt="">
				</div>
				<div class="logo">
					<h1 class="f_left">
						<a href="#">
							<img src="images/logo.png" alt=""></a>
					</h1>
					<div class="f_right logo_r">
						<ul class="logo_r_show">
							<!-- <li class="search">
								<p>
									<span class="f_left">受邀者</span>
									<input class="f_left" type="text"> <i></i>
								</p>
							</li> -->
							<li class="translate-box">
								<a class="translate-box-cn" href="javascript:translate.changeLanguage('english');javascript:void(0);">
									<img src="images/translateEn.png" alt="">
								</a>
								<a class="translate-box-en" style="display: none;"
									href="javascript:translate.changeLanguage('chinese_simplified');javascript:void(0);">
									<img src="images/translateCn.png" alt="">
								</a>
							</li>
							<li class="h_qiandao">
								<a href="#">微信签到</a>
							</li>
							<li class="prompt">
								<a href="#">
									<span>0</span>
								</a>
							</li>
							<li class="help">帮助与文档</li>
							<li class="tel">18621589099</li>
						</ul>
					</div>
				</div>
			</div>

			<!-- content -->
			<div id="content" class="clearfix">
				<!-- 左边导航 -->
				<div class="c_left">
					<ul class="c_left_nav f_left">
						<li class="current" title="活动">
							<img src="images/icon6.png" alt="">
						</li>
						<li title="应用">
							<img src="images/icon7.png" alt="">
						</li>
						<li title="联系人">
							<img src="images/icon8.png" alt="">
						</li>
						<li title="供应商">
							<img src="images/icon9.png" alt="">
						</li>
						<li title="账户">
							<img src="images/icon10.png" alt="">
						</li>
					</ul>

				</div>

				<!-- 主要模块 -->
				<div class="c_right">
					<div class="main">
						<div class="main-content">
							<div class="main-html">
								<!-- 步骤条 -->
								<div class="step-html">
									<div class="step-box">
										<div class="step-list step-act">
											<span class="step-number">1</span>
										</div>
										<div class="step-list-html-text step-act">设置场地排列</div>
										<div class="step-line"></div>
										<div class="step-list">
											<span class="step-number">2</span>
										</div>
										<div class="step-list-html-text">设置场地布局</div>
										<div class="step-line"></div>
										<div class="step-list">
											<span class="step-number">3</span>
										</div>
										<div class="step-list-html-text">设置分区</div>
										<!-- <div class="step-line"></div>
										<div class="step-list">
											<span class="step-number">4</span>
										</div>
										<div class="step-list-html-text">设置效果图</div> -->
									</div>
									<!-- <div class="step-list-html">
										<div class="step-list-html-text step-act">设置场地排列</div>
										<div class="step-list-html-text">设置空位</div>
										<div class="step-list-html-text">设置分区</div>
									</div> -->
								</div>

								<!-- 设置座位 -->
								<div class="seat-setting">
									<div class="seat-setting-tips">*请输入您需要的场地行数和列数，生成初始的场地图</div>
									<div class="layui-form-item">
										<label class="layui-form-label">场地图名称：</label>
										<div class="layui-input-block">
											<input type="number" placeholder="场地图名称" autocomplete="off" class="layui-input">
										</div>
									</div>
									<div class="layui-form-item">
										<label class="layui-form-label">行数（Rows)：</label>
										<div class="layui-input-block">
											<input type="number" placeholder="请输入行数" autocomplete="off" class="layui-input" id="row"
												value="20">
										</div>
									</div>
									<div class="layui-form-item">
										<label class="layui-form-label">列数（Cols)：</label>
										<div class="layui-input-block">
											<input type="number" placeholder="请输入行数" autocomplete="off" class="layui-input" id="col"
												value="40">
										</div>
									</div>
									<div class="bottom-btn">
										<button type="button" class="layui-btn" onclick="saveTable()">下一步</button>
									</div>
								</div>

							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- 灰色背景 -->
		<div id="fade" class="black_overlay"></div>

		<!-- 下载弹窗 -->
		<!-- <div id="light2" class="white_content2">
  <div class="title-one">
    <span>下载</span>
    <div class="f_right pop_xx" onclick="cancel('light2');"></div>
  </div>
  <div class="title-two">
      <img src="images/35.gif">
      <p> 正在下载中，请稍后……</p>
    </div>
    <div class="title-three">
      <button class="pop_btn2">取消</button>
    </div>
</div> -->



		<!-- <script src="js/jquery-1.7.2.min.js"></script> -->
		<script src="js/jquery-3.6.3.min.js"></script>
		<script src="js/index.js"></script>

		<!-- 	<script src="translateJs/translate.js"></script>
		<script src="translateJs/translateCommon.js"></script> -->

		<script src="layui/layui.js"></script>
		<script>
			function saveTable() {
				var seatRowsNum = $("#row").val()
				var seatColsNum = $("#col").val()
				localStorage.setItem("seatRows", seatRowsNum)
				localStorage.setItem("seatCols", seatColsNum)
				window.location.href = "step2.html"
			}
		</script>


	</body>
</html>