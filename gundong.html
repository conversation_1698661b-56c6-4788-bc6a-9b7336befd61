<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Multi-direction Scroll</title>
    <style>
        body, html {
            height: 100%;
            margin: 0;
            overflow: hidden; /* 防止整个页面滚动 */
        }
        .scroll-container {
            width: 2000px; /* 超出视口宽度 */
            height: 2000px; /* 超出视口高度 */
            overflow: auto; /* 双向滚动 */
            background-color: lightgray;
        }
        .content {
            width: 1800px;
            height: 1800px;
            background-color: lightblue;
        }
    </style>
</head>
<body>
    <div class="scroll-container">
        <div class="content">
            <h1>Multi-Directional Scroll</h1>
            <p>Scroll in both directions.</p>
        </div>
    </div>
</body>
</html>
