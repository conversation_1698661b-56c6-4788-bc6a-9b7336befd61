// 按钮样式选择
function btnSelect(obj, selector1, selector2) {
	$(obj).addClass('btnModuleAct').siblings().removeClass("btnModuleAct");
	var thisIndex = $(obj).index();
	if (thisIndex == 3) {
		var btnBcak = $(obj).find("span").css("background");
		var btnColor = $(obj).find("span").css("color");
		var btnBorderArio = $(obj).find("span").css("border-radius");
		var btnBorderW = $(obj).find("span").css("border-width");
		var btnBorderC = $(obj).find("span").css("border-color");
		var btnBorderS = $(obj).find("span").css("border-style");

		$(selector2).css('background', btnBcak);
		$(selector2).css('border-radius', "50%");
		$(selector2).css('border-width', btnBorderW);
		$(selector2).css('border-color', btnBorderC);
		$(selector2).css('border-style', btnBorderS);
		$(selector2).css('color', btnColor);

		$(selector1).css("width", "60px");
		$(selector1).css("height", "60px");
		$(selector2).css("height", "60px");
		$(selector2).css("line-height", "50px");

	} else {

		$(selector1).css("width", "100px");
		$(selector1).css("height", "35px");
		$(selector2).css("height", "100%");
		// $(selector1).css("line-height", "30px");

		var btnBcak = $(obj).find("span").css("background");
		var btnColor = $(obj).find("span").css("color");
		var btnBorderArio = $(obj).find("span").css("border-radius");
		var btnBorderW = $(obj).find("span").css("border-width");
		var btnBorderC = $(obj).find("span").css("border-color");
		var btnBorderS = $(obj).find("span").css("border-style");

		$(selector2).css('background', btnBcak);
		$(selector2).css('border-radius', btnBorderArio);
		$(selector2).css('border-width', btnBorderW);
		$(selector2).css('border-color', btnBorderC);
		$(selector2).css('border-style', btnBorderS);
		$(selector2).css('color', btnColor);
	}
}

/*设置字体*/
function fontFamily(obj, attr, selector) {
	var val = $(obj).val();
	$(selector).css(attr, val);
}