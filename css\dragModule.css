.freeContainerModule .freeDragItem {
	background: transparent;
	/* padding: 0px 14px 12px; */
	box-sizing: border-box;
	cursor: move;
	position: absolute;
	z-index: 10;
	border: 2px solid transparent;
	display: inline-block;
	/* width: 200px; */
}

.freeContainerModule .freeDragItem>div[name='paper'] {
	width: 100%;
	/* overflow: hidden; */
}

.freeBtbBox {
	width: 100px;
	height: 35px;
	text-align: center;
	overflow-wrap: break-word;
	word-break: normal;
	position: relative;
}

.freeweb_btn {
	width: 100%;
	height: 100%;
}

.freeweb_btn a {
	display: flex;
	width: 100%;
	height: 100%;
	text-align: center;
	justify-content: center;
	align-items: center;
	background: #4078cb;
	color: #fff;
	text-decoration: none;
	font-size: 14px;
	border: 1px solid transparent;
}

.dragItemClicked .coor1,
.dragItemClicked .coor2,
.dragItemClicked .coor3 {
	width: 8px;
	height: 8px;
	overflow: hidden;
	cursor: se-resize;
	position: absolute;
	right: -5px;
	bottom: -5px;
	background-color: #fff;
	border: 1px solid #666;
}

.del_img {
	display: none;
	position: absolute;
	top: -8px;
	right: -8px;
	cursor: pointer;
}

.freeContainerModule>.dragItemClicked:hover .del_img,
.freeContainerModuleOther>.dragItemClicked:hover .del_img {
	display: block;
}

.freeContainerModule>.dragItemClicked,
.freeContainerModuleOther>.dragItemClicked {
	border: 2px dotted #ddd;
}

.freeContainerModule>.freec_dragLI,
.freeContainerModuleOther>.freec_dragLI {
	background: transparent;
	/* padding: 0px 14px 12px; */
	box-sizing: border-box;
	cursor: move;
	position: absolute;
	z-index: 10;
	border: 2px solid transparent;
	display: inline-block;
	/* width: 200px; */
}

.settingContent {
	width: 100%;
	overflow: hidden;
}

.rangeBoxSetting {
	width: 100%;
	overflow: initial;
}

.rangeItem {
	margin: 20px 10px;
}

.sp-hidden {
	display: none !important;
	/* position: absolute; */
	/* top: -150px; */
}

.dragContent {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	overflow: hidden;
	z-index: 1;
}

.dragContentOther {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	overflow: hidden;
	z-index: 10;
}

.freeContainerModuleOther {
	width: 100%;
	height: 100%;
	position: relative;
	left: 0;
	top: 0;
}

.freeContainerModuleOther .freeDragItem {
	background: transparent;
	/* padding: 0px 14px 12px; */
	box-sizing: border-box;
	cursor: move;
	position: absolute;
	z-index: 10;
	border: 2px solid transparent;
	display: inline-block;
	/* width: 200px; */
}