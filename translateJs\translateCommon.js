/*
{
		"id": "english",
		"name": "English" // 英语
},
{
		"id": "portuguese",
		"name": "Português", // 葡萄牙语
},
{
		"id": "french",
		"name": "Français" // 法语
},
{
		"id": "russian",
		"name": "Русский язык" // 俄语
},
{
		"id": "spanish",
		"name": "español"
},

*/
// <a href="javascript:translate.changeLanguage('portuguese');javascript:void(0);">Português</a>
// <a href="javascript:translate.changeLanguage('russian');javascript:void(0);">Русский язык</a>
let translateHtml = `
											<a href="javascript:translate.changeLanguage('english');javascript:void(0);">English</a>
											<a href="javascript:translate.changeLanguage('spanish');javascript:void(0);">Español</a>
										`
// <a href="javascript:translate.changeLanguage('french');javascript:void(0);">Français</a>
// <a href="javascript:translate.changeLanguage('chinese_simplified');javascript:void(0);">中文</a>

// $(".translate-down-list").html(translateHtml)

// 初始化组件
function initTranslate() {
	translate.setUseVersion2(); //设置使用v2.x 版本


	translate.language.setLocal(
		'chinese_simplified'
	); //设置本地语种（当前网页的语种）。如果不设置，默认自动识别当前网页显示文字的语种。 可填写如 'english'、'chinese_simplified' 等，具体参见文档下方关于此的说明。
	translate.listener
		.start(); //开启html页面变化的监控，对变化部分会进行自动翻译。注意，这里变化区域，是指使用 translate.setDocuments(...) 设置的区域。如果未设置，那么为监控整个网页的变化
	translate.selectLanguageTag.show = false; //不出现的select的选择语言
	translate.language.setDefaultTo('chinese_simplified');
	translate.execute(); //进行翻译 

	// 获取目前使用的语言
	let lang = translate.language.getCurrent()
	console.log(lang)
	if (lang == "chinese_simplified") {
		$(".translate-box-cn").show().siblings().hide()
	} else if (lang == "english") {
		$(".translate-box-en").show().siblings().hide()
	}
}

initTranslate()