<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Step2 按钮测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Step2 按钮检查</h1>
        
        <div class="test-section">
            <h3>检查保存按钮</h3>
            <button onclick="checkButton()">检查按钮状态</button>
            <button onclick="window.open('step2.html', '_blank')">打开Step2</button>
            <div id="results"></div>
        </div>

        <div class="test-section">
            <h3>手动进入Step3</h3>
            <p>如果Step2中的按钮不可见，可以手动进入Step3：</p>
            <button onclick="goToStep3()">直接进入Step3</button>
        </div>

        <div class="test-section">
            <h3>检查数据</h3>
            <button onclick="checkData()">检查当前数据</button>
            <div id="dataResults"></div>
        </div>
    </div>

    <script>
        function checkButton() {
            const results = document.getElementById('results');
            
            // 在新窗口中打开Step2并检查按钮
            const step2Window = window.open('step2.html', 'step2test');
            
            setTimeout(() => {
                try {
                    const button = step2Window.document.getElementById('saveLayoutBtn');
                    const rightPanel = step2Window.document.getElementById('layoutRightPanel');
                    
                    let html = '<h4>按钮检查结果:</h4>';
                    
                    if (button) {
                        const buttonStyle = step2Window.getComputedStyle(button);
                        const panelStyle = step2Window.getComputedStyle(rightPanel);
                        
                        html += `<p>✅ 按钮存在: ${button.textContent}</p>`;
                        html += `<p>按钮显示状态: ${buttonStyle.display}</p>`;
                        html += `<p>按钮可见性: ${buttonStyle.visibility}</p>`;
                        html += `<p>右侧面板显示: ${panelStyle.display}</p>`;
                        
                        if (buttonStyle.display === 'none' || buttonStyle.visibility === 'hidden') {
                            html += '<p class="error">❌ 按钮被隐藏了</p>';
                        } else {
                            html += '<p class="success">✅ 按钮应该是可见的</p>';
                        }
                    } else {
                        html += '<p class="error">❌ 找不到保存按钮</p>';
                    }
                    
                    results.innerHTML = html;
                } catch (e) {
                    results.innerHTML = `<p class="error">检查失败: ${e.message}</p>`;
                }
            }, 2000);
        }
        
        function goToStep3() {
            // 检查是否有座位数据
            const seats = localStorage.getItem('seats');
            if (!seats || Object.keys(JSON.parse(seats)).length === 0) {
                alert('❌ 请先在Step2中设计座位布局');
                return;
            }
            
            // 直接跳转到Step3
            window.open('step3.html', '_blank');
        }
        
        function checkData() {
            const dataResults = document.getElementById('dataResults');
            const seats = localStorage.getItem('seats');
            const seatRows = localStorage.getItem('seatRows');
            const seatCols = localStorage.getItem('seatCols');
            
            let html = '<h4>当前数据状态:</h4>';
            html += `<p>场地规格: ${seatRows || '?'}行 × ${seatCols || '?'}列</p>`;
            
            if (seats) {
                const seatsData = JSON.parse(seats);
                const seatCount = Object.keys(seatsData).length;
                html += `<p>座位数据: ${seatCount}个座位</p>`;
                html += '<p class="success">✅ 可以进入Step3</p>';
            } else {
                html += '<p class="error">❌ 没有座位数据，请先完成Step2</p>';
            }
            
            dataResults.innerHTML = html;
        }
        
        // 页面加载时自动检查数据
        window.onload = checkData;
    </script>
</body>
</html>
