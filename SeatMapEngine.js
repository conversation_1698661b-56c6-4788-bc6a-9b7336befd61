/**
 * 座位图渲染引擎 - 框架无关的核心逻辑
 * 功能：座位生成、渲染、交互、性能优化
 */

// 座位状态枚举
const SeatStatus = {
    AVAILABLE: 'available',
    SELECTED: 'selected',
    OCCUPIED: 'occupied',
    DISABLED: 'disabled',
    EMPTY: 'empty'
}

class SeatMapEngine {
    constructor(canvas, options = {}) {
        this.canvas = canvas
        this.ctx = canvas.getContext('2d')

        // 默认配置
        this.config = {
            seatSize: 24,
            seatSpacing: 4,
            showSeatNumbers: true,
            enableRoundedCorners: true,
            enablePerformanceMode: true,
            ...options
        }

        // 状态管理
        this.state = {
            zoom: 1,
            offsetX: 0,
            offsetY: 0,
            isDragging: false,
            lastMouseX: 0,
            lastMouseY: 0,
            isHighPerformanceMode: false,
            showText: true,
            cornerTransition: 1,
            // 新增选择相关状态
            isSelecting: false,
            selectionStartX: 0,
            selectionStartY: 0,
            selectionEndX: 0,
            selectionEndY: 0
        }

        // 座位数据
        this.seats = []

        // 性能统计
        this.stats = {
            totalSeats: 0,
            visibleSeats: 0,
            renderTime: 0,
            fps: 60,
            lastFrameTime: performance.now()
        }

        // 事件回调
        this.callbacks = {
            onSeatClick: options.onSeatClick || (() => {}),
            onSeatHover: options.onSeatHover || (() => {}),
            onViewChange: options.onViewChange || (() => {})
        }

        // 初始化
        this.init()
    }
    
    init() {
        this.setupCanvas()
        this.bindEvents()
        this.startRenderLoop()
    }
    
    setupCanvas() {
        // 设置Canvas尺寸
        this.state.width = this.canvas.width
        this.state.height = this.canvas.height
        
        // 设置高DPI支持
        const dpr = window.devicePixelRatio || 1
        const rect = this.canvas.getBoundingClientRect()
        
        this.canvas.width = rect.width * dpr
        this.canvas.height = rect.height * dpr
        this.ctx.scale(dpr, dpr)
        
        this.canvas.style.width = rect.width + 'px'
        this.canvas.style.height = rect.height + 'px'
    }
    
    // 生成座位数据
    generateSeats(rows, cols) {
        this.seats = []
        
        for (let row = 0; row < rows; row++) {
            for (let col = 0; col < cols; col++) {
                const seat = {
                    id: `${row}-${col}`,
                    row: row,
                    col: col,
                    x: col * (this.config.seatSize + this.config.seatSpacing),
                    y: row * (this.config.seatSize + this.config.seatSpacing),
                    width: this.config.seatSize,
                    height: this.config.seatSize,
                    status: SeatStatus.AVAILABLE,
                    number: `${(row + 1).toString().padStart(2, '0')}-${(col + 1).toString().padStart(2, '0')}`,
                    name: '',
                    price: 0
                }
                this.seats.push(seat)
            }
        }
        
        this.stats.totalSeats = this.seats.length
        this.render()
        
        return this.seats
    }
    
    // 主渲染函数
    render() {
        const startTime = performance.now()

        // 清空画布
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height)

        // 设置变换
        this.ctx.save()
        this.ctx.translate(this.state.offsetX, this.state.offsetY)
        this.ctx.scale(this.state.zoom, this.state.zoom)

        // 绘制座位
        this.renderSeats()

        this.ctx.restore()

        // 绘制选择框（在变换之外，使用屏幕坐标）
        if (this.state.isSelecting) {
            this.renderSelectionBox()
        }

        // 更新性能统计
        const endTime = performance.now()
        this.stats.renderTime = endTime - startTime
        this.updateFPS()

        // 触发视图变化回调
        this.callbacks.onViewChange({
            zoom: this.state.zoom,
            offsetX: this.state.offsetX,
            offsetY: this.state.offsetY,
            stats: this.stats
        })


    }
    
    // 渲染座位
    renderSeats() {
        // 视口裁剪 - 只绘制可见座位
        const visibleSeats = this.getVisibleSeats()
        this.stats.visibleSeats = visibleSeats.length
        // 根据数量选择绘制模式
        if (visibleSeats.length > 1200 || this.state.isHighPerformanceMode) {
            this.renderSeatsBatch(visibleSeats)
        } else {
            this.renderSeatsIndividual(visibleSeats)
        }

        // 绘制文字
        if (this.shouldShowDetailedView()) {
            this.renderTexts(visibleSeats)
        }


    }
    
    // 获取可见座位
    getVisibleSeats() {
        const viewportLeft = -this.state.offsetX / this.state.zoom
        const viewportTop = -this.state.offsetY / this.state.zoom
        const viewportRight = viewportLeft + (this.canvas.width / this.state.zoom)
        const viewportBottom = viewportTop + (this.canvas.height / this.state.zoom)
        
        return this.seats.filter(seat => {
            return seat.x + seat.width >= viewportLeft &&
                   seat.x <= viewportRight &&
                   seat.y + seat.height >= viewportTop &&
                   seat.y <= viewportBottom
        })
    }

    // 放大
    zoomIn() {
        const newZoom = Math.min(this.state.zoom * 1.2, 3.0)
        this.state.zoom = newZoom
        this.render()
    }

    // 缩小
    zoomOut() {
        const newZoom = Math.max(this.state.zoom / 1.2, 0.1)
        this.state.zoom = newZoom
        this.render()
    }
    
    // 批量渲染座位
    renderSeatsBatch(seats) {
        const seatsByStatus = {}
        
        // 按状态分组
        seats.forEach(seat => {
            if (!seatsByStatus[seat.status]) {
                seatsByStatus[seat.status] = []
            }
            seatsByStatus[seat.status].push(seat)
        })
        
        // 批量绘制
        Object.entries(seatsByStatus).forEach(([status, statusSeats]) => {
            const fillColor = this.getSeatColor(status)
            this.ctx.fillStyle = fillColor
            
            const path = new Path2D()
            statusSeats.forEach(seat => {
                if (this.shouldShowDetailedView() && this.config.enableRoundedCorners) {
                    // 简化的圆角
                    this.drawRoundedRect(seat.x, seat.y, seat.width, seat.height, 3)
                    this.ctx.fill()
                } else {
                    // 矩形批量绘制
                    path.rect(seat.x, seat.y, seat.width, seat.height)
                }
            })
            
            if (!this.shouldShowDetailedView() || !this.config.enableRoundedCorners) {
                this.ctx.fill(path)
            }
        })
    }
    
    // 单个渲染座位
    renderSeatsIndividual(seats) {
        seats.forEach(seat => {
            this.renderSeat(seat)
        })
    }
    
    // 渲染单个座位
    renderSeat(seat) {
        const fillColor = this.getSeatColor(seat.status, seat)
        this.ctx.fillStyle = fillColor



        // 保存Canvas状态，防止绘制过程中状态被意外修改
        this.ctx.save()

        if (this.shouldShowDetailedView() && this.config.enableRoundedCorners) {
            const radius = Math.min(6, seat.width * 0.2)
            this.drawRoundedRect(seat.x, seat.y, seat.width, seat.height, radius)
            this.ctx.fill()
        } else {
            this.ctx.fillRect(seat.x, seat.y, seat.width, seat.height)
        }

        // 恢复Canvas状态
        this.ctx.restore()


    }
    
    // 绘制圆角矩形
    drawRoundedRect(x, y, width, height, radius) {
        this.ctx.beginPath()
        this.ctx.moveTo(x + radius, y)
        this.ctx.lineTo(x + width - radius, y)
        this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius)
        this.ctx.lineTo(x + width, y + height - radius)
        this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
        this.ctx.lineTo(x + radius, y + height)
        this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius)
        this.ctx.lineTo(x, y + radius)
        this.ctx.quadraticCurveTo(x, y, x + radius, y)
        this.ctx.closePath()
    }
    
    // 渲染文字
    renderTexts(seats) {
        if (!this.config.showSeatNumbers || seats.length > 2000) {
            console.log('📝 跳过文字渲染 - showSeatNumbers:', this.config.showSeatNumbers, 'seats.length:', seats.length);
            return;
        }

        console.log('📝 开始渲染文字 - 座位数量:', seats.length);
        console.log('📝 当前Canvas变换状态 - zoom:', this.state.zoom, 'offset:', this.state.offsetX, this.state.offsetY);

        // 检查Canvas状态
        const transform = this.ctx.getTransform();
        console.log('📝 当前Canvas变换矩阵:', transform);

        this.ctx.textAlign = 'center'
        this.ctx.textBaseline = 'middle'
        // 调整字体大小以适应座位编号显示
        const fontSize = Math.max(4, Math.min(8, this.config.seatSize * 0.2));
        this.ctx.font = `${fontSize}px Arial`;
        console.log('🔤 字体设置:', fontSize + 'px Arial');

        let textCount = 0;
        seats.forEach(seat => {
            if (!seat.number) return

            this.ctx.fillStyle = this.getTextColor(seat.status)
            const textX = seat.x + seat.width / 2;
            const textY = seat.y + seat.height / 2;

            // 在绘制前检查Canvas状态
            if (textCount === 0) {
                const currentTransform = this.ctx.getTransform();
                console.log('📝 绘制第一个文字时的变换矩阵:', currentTransform);
            }

            this.ctx.fillText(seat.number, textX, textY)
            textCount++;

            // 只记录前几个和最后几个座位的文字渲染
            if (textCount <= 3 || textCount > seats.length - 3) {
                console.log(`📝 渲染文字 ${textCount}: "${seat.number}" at (${textX}, ${textY}) [座位: ${seat.x}, ${seat.y}]`);
            }
        })

        console.log('📝 文字渲染完成 - 总共渲染了', textCount, '个文字');

        // 检查渲染后的Canvas状态
        const finalTransform = this.ctx.getTransform();
        console.log('📝 文字渲染后的变换矩阵:', finalTransform);
    }
    
    // 获取座位颜色
    getSeatColor(status, seat = null) {
        // 如果座位有分组颜色
        if (seat && seat.groupColor) {
            // 如果是选中状态，返回加深的颜色
            if (status === SeatStatus.SELECTED) {
                return this.darkenColor(seat.groupColor, 0.3); // 加深30%
            }
            // 否则返回原分组颜色
            return seat.groupColor;
        }

        const colors = {
            [SeatStatus.AVAILABLE]: '#e5e5e5',  // 灰色 - 可用但未选中
            [SeatStatus.SELECTED]: '#d1d5db',   // 浅灰色 - 选中的座位（和之前一样）
            [SeatStatus.OCCUPIED]: '#FEE2E2',
            [SeatStatus.DISABLED]: '#F3F4F6',
            [SeatStatus.EMPTY]: '#FEFEFE'
        }
        const color = colors[status] || colors[SeatStatus.EMPTY]
        return color
    }

    // 加深颜色的辅助函数
    darkenColor(color, factor) {
        // 将十六进制颜色转换为RGB
        const hex = color.replace('#', '');
        const r = parseInt(hex.substr(0, 2), 16);
        const g = parseInt(hex.substr(2, 2), 16);
        const b = parseInt(hex.substr(4, 2), 16);

        // 加深颜色
        const newR = Math.round(r * (1 - factor));
        const newG = Math.round(g * (1 - factor));
        const newB = Math.round(b * (1 - factor));

        // 转换回十六进制
        return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
    }
    
    // 获取文字颜色
    getTextColor(status) {
        const colors = {
            [SeatStatus.AVAILABLE]: '#333',     // 原版未选中座位文字颜色
            [SeatStatus.SELECTED]: '#333',      // 原版选中座位文字颜色 (深灰)
            [SeatStatus.OCCUPIED]: '#B91C1C',
            [SeatStatus.DISABLED]: '#6B7280',
            [SeatStatus.EMPTY]: '#374151'
        }
        return colors[status] || colors[SeatStatus.EMPTY]
    }
    
    // 判断是否显示详细视图
    shouldShowDetailedView() {
        const actualSeatSize = this.config.seatSize * this.state.zoom
        let threshold = 40
        
        if (this.stats.totalSeats > 5000) threshold = 50
        else if (this.stats.totalSeats > 2000) threshold = 45
        else if (this.stats.totalSeats > 1000) threshold = 42
        
        return actualSeatSize >= threshold
    }
    
    // 更新FPS
    updateFPS() {
        const now = performance.now()
        const frameTime = now - this.stats.lastFrameTime
        this.stats.lastFrameTime = now
        
        if (frameTime > 0) {
            this.stats.fps = Math.round(1000 / frameTime)
        }
    }
    
    // 缩放控制
    zoomIn() {
        this.state.zoom = Math.min(3, this.state.zoom * 1.2)
        this.render()
    }
    
    zoomOut() {
        this.state.zoom = Math.max(0.3, this.state.zoom / 1.2)
        this.render()
    }
    
    resetZoom() {
        this.state.zoom = 1
        this.state.offsetX = 0
        this.state.offsetY = 0
        this.render()
    }
    
    // 事件处理方法
    handleMouseDown(e) {
        const rect = this.canvas.getBoundingClientRect()
        const x = e.clientX - rect.left
        const y = e.clientY - rect.top

        // 检查是否点击了座位
        const clickedSeat = this.getSeatAtPosition(x, y)
        if (clickedSeat) {
            // 点击座位，切换选中状态
            this.toggleSeatStatus(clickedSeat)
            this.callbacks.onSeatClick(clickedSeat)
            this.render()
        } else {
            // 没有点击座位，开始框选
            this.state.isSelecting = true
            this.state.selectionStartX = x
            this.state.selectionStartY = y
            this.state.selectionEndX = x
            this.state.selectionEndY = y
        }

        this.state.isDragging = true
        this.state.lastMouseX = x
        this.state.lastMouseY = y
    }

    handleMouseMove(e) {
        const rect = this.canvas.getBoundingClientRect()
        const x = e.clientX - rect.left
        const y = e.clientY - rect.top

        if (this.state.isDragging) {
            if (this.state.isSelecting) {
                // 更新选择框
                this.state.selectionEndX = x
                this.state.selectionEndY = y
                this.render()
            } else {
                // 拖动视图
                const deltaX = x - this.state.lastMouseX
                const deltaY = y - this.state.lastMouseY

                this.state.offsetX += deltaX
                this.state.offsetY += deltaY

                this.state.lastMouseX = x
                this.state.lastMouseY = y

                this.render()
            }
        } else {
            // 悬停检测
            const hoveredSeat = this.getSeatAtPosition(x, y)
            if (hoveredSeat) {
                this.callbacks.onSeatHover(hoveredSeat)
            }
        }
    }

    handleMouseUp() {
        if (this.state.isSelecting) {
            // 结束框选，切换选择框内座位的状态
            const selectedSeats = this.getSeatsInSelection()
            selectedSeats.forEach(seat => {
                this.toggleSeatStatus(seat)
                // 为每个框选的座位调用点击回调
                this.callbacks.onSeatClick(seat)
            })

            this.state.isSelecting = false
            this.render()
        }

        this.state.isDragging = false
    }

    handleWheel(e) {
        e.preventDefault()

        const rect = this.canvas.getBoundingClientRect()
        const x = e.clientX - rect.left
        const y = e.clientY - rect.top

        const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1
        const newZoom = Math.max(0.3, Math.min(3, this.state.zoom * zoomFactor))

        // 以鼠标位置为中心缩放
        const zoomRatio = newZoom / this.state.zoom
        this.state.offsetX = x - (x - this.state.offsetX) * zoomRatio
        this.state.offsetY = y - (y - this.state.offsetY) * zoomRatio
        this.state.zoom = newZoom

        this.render()
    }

    // 获取指定位置的座位
    getSeatAtPosition(canvasX, canvasY) {
        // 转换为座位坐标系
        const seatX = (canvasX - this.state.offsetX) / this.state.zoom
        const seatY = (canvasY - this.state.offsetY) / this.state.zoom

        return this.seats.find(seat =>
            seatX >= seat.x && seatX <= seat.x + seat.width &&
            seatY >= seat.y && seatY <= seat.y + seat.height
        )
    }

    // 事件绑定 - 只保留滚轮缩放
    bindEvents() {
        // 只绑定滚轮事件用于缩放
        this.canvas.addEventListener('wheel', this.handleWheel.bind(this))

        // 移除其他鼠标事件，改为通过工具栏控制
        // this.canvas.addEventListener('mousedown', this.handleMouseDown.bind(this))
        // this.canvas.addEventListener('mousemove', this.handleMouseMove.bind(this))
        // this.canvas.addEventListener('mouseup', this.handleMouseUp.bind(this))
    }

    // 渲染循环
    startRenderLoop() {
        // 初始渲染
        this.render()
    }
    
    // 切换座位状态
    toggleSeatStatus(seat) {
        if (seat.status === SeatStatus.SELECTED) {
            seat.status = SeatStatus.AVAILABLE
        } else if (seat.status === SeatStatus.AVAILABLE) {
            seat.status = SeatStatus.SELECTED
        }
    }

    // 绘制选择框
    renderSelectionBox() {
        const startX = Math.min(this.state.selectionStartX, this.state.selectionEndX)
        const startY = Math.min(this.state.selectionStartY, this.state.selectionEndY)
        const width = Math.abs(this.state.selectionEndX - this.state.selectionStartX)
        const height = Math.abs(this.state.selectionEndY - this.state.selectionStartY)



        this.ctx.save()

        // 根据选择模式设置不同颜色（功能已对调）
        if (this.state.selectionMode === 'remove') {
            // 移除模式：红色（现在是框选-按钮，实际是移除功能）
            this.ctx.strokeStyle = '#EF4444'
            this.ctx.fillStyle = 'rgba(239, 68, 68, 0.1)'
        } else if (this.state.selectionMode === 'zone') {
            // 分区选择模式：绿色（选择已有座位用于分区）
            this.ctx.strokeStyle = '#10B981'
            this.ctx.fillStyle = 'rgba(16, 185, 129, 0.1)'
        } else {
            // 添加模式：蓝色（现在是框选+按钮，实际是添加功能）
            this.ctx.strokeStyle = '#3B82F6'
            this.ctx.fillStyle = 'rgba(59, 130, 246, 0.1)'
        }

        this.ctx.lineWidth = 2
        this.ctx.setLineDash([5, 5])

        this.ctx.fillRect(startX, startY, width, height)
        this.ctx.strokeRect(startX, startY, width, height)
        this.ctx.restore()
    }

    // 获取选择框内的座位
    getSeatsInSelection() {
        const startX = Math.min(this.state.selectionStartX, this.state.selectionEndX)
        const startY = Math.min(this.state.selectionStartY, this.state.selectionEndY)
        const endX = Math.max(this.state.selectionStartX, this.state.selectionEndX)
        const endY = Math.max(this.state.selectionStartY, this.state.selectionEndY)

        // 转换为座位坐标系
        const seatStartX = (startX - this.state.offsetX) / this.state.zoom
        const seatStartY = (startY - this.state.offsetY) / this.state.zoom
        const seatEndX = (endX - this.state.offsetX) / this.state.zoom
        const seatEndY = (endY - this.state.offsetY) / this.state.zoom

        return this.seats.filter(seat => {
            const seatCenterX = seat.x + seat.width / 2
            const seatCenterY = seat.y + seat.height / 2

            return seatCenterX >= seatStartX && seatCenterX <= seatEndX &&
                   seatCenterY >= seatStartY && seatCenterY <= seatEndY
        })
    }

    // 选择整行
    selectRow(rowIndex) {
        const rowSeats = this.seats.filter(seat => seat.row === rowIndex)
        rowSeats.forEach(seat => this.toggleSeatStatus(seat))
        this.render()
        return rowSeats
    }

    // 选择整列
    selectColumn(colIndex) {
        const colSeats = this.seats.filter(seat => seat.col === colIndex)
        colSeats.forEach(seat => this.toggleSeatStatus(seat))
        this.render()
        return colSeats
    }

    // 加载座位数据
    loadSeats(seatData) {
        this.seats = seatData
        this.stats.totalSeats = this.seats.length
        this.render()
    }

    // 获取所有选中的座位
    getSelectedSeats() {
        return this.seats.filter(seat => seat.status === SeatStatus.SELECTED)
    }

    // 获取所有可用的座位
    getAvailableSeats() {
        return this.seats.filter(seat => seat.status === SeatStatus.AVAILABLE)
    }

    // 销毁
    destroy() {
        // 清理事件监听器 - 只移除滚轮事件
        this.canvas.removeEventListener('wheel', this.handleWheel.bind(this))

        // 清理数据
        this.seats = []
        this.callbacks = {}
    }
}
