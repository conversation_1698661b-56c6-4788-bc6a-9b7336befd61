.edit-module-box {
	width: 90%;
	overflow: hidden;
	margin: 20px auto;
}

.edit-module-flex {
	display: flex;
	align-items: center;
	width: 100%;
	margin: 10px auto;
}

.edit-module-flex .layui-input {
	flex: 1;
}

.delete-module-text {
	width: 90%;
	overflow: hidden;
	margin: 20px auto;
	text-align: center;
	color: #333;
	font-size: 14px;
}

body .seatBox .seat-table tr .selected {
	background-color: #fff !important;
}

.zoomBox {
	position: absolute;
	right: 15px;
	top: 20px;
}

.zoomBox .layui-icon {
	font-size: 25px;
	cursor: pointer;
	color: #666;
}

.new-content-top {
	width: 100%;
	overflow: hidden;
	margin-bottom: 10px;
	margin-top: 0px;
}

.content-top-btn {
	float: right;
	overflow: hidden;
}

.main-content {
	height: calc(100vh - 120px);
}

.seatBoxContent {
	height: calc(100vh - 230px);
}

.seatBox {
	display: flex;
}