#f-fade {
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1818;
	display: none;
}

.white-contentAll {
	position: fixed;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	overflow: initial;
	background-color: #fff;
	-webkit-background-clip: padding-box;
	background-clip: padding-box;
	border: 1px solid #999;
	border: 1px solid rgba(0, 0, 0, .2);
	border-radius: 6px;
	outline: 0;
	-webkit-box-shadow: 0 3px 9px rgba(0, 0, 0, .5);
	box-shadow: 0 3px 9px rgba(0, 0, 0, .5);
	z-index: 9999;
	display: none;
}

.white-header {
	padding: 15px;
	border-bottom: 1px solid #e5e5e5;
}

.white-headerTitle {
	font-size: 14px;
	font-weight: bold;
}

.white-delete {
	display: inline-block;
	position: absolute;
	top: 12px;
	right: 12px;
	width: 25px;
	height: 25px;
	cursor: pointer;
	background: url(../images/delete.png) 0 0 no-repeat;
}

.white-delete:hover {
	background: url(../images/deleteHover.png) 0 0 no-repeat;
}

.white-body {
	padding: 15px;
	font-size: 14px;
	max-height: 380px;
	overflow-y: auto;
}

.white-footer {
	padding: 15px;
	text-align: right;
	border-top: 1px solid #e5e5e5;
}

.white-footer button {
	display: inline-block;
	padding: 4px 12px 4px;
	margin-bottom: 0;
	margin-right: 10px;
	font-size: 14px;
	font-weight: 400;
	line-height: 1.42857143;
	text-align: center;
	white-space: nowrap;
	vertical-align: middle;
	-ms-touch-action: manipulation;
	touch-action: manipulation;
	cursor: pointer;
	-webkit-user-select: none;
	-moz-user-select: none;
	-ms-user-select: none;
	user-select: none;
	background-image: none;
	border: 1px solid transparent;
	border-radius: 4px;
	outline-style: none;
	box-sizing: border-box;
}

.white-footer .white-close {
	color: #333;
	background-color: #fff;
	border-color: #ccc;
	width: 70px;
}

.white-footer .white-close:hover {
	background-color: #E6E6E6;
}

.white-footer .white-sure {
	color: #fff;
	background-color: #16baaa;
	border-color: #16baaa;
	min-width: 70px;
}

.white-footer .white-sure:hover {
	background-color: #16baaa;
}