<!DOCTYPE html>
<html lang="ch">
<head>
    <title>测试新座位系统 - Step2功能</title>
    <meta charset="UTF-8">
    <link rel="stylesheet" type="text/css" href="css/reset.css">
    <link rel="stylesheet" href="css/translateCommon.css">
    <link rel="stylesheet" href="layui/css/layui.css" />
    <link rel="stylesheet" href="css/stepPlug.css" />
    <link rel="stylesheet" href="css/seat3.css" />
    <link rel="stylesheet" href="css/step2only.css" />
    <style>
        .test-panel {
            position: fixed;
            top: 10px;
            right: 10px;
            width: 300px;
            background: white;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            z-index: 1000;
        }
        .test-panel h3 {
            margin: 0 0 10px 0;
            color: #333;
        }
        .test-panel button {
            margin: 5px 0;
            width: 100%;
        }
        .test-log {
            max-height: 200px;
            overflow-y: auto;
            background: #f5f5f5;
            padding: 10px;
            margin-top: 10px;
            font-size: 12px;
            border-radius: 3px;
        }
        .test-log div {
            margin: 2px 0;
            padding: 2px 5px;
            border-radius: 2px;
        }
        .test-log .success { background: #d4edda; color: #155724; }
        .test-log .error { background: #f8d7da; color: #721c24; }
        .test-log .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <!-- 测试面板 -->
    <div class="test-panel">
        <h3>🧪 功能测试面板</h3>
        
        <button class="layui-btn layui-btn-sm" onclick="testBasicFunctions()">
            测试基础功能
        </button>
        
        <button class="layui-btn layui-btn-sm layui-btn-normal" onclick="testInteractions()">
            测试交互功能
        </button>
        
        <button class="layui-btn layui-btn-sm layui-btn-warm" onclick="testDataCompatibility()">
            测试数据兼容性
        </button>
        
        <button class="layui-btn layui-btn-sm layui-btn-danger" onclick="clearLog()">
            清空日志
        </button>
        
        <div class="test-log" id="testLog">
            <div class="info">测试日志将显示在这里...</div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div id="section">
        <div id="content" class="clearfix">
            <div class="c_right">
                <div class="main">
                    <div class="main-content">
                        <div class="main-html">
                            <!-- 座位图提示 -->
                            <div class="seat-setting-tips">
                                *请选择实际有座位的位置，取消没有座位的位置
                                <span id="seatTotal">当前座位总数：0个</span>
                            </div>
                            
                            <!-- 座位图容器 -->
                            <div class="seatBoxContent seatBoxContentSetp2" id="seatBoxContent">
                                <div class="newSeatBox">
                                    <div class="seatBox" id="seatBox" style="width: auto;" zoom="1">
                                        <!-- Canvas将在这里创建 -->
                                    </div>
                                </div>
                                <!-- 缩放控制 -->
                                <div class="zoomBox">
                                    <i class="layui-icon layui-icon-reduce-circle" onclick="zoomFn('in')"></i>
                                    <i class="layui-icon layui-icon-add-circle" onclick="zoomFn('out')"></i>
                                </div>
                            </div>
                            
                            <!-- 底部按钮 -->
                            <div class="bottom-btn">
                                <button type="button" class="layui-btn layui-btn-primary" onclick="testSave()">
                                    测试保存
                                </button>
                                <button type="button" class="layui-btn" onclick="testLoad()">
                                    测试加载
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 脚本 -->
    <script src="layui/layui.js"></script>
    <script src="js/jquery-3.3.1.min.js"></script>
    
    <script type="module">
        import { CompatibleSeatWrapper } from './CompatibleSeatWrapper.js'
        
        // 全局变量
        window.CompatibleSeatWrapper = CompatibleSeatWrapper
        window.seats = null
        
        // 缩放函数
        window.zoomFn = function(direction) {
            const canvas = document.querySelector('#seatBox canvas')
            if (canvas && canvas.seatEngine) {
                if (direction === 'in') {
                    canvas.seatEngine.zoomOut()
                    log('缩小视图', 'info')
                } else if (direction === 'out') {
                    canvas.seatEngine.zoomIn()
                    log('放大视图', 'info')
                }
            } else {
                log('未找到座位引擎', 'error')
            }
        }
        
        // 日志函数
        window.log = function(message, type = 'info') {
            const logDiv = document.getElementById('testLog')
            const div = document.createElement('div')
            div.className = type
            div.textContent = `${new Date().toLocaleTimeString()}: ${message}`
            logDiv.appendChild(div)
            logDiv.scrollTop = logDiv.scrollHeight
        }
        
        // 清空日志
        window.clearLog = function() {
            document.getElementById('testLog').innerHTML = ''
        }
        
        // 测试基础功能
        window.testBasicFunctions = function() {
            log('开始测试基础功能...', 'info')
            
            try {
                // 测试座位系统创建
                if (window.seats) {
                    log('✅ 座位系统已创建', 'success')
                } else {
                    log('❌ 座位系统未创建', 'error')
                    return
                }
                
                // 测试Canvas创建
                const canvas = document.querySelector('#seatBox canvas')
                if (canvas) {
                    log('✅ Canvas元素已创建', 'success')
                } else {
                    log('❌ Canvas元素未创建', 'error')
                }
                
                // 测试引擎绑定
                if (canvas && canvas.seatEngine) {
                    log('✅ 座位引擎已绑定到Canvas', 'success')
                } else {
                    log('❌ 座位引擎未绑定', 'error')
                }
                
                // 测试接口
                const seatsData = window.seats.getSeats()
                log(`✅ getSeats()接口正常，当前座位数: ${Object.keys(seatsData).length}`, 'success')
                
            } catch (error) {
                log(`❌ 基础功能测试失败: ${error.message}`, 'error')
            }
        }
        
        // 测试交互功能
        window.testInteractions = function() {
            log('开始测试交互功能...', 'info')
            
            try {
                const canvas = document.querySelector('#seatBox canvas')
                if (!canvas || !canvas.seatEngine) {
                    log('❌ 座位引擎不可用', 'error')
                    return
                }
                
                const engine = canvas.seatEngine
                
                // 测试行选择
                log('测试选择第1行...', 'info')
                engine.selectRow(0)
                const rowSeats = engine.getSelectedSeats()
                log(`✅ 行选择功能正常，选中 ${rowSeats.length} 个座位`, 'success')
                
                // 测试列选择
                log('测试选择第1列...', 'info')
                engine.selectColumn(0)
                const colSeats = engine.getSelectedSeats()
                log(`✅ 列选择功能正常，当前选中 ${colSeats.length} 个座位`, 'success')
                
                // 更新统计
                window.seats.updateSeatsData()
                log('✅ 数据同步功能正常', 'success')
                
            } catch (error) {
                log(`❌ 交互功能测试失败: ${error.message}`, 'error')
            }
        }
        
        // 测试数据兼容性
        window.testDataCompatibility = function() {
            log('开始测试数据兼容性...', 'info')
            
            try {
                // 模拟原项目数据格式
                const testData = {
                    "1_1": { row: 1, col: 1, price: 100, color: "red", groupName: "VIP区" },
                    "1_2": { row: 1, col: 2, price: 100, color: "red", groupName: "VIP区" },
                    "2_1": { row: 2, col: 1, price: 50, color: "blue", groupName: "普通区" }
                }
                
                // 测试数据加载
                window.seats.loadExistingData(testData)
                log('✅ 原项目数据格式加载成功', 'success')
                
                // 测试数据获取
                const retrievedData = window.seats.getSeats()
                const dataKeys = Object.keys(retrievedData)
                log(`✅ 数据获取成功，包含 ${dataKeys.length} 个座位`, 'success')
                
                // 验证数据格式
                if (dataKeys.length > 0) {
                    const firstSeat = retrievedData[dataKeys[0]]
                    if (firstSeat.row && firstSeat.col && firstSeat.hasOwnProperty('price')) {
                        log('✅ 数据格式兼容性验证通过', 'success')
                    } else {
                        log('❌ 数据格式不兼容', 'error')
                    }
                }
                
            } catch (error) {
                log(`❌ 数据兼容性测试失败: ${error.message}`, 'error')
            }
        }
        
        // 测试保存
        window.testSave = function() {
            try {
                const data = window.seats.getSeats()
                localStorage.setItem("seats_test", JSON.stringify(data))
                log(`✅ 保存成功，数据包含 ${Object.keys(data).length} 个座位`, 'success')
            } catch (error) {
                log(`❌ 保存失败: ${error.message}`, 'error')
            }
        }
        
        // 测试加载
        window.testLoad = function() {
            try {
                const data = localStorage.getItem("seats_test")
                if (data) {
                    const parsedData = JSON.parse(data)
                    window.seats.loadExistingData(parsedData)
                    log(`✅ 加载成功，恢复 ${Object.keys(parsedData).length} 个座位`, 'success')
                } else {
                    log('❌ 没有找到测试数据', 'error')
                }
            } catch (error) {
                log(`❌ 加载失败: ${error.message}`, 'error')
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成，开始初始化...', 'info')
            
            try {
                // 创建座位系统
                window.seats = new CompatibleSeatWrapper({
                    rows: 8,
                    cols: 10,
                    size: 20,
                    box: "#seatBox",
                    step: 101,
                    datas: {},
                    thre_id: 20,
                    hall_id: 10,
                    movie_id: 30
                })
                
                log('✅ 座位系统初始化成功', 'success')
                log('可以开始测试各项功能了！', 'info')
                
            } catch (error) {
                log(`❌ 初始化失败: ${error.message}`, 'error')
            }
        })
    </script>
</body>
</html>
