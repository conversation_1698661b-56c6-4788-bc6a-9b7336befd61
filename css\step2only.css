body .seatBox .seat-table tr .selected {
	background-color: #fff !important;
}

.zoomBox {
	position: absolute;
	right: 15px;
	top: 20px;
}

.zoomBox .layui-icon {
	font-size: 25px;
	cursor: pointer;
	color: #666;
}

.new-content-top {
	width: 100%;
	overflow: hidden;
	margin-bottom: 10px;
	margin-top: 0px;
}

.content-top-btn {
	float: right;
	overflow: hidden;
}

.seatBoxContentSetp2 {
	height: 100% !important;
	/* position: relative; */
}

.seatBox {
	/* display: flex; 移除flex布局，使用Canvas布局 */
}