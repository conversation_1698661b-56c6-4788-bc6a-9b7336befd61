<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>Step3 - 座位分区设置</title>
    <link rel="stylesheet" href="layui/css/layui.css" />
    <style>
        body { margin: 0; font-family: Arial, sans-serif; }
        
        .header {
            background: #001529;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .main-content {
            display: flex;
            height: calc(100vh - 80px);
            overflow: hidden;
        }

        .sidebar-toolbar {
            width: 250px;
            background: white;
            border-right: 1px solid #e6e6e6;
            overflow-y: auto;
            padding: 20px 15px;
            box-sizing: border-box;
        }

        .toolbar-section {
            margin-bottom: 25px;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
        }

        .section-title {
            font-size: 14px;
            font-weight: bold;
            color: #333;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e6e6e6;
        }

        .btn-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 8px;
        }

        .sidebar-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.2s;
            font-size: 12px;
            min-height: 36px;
        }

        .sidebar-btn.primary {
            background: #52c41a;
            border-color: #52c41a;
            color: white;
        }

        .sidebar-btn.active {
            background: #1890ff;
            border-color: #1890ff;
            color: white;
        }

        .seat-canvas-container {
            flex: 1;
            position: relative;
            background: #fafafa;
            overflow: hidden;
        }

        #seatBox {
            width: 100%;
            height: 100%;
            position: relative;
            overflow: hidden;
        }

        #seatCanvas {
            display: block;
            cursor: grab;
        }

        .main-right {
            width: 300px;
            background: white;
            border-left: 1px solid #e6e6e6;
            overflow-y: auto;
            padding: 20px;
        }

        .info-section {
            margin-bottom: 20px;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
        }

        .zone-form {
            background: #f0f0f0;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 15px;
        }

        .form-group {
            margin-bottom: 12px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Step3 - 座位分区设置</h1>
    </div>

    <div class="main-content">
        <!-- 左侧工具栏 -->
        <div class="sidebar-toolbar">
            <div class="toolbar-section">
                <div class="section-title">导航</div>
                <div class="btn-grid">
                    <button type="button" class="sidebar-btn primary" onclick="goToStep2()">上一步</button>
                    <button type="button" class="sidebar-btn primary" onclick="saveZones()">保存设置</button>
                </div>
            </div>

            <div class="toolbar-section">
                <div class="section-title">工具</div>
                <div class="btn-grid">
                    <button type="button" class="sidebar-btn tool-btn active" data-tool="select">选择座位</button>
                    <button type="button" class="sidebar-btn tool-btn" data-tool="boxselect">框选座位</button>
                </div>
                <div class="btn-grid">
                    <button type="button" class="sidebar-btn tool-btn" data-tool="pan">拖拽</button>
                    <button type="button" class="sidebar-btn" onclick="showZoneForm()">创建分区</button>
                </div>
            </div>

            <div class="toolbar-section">
                <div class="section-title">操作</div>
                <div class="btn-grid">
                    <button type="button" class="sidebar-btn" onclick="selectAllSeats()">全选</button>
                    <button type="button" class="sidebar-btn" onclick="clearSelection()">清除选择</button>
                </div>
            </div>
        </div>

        <!-- 座位画布区域 -->
        <div class="seat-canvas-container">
            <div id="seatBox">
                <canvas id="seatCanvas"></canvas>
            </div>
        </div>

        <!-- 右侧面板 -->
        <div class="main-right">
            <div class="info-section">
                <h4>📊 当前选择</h4>
                <p>已选择座位：<span id="selectedCount">0</span> 个</p>
                <p>总座位数：<span id="totalSeats">0</span> 个</p>
            </div>

            <div class="zone-form" id="zoneForm" style="display: none;">
                <h4>🎨 创建新分区</h4>
                <div class="form-group">
                    <label>分区名称</label>
                    <input type="text" id="zoneName" placeholder="例如：VIP区" />
                </div>
                <div class="form-group">
                    <label>分区价格</label>
                    <input type="number" id="zonePrice" placeholder="例如：100" />
                </div>
                <div class="form-group">
                    <label>分区颜色</label>
                    <input type="color" id="zoneColor" value="#1890ff" />
                </div>
                <div class="form-group">
                    <button class="layui-btn layui-btn-sm" onclick="createZone()">创建分区</button>
                    <button class="layui-btn layui-btn-primary layui-btn-sm" onclick="cancelZone()">取消</button>
                </div>
            </div>

            <div class="info-section">
                <h4>📝 已创建分区</h4>
                <div id="zoneList">
                    <p style="color: #999; text-align: center;">暂无分区</p>
                </div>
            </div>
        </div>
    </div>

    <script src="layui/layui.js"></script>
    <script src="js/jquery-3.6.3.min.js"></script>
    <script src="SeatMapEngine.js"></script>
    <script>
        // 全局变量
        let engine;
        let seatsData = {};
        let selectedSeats = new Set();
        let zones = [];
        let currentTool = 'select';

        // 创建测试数据
        function createTestData() {
            localStorage.setItem("seatRows", "8");
            localStorage.setItem("seatCols", "12");
            
            const testSeats = {};
            for (let row = 0; row < 8; row++) {
                for (let col = 0; col < 12; col++) {
                    testSeats[row + "_" + col] = {
                        row: row,
                        col: col,
                        price: 0
                    };
                }
            }
            seatsData = testSeats;
        }

        $(document).ready(function() {
            // 创建测试数据
            createTestData();

            // 初始化Canvas
            const canvas = document.getElementById('seatCanvas');
            const container = document.getElementById('seatBox');

            engine = new SeatMapEngine(canvas, {
                rows: 8,
                cols: 12,
                seatSize: 24,
                seatSpacing: 4,
                showRowNumbers: true,
                showColNumbers: true,
                container: container
            });

            // 设置座位数据
            engine.seats.forEach(seat => {
                const seatKey = seat.row + "_" + seat.col;
                if (seatsData[seatKey]) {
                    seat.status = 'available';
                } else {
                    seat.status = 'empty';
                }
            });

            // 设置点击处理
            engine.onSeatClick = function(seat) {
                if (currentTool !== 'select') return;
                
                const seatKey = seat.row + "_" + seat.col;
                if (!seatsData[seatKey]) return;

                if (selectedSeats.has(seatKey)) {
                    selectedSeats.delete(seatKey);
                    seat.status = 'available';
                } else {
                    selectedSeats.add(seatKey);
                    seat.status = 'selected';
                }

                engine.render();
                updateSelectionInfo();
            };

            // 初始化界面
            updateSelectionInfo();
            engine.render();
        });

        function updateSelectionInfo() {
            document.getElementById('selectedCount').textContent = selectedSeats.size;
            document.getElementById('totalSeats').textContent = Object.keys(seatsData).length;
        }

        function selectAllSeats() {
            selectedSeats.clear();
            engine.seats.forEach(seat => {
                const seatKey = seat.row + "_" + seat.col;
                if (seatsData[seatKey]) {
                    selectedSeats.add(seatKey);
                    seat.status = 'selected';
                }
            });
            engine.render();
            updateSelectionInfo();
        }

        function clearSelection() {
            selectedSeats.clear();
            engine.seats.forEach(seat => {
                const seatKey = seat.row + "_" + seat.col;
                if (seatsData[seatKey]) {
                    seat.status = 'available';
                }
            });
            engine.render();
            updateSelectionInfo();
        }

        function showZoneForm() {
            if (selectedSeats.size === 0) {
                alert('请先选择要分区的座位！');
                return;
            }
            document.getElementById('zoneForm').style.display = 'block';
        }

        function cancelZone() {
            document.getElementById('zoneForm').style.display = 'none';
            document.getElementById('zoneName').value = '';
            document.getElementById('zonePrice').value = '';
        }

        function createZone() {
            const name = document.getElementById('zoneName').value.trim();
            const price = document.getElementById('zonePrice').value.trim();
            const color = document.getElementById('zoneColor').value;

            if (!name || !price) {
                alert('请填写完整信息！');
                return;
            }

            // 创建分区
            const zone = {
                id: Date.now().toString(),
                name: name,
                price: parseFloat(price),
                color: color,
                seats: Array.from(selectedSeats)
            };

            zones.push(zone);

            // 更新座位颜色
            engine.seats.forEach(seat => {
                const seatKey = seat.row + "_" + seat.col;
                if (selectedSeats.has(seatKey)) {
                    seat.groupColor = color;
                    seat.groupName = name;
                    seat.status = 'available';
                }
            });

            selectedSeats.clear();
            updateSelectionInfo();
            updateZoneList();
            cancelZone();
            engine.render();

            alert(`分区"${name}"创建成功！`);
        }

        function updateZoneList() {
            const zoneList = document.getElementById('zoneList');
            if (zones.length === 0) {
                zoneList.innerHTML = '<p style="color: #999; text-align: center;">暂无分区</p>';
                return;
            }

            let html = '';
            zones.forEach(zone => {
                html += `
                    <div style="background: white; padding: 10px; margin-bottom: 10px; border-radius: 4px; border: 1px solid #eee;">
                        <h5 style="margin: 0 0 5px 0;">${zone.name}</h5>
                        <p style="margin: 0; font-size: 12px; color: #666;">
                            价格：¥${zone.price} | 座位：${zone.seats.length}个
                            <span style="display: inline-block; width: 12px; height: 12px; background: ${zone.color}; border-radius: 2px; margin-left: 5px;"></span>
                        </p>
                    </div>
                `;
            });
            zoneList.innerHTML = html;
        }

        function saveZones() {
            if (zones.length === 0) {
                alert('请至少创建一个分区！');
                return;
            }
            localStorage.setItem('zones', JSON.stringify(zones));
            alert('分区设置已保存！');
        }

        function goToStep2() {
            if (confirm('确定要返回第二步吗？')) {
                location.href = 'step2.html';
            }
        }
    </script>
</body>
</html>
