.c_right {
	background: #fff;
}

.content-btn-box {
	width: 100%;
	overflow: hidden;
}

.content-title {
	font-size: 16px;
	color: #000;
	font-weight: bold;
}

.new-content-btn {
	margin-top: 0px;
	float: right;
	display: flex;
	align-items: center;
}

.new-content-btn button {
	background: #4078cb;
	margin: 0 0 0 10px !important;
}

.content-html {
	width: 100%;
	overflow: hidden;
	border-top: 1px solid #ddd;
	margin-top: 10px;
	padding-top: 10px;
}

.activity_show .activity_list {
	border: 1px solid #ccc;
	background: #fff;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
}

.main .activity {
	float: left;
	width: 100%;
	height: auto;
	min-height: 900px;
	background: #fff;
	padding: 0 10px 10px 10px;
	box-sizing: border-box;
}

.activity_con .activity_show {
	float: left;
	width: 202px !important;
	padding: 0 0px 10px 0px!important;
	box-sizing: border-box;
	position: relative;
	height: 280px;
	margin: 0 51px 10px 0;
}

.activity_show .activity_list:hover {
	border-color: #ccc;
}

.ac_text .ac_border {
	border-left: 1px solid #E8E8E8;
	border-right: 0px;
}

.ac_text p {
	color: #444;
	padding-left: 5px;
	font-size: 15px;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	display: block;
	font-weight: bold;
}

.activity_list img:nth-child(1) {
	width: 100%;
	height: 202px;
	z-index: 5;
}

.ac_text {
	height: 65px;
	padding: 10px 0 0 0;
	box-sizing: border-box;
}


.activity_show .hide {
	width: 100%;
	display: none;
	position: absolute;
	top: 0;
	left: 0;
	border: 1px solid #ccc;
}

.activity_show .hide .hide-top {
	width: 100%;
	height: 202px;
	background: rgba(16, 16, 16, .8);
	position: relative;
}

.activity_con .activity_show:hover .hide {
	display: block;
}

.activity_con .activity_show .hide .hide-top ul {
	width: 100%;
	height: 50px;
	margin-top: 70px;
	position: absolute;
}

.activity_con .activity_show .hide .hide-top ul li {
	width: 50px;
	height: 50px;
	text-align: center;
	float: left;
	cursor: pointer;
}

.activity_con .activity_show .hide .hide-top ul li:nth-child(1) {
	background: #ccc;
	border-radius: 50%;
	margin-left: 33px;
	margin-right: 35px;
}

.activity_con .activity_show .hide .hide-top ul li:nth-child(2) {
	background: #ccc;
	border-radius: 50%;
}

.activity_con .activity_show .hide .hide-top ul li:nth-child(3) {
	margin-left: 33px;
	padding-right: 36px;
}

.activity_con .activity_show .hide .hide-top ul li a {
	color: #fff;
	font-size: 22px;
	padding-top: 5px;
	position: absolute;
	margin-left: -12px;
}

.activity_con .activity_show .hide .hide-top ul li:nth-child(1):hover {
	background: #fff;
}

.activity_con .activity_show .hide .hide-top ul li:nth-child(1):hover a {
	color: #4078CB;
	font-size: 28px;
}

.activity_con .activity_show .hide .hide-top ul li:nth-child(2):hover {
	background: #fff;
}

.activity_con .activity_show .hide .hide-top ul li:nth-child(2):hover a {
	color: #4078CB;
	font-size: 26px;
}

.activity_con .activity_show .hide .hide-top ul li p {
	color: #fff;
	font-size: 13px;
}

.activity_show .hide .hide-bottom {
	width: 100%;
	height: 65px;
	background: #fff;
	box-sizing: border-box;
}

.activity_show .hide .hide-bottom .option {
	width: 80%;
	height: 50px;
	margin-left: 10%;
	margin-top: 8px;
	position: absolute;
}

.activity_show .hide .hide-bottom .option .ag {
	width: 33.3333%;
	height: 50px;
	float: left;
	cursor: pointer;
}

.activity_show .hide .hide-bottom .option .ag li {
	float: left;
	width: 100%;
	height: 20px;
	padding-left: 14px;
}

.activity_show .hide .hide-bottom .option .ag li a {
	font-size: 24px;
	color: #999;
}

.activity_show .hide .hide-bottom .option .ag li span {
	color: #999;
}

.activity_show .hide .hide-bottom .option .ag:hover a {
	color: #4078CB;
	font-size: 28px;
}

.activity_show .hide .hide-bottom .option .ag:hover span {
	color: #4078CB;
	font-size: 16px;
}

.activity_show .hide .hide-bottom .option .ag:hover img {
	display: block;
}

.erweima {
	width: 202px;
	height: 202px;
	margin-top: -210px;
	position: absolute;
	margin-left: -20px;
	display: none;
}

.activity_show .hide .hide-top2 {
	width: 100%;
	height: 202px;
	background: rgba(16, 16, 16, .8);
	position: relative;
}

.activity_con .activity_show .hide .hide-top2 ul {
	width: 100%;
	/* height: 50px; */
	margin-top: 70px;
	position: absolute;
	display: none;
}

.activity_con .activity_show .hide .hide-top2 ul li {
	/* width: 50px;
	height: 50px; */
	text-align: center;
	float: left;
	cursor: pointer;
}

.activity_con .activity_show .hide .hide-top2 ul li:nth-child(1) {
	/* background: #ccc; */
	/* border-radius: 50%; */
	/* margin-left: 75px; */
	/* margin-right: 35px; */
	width: 100%;
}

.activity_con .activity_show .hide .hide-top2 ul li:nth-child(2) {
	margin-left: 75px;
}

.activity_con .activity_show .hide .hide-top2 ul li a {
	color: #fff;
	font-size: 22px;
	padding-top: 5px;
	position: absolute;
	margin-left: -12px;
}

.activity_con .activity_show .hide .hide-top2 ul li:nth-child(1):hover {
	/* background: #fff; */
}

.activity_con .activity_show .hide .hide-top2 ul li:nth-child(1):hover a {
	color: #4078CB;
	font-size: 28px;
}

.activity_con .activity_show .hide .hide-top2 ul li p {
	color: #fff;
	font-size: 13px;
}

.overviewSeat {
	width: 100px;
	line-height: 35px;
	border-radius: 18px;
	text-align: center;
	background: #fff;
	border: 1px solid #1890ff;
	margin: 0 auto;
}

.overviewSeat:hover {
	background: #1890ff;
	color: #fff;
}



/*弹窗*/
.black_overlay {
	display: none;
	position: fixed;
	top: 0%;
	left: 0%;
	width: 100%;
	height: 100%;
	background-color: black;
	z-index: 9998;
	-moz-opacity: 0.5;
	opacity: .5;
	filter: alpha(opacity=88);
	overflow: hidden;
}

.white_content {
	display: none;
	position: fixed;
	top: 50%;
	left: 50%;
	width: 440px;
	height: 200px;
	transform: translate(-50%, -50%);
	background-color: white;
	z-index: 9999;
}

.pop_xx {
	width: 17px;
	height: 17px;
	background: url(../images/pop_xx.jpg) no-repeat;
	background-size: contain;
	cursor: pointer;
	margin-top: 18px;
}

.pop_xx_fabu {
	width: 17px;
	height: 17px;
	background: url(../images/pop_xx.jpg) no-repeat;
	background-size: contain;
	cursor: pointer;
	margin-top: 18px;
}

.white_content .title-one {
	height: 49px;
	background-color: #f5f5f5;
	border-bottom: 1px solid #ddd;
	padding: 0 15px;
	line-height: 49px;
	font-size: 14px;
}

.white_content .title-two {
	width: 80%;
	height: 60px;
	margin-top: 25px;
	margin-left: 10%;
}

.white_content .title-two img {
	width: 40px;
	height: 40px;
	float: left;
	margin-left: 100px;
}

.white_content .title-two p {
	float: left;
	font-size: 14px;
	margin-left: 20px;
	color: #666;
}

.white_content .title-three {
	padding: 20px 0;
	text-align: center;
}

.white_content .title-three button {
	font-size: 12px;
	line-height: 29px;
}

.white_content .title-three .current {
	height: 30px;
	line-height: 30px;
	padding: 0 15px;
	background: #4079CB;
	border: 1px solid #4079CB;
	color: #fff;
}

.pop_btn1,
.pop_btn2 {
	height: 30px;
	line-height: 30px;
	padding: 0 15px;
	background: #F7F7F7;
	border: 1px solid #DDDDDD;
	cursor: pointer;
	margin-left: 10px;
}

/*发布弹窗*/
.white_content1 {
	display: none;
	position: fixed;
	top: 50%;
	left: 50%;
	width: 545px;
	height: 350px;
	transform: translate(-50%, -50%);
	background-color: white;
	z-index: 9999;
}

.white_content1 .title-one {
	height: 49px;
	background-color: #f5f5f5;
	border-bottom: 1px solid #ddd;
	padding: 0 15px;
	line-height: 49px;
	font-size: 14px;
}

.white_content1 .title-two {
	width: 100%;
	height: 70%;
	margin-top: 25px;
}

.white_content1 .title-two .title-two-left {
	width: 50%;
	height: 100%;
	margin-left: 3%;
	float: left;
}

.white_content1 .title-two .title-two-left .over .one {
	width: 100%;
	height: 40px;
}

.white_content1 .title-two .title-two-left .over .one li {
	float: left;
}

.white_content1 .title-two .title-two-left .over .one img {
	width: 30px;
	height: 30px;
}

.white_content1 .title-two .title-two-left .over .one p {
	color: #666;
	font-size: 13px;
	margin-left: 15px;
}

.white_content1 .title-two .title-two-left .over .two li {
	float: left;
	width: 100%;
	margin-bottom: 5px;
}

.white_content1 .title-two .title-two-left .over .two p {
	font-size: 13px;
}

.white_content1 .title-two .title-two-left .over .two input {
	width: 93%;
	height: 28px;
	border: 1px solid #ccc;
	background: #E3E3E3;
}

.white_content1 .title-two .title-two-left .over .two button {
	width: 129px;
	height: 28px;
	background: #fff;
	border: 1px solid #4078cb;
	color: #4078cb;
	cursor: pointer;
}

.white_content1 .title-two .title-two-left .over .two button a {
	color: #4078cb;
	font-size: 18px;
	margin-right: 5px;
}

.white_content1 .title-two .title-two-right {
	width: 44%;
	height: 100%;
	float: left;
	border: 1px solid #ccc;
}

.white_content1 .title-two .title-two-right img {
	margin-left: 44px;
	margin-top: 25px;
}

.white_content1 .title-two .title-two-right p {
	text-align: center;
	margin-top: 7px;
}

/*创建弹窗*/
.white_content2 {
	display: none;
	position: fixed;
	top: 50%;
	left: 50%;
	width: 465px;
	height: 250px;
	transform: translate(-50%, -50%);
	background-color: white;
	z-index: 9999;
}

.white_content2 .title-one {
	height: 49px;
	background-color: #f5f5f5;
	border-bottom: 1px solid #ddd;
	padding: 0 15px;
	line-height: 49px;
	font-size: 14px;
}

.white_content2 .title-two {
	margin-top: 25px;

}

.white_content2 .title-two .title-two-first {
	float: left;
	margin-left: 100px;
}

.white_content2 .title-two .title-two-first li {
	height: 30px;
	float: left;
}

.white_content2 .title-two .title-two-first li p {
	color: #666;
	font-size: 14px;
}

.white_content2 .title-two .title-two-first li select {
	width: 215px;
	height: 28px;
	padding-left: 5px;
	border: 1px solid #ccc;
}

.white_content2 .title-two .title-two-second {
	float: left;
	margin-left: 80px;
	margin-top: 25px;
}

.white_content2 .title-two .title-two-second li {
	height: 30px;
	float: left;
}

.white_content2 .title-two .title-two-second li p {
	color: #666;
	font-size: 14px;
}

.white_content2 .title-two .title-two-second li p span {
	color: red;
}

.white_content2 .title-two .title-two-second li input {
	width: 215px;
	height: 28px;
	padding-left: 5px;
	border: 1px solid #ccc;
}

.white_content2 .title-two .title-two-third {
	float: left;
	margin-left: 173px;
	width: 100%;
	color: red;
}

.white_content2 .title-three {
	padding: 20px 0;
	text-align: center;
	margin-top: 120px;
}

.white_content2 .title-three button {
	font-size: 12px;
	line-height: 29px;
}

.white_content2 .title-three .current {
	height: 30px;
	line-height: 30px;
	padding: 0 15px;
	background: #4079CB;
	border: 1px solid #4079CB;
	color: #fff;
}

.pop_btn1,
.pop_btn2 {
	height: 30px;
	line-height: 30px;
	padding: 0 15px;
	background: #F7F7F7;
	border: 1px solid #DDDDDD;
	cursor: pointer;
	margin-left: 10px;
}

.ac_text ul li {
	float: left;
	font-size: 12px;
	padding: 0 5px;
	height: 28px;
	line-height: 28px;
	color: #666666;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	width: 95%;
}




.main-ul {
	margin-top: 10px;
	overflow: hidden;
}

.main-ul li {
	float: left;
	width: 200px;
	height: 340px;
	padding: 8px;
	margin-right: 45px;
	border: 1px solid #ccc;
	position: relative;
	cursor: pointer;
	margin-bottom: 20px;
}

.ulImg {
	width: 100%;
	height: 305px;
	position: relative;
}

.micImg {
	width: 100%;
	height: 100%;
}

.Ptext1 {
	width: 100%;
	font-size: 15px;
	font-weight: bold;
	margin-top: 8px;
	height: 30px;
	line-height: 30px;
}

.Ptext2 {
	width: 100%;
	font-size: 15px;
	text-align: center;
	color: #fff;
	background: #4078cb;
	margin-top: 8px;
	height: 30px;
	line-height: 30px;
	display: none;
}

.ulImgMould {
	position: absolute;
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	background: rgba(0, 0, 0, 0.2);
	display: none;
}

.ulImgMbox {
	width: 150px;
	height: 162px;
	background: #fff;
	margin: 75px auto 0;
}

.ulCodeImg {
	width: 90%;
	margin-left: 5%;
}

.ulImgMbox p {
	width: 100%;
	background: #ccc;
	text-align: center;
	height: 27px;
	line-height: 27px;
	font-size: 12px;
}

.liSpan {
	display: inline-block;
	width: 20px;
	height: auto;
	line-height: 1.9;
	color: #ccc;
	font-size: 17px;
	margin-top: 55px;
}

.white_content_new {
	display: none;
	position: fixed;
	top: 50%;
	left: 50%;
	width: 440px;
	height: 250px;
	transform: translate(-50%, -50%);
	background-color: white;
	z-index: 9999;
}

.white_content_new .title-one {
	height: 40px;
	font-size: 12px;
	line-height: 40px;
	margin-left: 10px;
	background-color: #f5f5f5;
	border-bottom: 1px solid #ccc;
}

.white_content_new .pop_xx {
	width: 17px;
	height: 17px;
	background: url(../images/pop_xx.jpg) no-repeat;
	background-size: contain;
	cursor: pointer;
	margin: 10px;
}
}

.white_content_new .title-two {
	width: 100%;
	height: 155px;
}

.white_content_new .title-two .left_left {
	width: 40%;
	height: 160px;
	float: left;
}

.white_content_new .title-two .left_left img {
	width: 60px;
	height: 60px;
	margin-left: 70px;
	margin-top: 50px;
}

.white_content_new .title-two .right_right {
	/* width: 60%; */
	height: 160px;
	margin-left: 10%;
	float: left;
	/* font-size: 14px; */
}

.white_content_new .title-two .right_right li {
	width: 92%;
	float: left;
	line-height: 25px;
}

.white_content_new .title-three {
	width: 100%;
	height: 50px;
}

.white_content_new .title-three .btn2 {
	width: 80px;
	height: 30px;
	float: left;
	margin: 10px 10px;
	text-align: center;
	line-height: 30px;
	cursor: pointer;
}

.layui-icon {
	font-size: 24px!important;
	color: #666666;
}

.activity_show .hide .hide-bottom .option .ag:hover .layui-icon {
	color: #4078cb;
}

.hide-top2 .layui-icon {
	font-size: 30px;
	color: #666666;
	margin-top: 10px;
	display: inline-block;
}

.activity_con .activity_show .hide .hide-top2 ul li:nth-child(1):hover .layui-icon {
	color: #4078cb;
}
.new-content {
	padding-left:15px;
	box-sizing:border-box;
}
#queryForm {
	display:none;
}